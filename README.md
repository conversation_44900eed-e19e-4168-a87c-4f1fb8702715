# 🛡️ 智能漏洞检测系统

## 项目概述
基于AI大模型驱动的全自动化Web漏洞检测系统，支持SQL注入和SSRF漏洞检测。

## 🚀 核心功能

### 1. SQL注入检测
- 智能表单发现和参数识别
- 多种注入技术和绕过方法
- 实时浏览器自动化测试
- 详细的漏洞报告生成

### 2. SSRF检测 (新增)
- 🤖 AI驱动的nmap端口扫描策略生成
- 🎯 智能端口筛选（5个最重要端口）
- ⚡ 为每个端口生成2个专门载荷
- 🌐 生成10个通用SSRF载荷
- 🔍 大模型分析响应判断漏洞
- 📊 实时控制台输出完整攻击URL

## 📁 项目结构

```
├── web_app.py                    # 主Web应用入口
├── browser_ai_modified.py       # SQL注入检测引擎
├── ssrf_detection_system.py     # SSRF检测引擎
├── templates/                   # Web界面模板
├── static/                      # 静态资源
├── ssrf_target_package/         # SSRF测试靶场
│   ├── ssrf_visual_target.py   # 可视化靶场
│   └── README.md               # 靶场说明
├── hr_management_target/        # SQL注入测试靶场
└── online_shop_target/          # 在线商店测试靶场
```

## 🎯 使用方法

### 启动主系统
```bash
python web_app.py
```
访问: http://localhost:5000

### 选择检测类型
1. **SQL注入检测** - 选择相应的注入类型
2. **SSRF服务端请求伪造** - 选择SSRF检测

### 启动测试靶场

#### SSRF靶场
```bash
cd ssrf_target
python ssrf_visual_target.py
```
访问: http://localhost:5002

#### SQL注入靶场
```bash
cd hr_management_target
python app.py
```
访问: http://localhost:5001

## 🔧 SSRF检测特性

### AI智能化
- 自动生成3条最关键的nmap扫描策略
- 智能筛选5个最重要的攻击端口
- AI分析响应判断漏洞存在性

### 全面检测
- **端口扫描**: TCP/UDP多维度扫描
- **载荷类型**: 文件读取、协议攻击、内网探测、云服务元数据
- **检测参数**: url, link, callback, redirect, proxy, fetch, image等

### 实时输出
- 策略生成过程实时显示
- 完整攻击URL输出到控制台
- 检测进度和结果实时反馈

### 工具集成
- nmap端口扫描 (路径: D:\nmap\nmap.exe)
- 大模型API调用
- HTTP请求测试
- 响应分析引擎

## 📊 检测输出示例

```
🎯 完整攻击URL: http://target.com?url=file:///C:/Windows/System32/drivers/etc/hosts
🎯 完整攻击URL: http://target.com?callback=http://***************/latest/meta-data/

漏洞 1:
  类型: general_ssrf
  描述: 读取Windows hosts文件
  载荷: file:///C:/Windows/System32/drivers/etc/hosts
  严重程度: High
  完整攻击URL:
    - http://target.com?url=file:///C:/Windows/System32/drivers/etc/hosts
    - http://target.com?link=file:///C:/Windows/System32/drivers/etc/hosts
  使用工具: nmap端口扫描, AI载荷生成, HTTP请求测试
```

## ⚙️ 配置要求

### 环境依赖
- Python 3.7+
- nmap工具 (路径: D:\nmap\nmap.exe)
- 大模型API密钥 (可选，有备用响应)

### 环境变量
```bash
SILICONFLOW_API_KEY8264=your_api_key
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1
SILICONFLOW_MODEL=deepseek-ai/DeepSeek-V3
```

## 🛡️ 安全说明
- 本系统仅用于授权的安全测试
- 请勿用于非法攻击活动
- 测试靶场包含故意的安全漏洞，仅供学习使用

## 📈 更新日志
- ✅ 集成AI驱动的SSRF检测系统
- ✅ 优化nmap扫描策略，不保存文件直接输出
- ✅ 完整攻击URL实时输出到控制台
- ✅ 通用化设计，支持任意目标检测
- ✅ 可视化SSRF测试靶场
