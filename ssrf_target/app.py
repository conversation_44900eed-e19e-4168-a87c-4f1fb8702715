from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
import requests
import os
import socket
import time
from urllib.parse import urlparse
import sqlite3
import hashlib

app = Flask(__name__)
app.secret_key = 'ssrf_target_secret_key_2024'

# 初始化数据库
def init_db():
    conn = sqlite3.connect('internal_tools.db')
    cursor = conn.cursor()
    
    # 创建用户表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 创建系统配置表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS system_config (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            config_key TEXT UNIQUE NOT NULL,
            config_value TEXT NOT NULL,
            description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 创建监控日志表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS monitor_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            service_name TEXT NOT NULL,
            url TEXT NOT NULL,
            status TEXT NOT NULL,
            response_time REAL,
            checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 插入默认用户
    cursor.execute('SELECT COUNT(*) FROM users')
    if cursor.fetchone()[0] == 0:
        # 默认密码: admin123
        admin_password = hashlib.md5('admin123'.encode()).hexdigest()
        cursor.execute('INSERT INTO users (username, password, role) VALUES (?, ?, ?)', 
                      ('admin', admin_password, 'admin'))
        
        user_password = hashlib.md5('user123'.encode()).hexdigest()
        cursor.execute('INSERT INTO users (username, password, role) VALUES (?, ?, ?)', 
                      ('user', user_password, 'user'))
    
    # 插入默认配置
    cursor.execute('SELECT COUNT(*) FROM system_config')
    if cursor.fetchone()[0] == 0:
        configs = [
            ('api_endpoint', 'http://api.internal.com', 'Internal API endpoint'),
            ('backup_server', 'http://backup.internal.com', 'Backup server URL'),
            ('monitoring_interval', '300', 'Monitoring check interval in seconds'),
            ('notification_webhook', 'http://webhook.internal.com/notify', 'Notification webhook URL')
        ]
        cursor.executemany('INSERT INTO system_config (config_key, config_value, description) VALUES (?, ?, ?)', configs)
    
    conn.commit()
    conn.close()

# 初始化数据库
init_db()

@app.route('/')
def index():
    """主页 - 真实的企业内部系统"""
    return render_template('realistic_home.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = sqlite3.connect('internal_tools.db')
        cursor = conn.cursor()
        
        hashed_password = hashlib.md5(password.encode()).hexdigest()
        cursor.execute('SELECT id, username, role FROM users WHERE username = ? AND password = ?', 
                      (username, hashed_password))
        user = cursor.fetchone()
        conn.close()
        
        if user:
            session['user_id'] = user[0]
            session['username'] = user[1]
            session['role'] = user[2]
            flash('登录成功！', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误！', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    flash('已成功退出登录！', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # 获取系统统计信息
    conn = sqlite3.connect('internal_tools.db')
    cursor = conn.cursor()
    
    cursor.execute('SELECT COUNT(*) FROM monitor_logs WHERE DATE(checked_at) = DATE("now")')
    today_checks = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM monitor_logs WHERE status = "success" AND DATE(checked_at) = DATE("now")')
    success_checks = cursor.fetchone()[0]
    
    cursor.execute('SELECT service_name, url, status, response_time, checked_at FROM monitor_logs ORDER BY checked_at DESC LIMIT 10')
    recent_logs = cursor.fetchall()
    
    conn.close()
    
    return render_template('dashboard.html', 
                         username=session['username'],
                         role=session['role'],
                         today_checks=today_checks,
                         success_checks=success_checks,
                         recent_logs=recent_logs)

@app.route('/url-checker', methods=['GET', 'POST'])
def url_checker():
    """URL连通性检查工具 - 存在SSRF漏洞"""
    # 去掉登录检查，直接访问

    result = None

    # 支持GET和POST请求的SSRF
    target_url = None
    service_name = 'Manual Check'

    if request.method == 'POST':
        target_url = request.form.get('url', '').strip()
        service_name = request.form.get('service_name', 'Manual Check').strip()
    elif request.method == 'GET':
        # 支持GET参数进行SSRF测试
        target_url = request.args.get('url') or request.args.get('target') or request.args.get('link') or request.args.get('address')
        service_name = request.args.get('service_name', 'GET Request').strip()

    if target_url:
        
        if target_url:
            try:
                print(f"[SSRF] URL检查: {target_url}")
                start_time = time.time()
                
                # 故意不做任何过滤，存在SSRF漏洞
                # 支持多种协议和特殊响应
                if target_url.startswith('file://'):
                    # 模拟文件读取
                    file_path = target_url.replace('file://', '')
                    if 'hosts' in file_path:
                        fake_content = "127.0.0.1 localhost\n::1 localhost\n192.168.1.1 router.local\n10.0.0.1 internal.company.com"
                    elif 'win.ini' in file_path:
                        fake_content = "[fonts]\n[extensions]\n[mci extensions]\n[files]\n[Mail]\nMAPI=1"
                    else:
                        fake_content = "# Windows System File\n# Access Granted via SSRF\n[System Configuration]"

                    result = {
                        'url': target_url,
                        'status': 'success',
                        'status_code': 200,
                        'response_time': round(time.time() - start_time, 3),
                        'content_length': len(fake_content),
                        'headers': {'Content-Type': 'text/plain'},
                        'content_preview': fake_content
                    }

                elif '127.0.0.1:3306' in target_url or 'localhost:3306' in target_url:
                    # 模拟MySQL服务响应
                    mysql_banner = "5.7.44-MySQL Community Server - (GPL)\nHost: localhost  Database: information_schema\nUser: root@localhost"
                    result = {
                        'url': target_url,
                        'status': 'success',
                        'status_code': 200,
                        'response_time': round(time.time() - start_time, 3),
                        'content_length': len(mysql_banner),
                        'headers': {'Server': 'MySQL/5.7.44', 'Content-Type': 'text/plain'},
                        'content_preview': mysql_banner
                    }

                elif '127.0.0.1:6379' in target_url or 'localhost:6379' in target_url:
                    # 模拟Redis服务响应
                    redis_info = "# Server\nredis_version:7.0.0\nredis_git_sha1:00000000\nredis_mode:standalone\narch_bits:64\nprocess_id:1234"
                    result = {
                        'url': target_url,
                        'status': 'success',
                        'status_code': 200,
                        'response_time': round(time.time() - start_time, 3),
                        'content_length': len(redis_info),
                        'headers': {'Server': 'Redis/7.0.0', 'Content-Type': 'text/plain'},
                        'content_preview': redis_info
                    }

                elif '***************' in target_url:
                    # 模拟AWS元数据服务
                    if 'meta-data' in target_url:
                        metadata = "ami-id\nami-launch-index\nami-manifest-path\ninstance-id\ninstance-type\nlocal-hostname\nlocal-ipv4\npublic-hostname\npublic-ipv4"
                        if 'instance-id' in target_url:
                            metadata = "i-1234567890abcdef0"
                        elif 'ami-id' in target_url:
                            metadata = "ami-0abcdef1234567890"
                    else:
                        metadata = "latest\n1.0\n2007-01-19\n2007-03-01\n2007-08-29\n2007-10-10\n2007-12-15"

                    result = {
                        'url': target_url,
                        'status': 'success',
                        'status_code': 200,
                        'response_time': round(time.time() - start_time, 3),
                        'content_length': len(metadata),
                        'headers': {'Server': 'EC2ws', 'Content-Type': 'text/plain'},
                        'content_preview': metadata
                    }

                else:
                    # 正常HTTP请求
                    response = requests.get(target_url, timeout=10, allow_redirects=True)
                    response_time = time.time() - start_time

                    result = {
                        'url': target_url,
                        'status': 'success',
                        'status_code': response.status_code,
                        'response_time': round(response_time, 3),
                        'content_length': len(response.content),
                        'headers': dict(response.headers),
                        'content_preview': response.text[:500]
                    }
                
                # 记录到数据库
                conn = sqlite3.connect('internal_tools.db')
                cursor = conn.cursor()
                cursor.execute('INSERT INTO monitor_logs (service_name, url, status, response_time) VALUES (?, ?, ?, ?)',
                             (service_name, target_url, 'success', response_time))
                conn.commit()
                conn.close()
                
                flash(f'URL检查成功！状态码: {response.status_code}', 'success')
                
            except Exception as e:
                result = {
                    'url': target_url,
                    'status': 'error',
                    'error': str(e)
                }
                
                # 记录错误到数据库
                conn = sqlite3.connect('internal_tools.db')
                cursor = conn.cursor()
                cursor.execute('INSERT INTO monitor_logs (service_name, url, status, response_time) VALUES (?, ?, ?, ?)',
                             (service_name, target_url, 'error', 0))
                conn.commit()
                conn.close()
                
                flash(f'URL检查失败: {str(e)}', 'error')
    
    return render_template('url_checker.html',
                         username='guest',
                         role='user',
                         result=result)

@app.route('/webhook-tester', methods=['GET', 'POST'])
def webhook_tester():
    """Webhook测试工具 - 存在SSRF漏洞"""
    # 去掉登录检查，直接访问

    result = None
    webhook_url = None
    test_data = '{"test": "data"}'

    if request.method == 'POST':
        webhook_url = request.form.get('webhook_url', '').strip()
        test_data = request.form.get('test_data', '{"test": "data"}').strip()
    elif request.method == 'GET':
        webhook_url = request.args.get('webhook_url') or request.args.get('callback') or request.args.get('url')
        test_data = request.args.get('test_data', '{"test": "data"}').strip()

    if webhook_url:
        
        if webhook_url:
            try:
                print(f"[SSRF] Webhook测试: {webhook_url}")
                
                # 故意不做任何过滤，存在SSRF漏洞
                # 支持多种协议的Webhook测试
                if '127.0.0.1:6379' in webhook_url or 'localhost:6379' in webhook_url:
                    # 模拟Redis服务响应
                    redis_response = "+OK\r\n$6\r\nserver\r\n$5\r\n7.0.0\r\n"
                    result = {
                        'webhook_url': webhook_url,
                        'status': 'success',
                        'status_code': 200,
                        'response_headers': {'Server': 'Redis/7.0.0'},
                        'response_content': redis_response
                    }
                elif '127.0.0.1:3306' in webhook_url or 'localhost:3306' in webhook_url:
                    # 模拟MySQL服务响应
                    mysql_response = "MySQL Server version: 5.7.44\nConnection established"
                    result = {
                        'webhook_url': webhook_url,
                        'status': 'success',
                        'status_code': 200,
                        'response_headers': {'Server': 'MySQL/5.7.44'},
                        'response_content': mysql_response
                    }
                else:
                    # 正常HTTP请求
                    response = requests.post(webhook_url,
                                           data=test_data,
                                           headers={'Content-Type': 'application/json'},
                                           timeout=10)

                    result = {
                        'webhook_url': webhook_url,
                        'status': 'success',
                        'status_code': response.status_code,
                        'response_headers': dict(response.headers),
                        'response_content': response.text[:1000]
                    }
                
                flash(f'Webhook测试成功！状态码: {response.status_code}', 'success')
                
            except Exception as e:
                result = {
                    'webhook_url': webhook_url,
                    'status': 'error',
                    'error': str(e)
                }
                flash(f'Webhook测试失败: {str(e)}', 'error')
    
    return render_template('webhook_tester.html',
                         username='guest',
                         role='user',
                         result=result)

@app.route('/image-proxy', methods=['GET', 'POST'])
def image_proxy():
    """图片代理服务 - 存在SSRF漏洞"""
    # 去掉登录检查，直接访问

    result = None
    image_url = None

    if request.method == 'POST':
        image_url = request.form.get('image_url', '').strip()
    elif request.method == 'GET':
        image_url = request.args.get('image_url') or request.args.get('url') or request.args.get('src') or request.args.get('image')

    if image_url:
        
        if image_url:
            try:
                print(f"[SSRF] 图片代理: {image_url}")
                
                # 故意不做任何过滤，存在SSRF漏洞
                # 支持多种协议的图片代理
                if image_url.startswith('file://'):
                    # 模拟文件读取
                    file_path = image_url.replace('file://', '')
                    if 'hosts' in file_path:
                        fake_content = "127.0.0.1 localhost\n::1 localhost\n192.168.1.1 router.local"
                        content_type = 'text/plain'
                    elif any(ext in file_path for ext in ['.jpg', '.png', '.gif']):
                        fake_content = "FAKE_IMAGE_DATA_VIA_SSRF"
                        content_type = 'image/jpeg'
                    else:
                        fake_content = "# System File Access via SSRF\n[Configuration Data]"
                        content_type = 'text/plain'

                    result = {
                        'image_url': image_url,
                        'status': 'success',
                        'status_code': 200,
                        'content_type': content_type,
                        'content_length': len(fake_content),
                        'is_image': 'image' in content_type
                    }
                elif 'ftp://' in image_url:
                    # 模拟FTP协议响应
                    ftp_response = "220 FTP Server Ready\n230 User logged in\n150 Opening data connection"
                    result = {
                        'image_url': image_url,
                        'status': 'success',
                        'status_code': 200,
                        'content_type': 'text/plain',
                        'content_length': len(ftp_response),
                        'is_image': False
                    }
                else:
                    # 正常HTTP请求
                    response = requests.get(image_url, timeout=10)

                    result = {
                        'image_url': image_url,
                        'status': 'success',
                        'status_code': response.status_code,
                        'content_type': response.headers.get('Content-Type', 'unknown'),
                        'content_length': len(response.content),
                        'is_image': 'image' in response.headers.get('Content-Type', '').lower()
                    }
                
                flash(f'图片代理成功！文件大小: {len(response.content)} bytes', 'success')
                
            except Exception as e:
                result = {
                    'image_url': image_url,
                    'status': 'error',
                    'error': str(e)
                }
                flash(f'图片代理失败: {str(e)}', 'error')
    
    return render_template('image_proxy.html',
                         username='guest',
                         role='user',
                         result=result)

@app.route('/config-manager', methods=['GET', 'POST'])
def config_manager():
    """配置管理 - 存在SSRF漏洞"""
    # 去掉登录检查和权限检查，直接访问
    
    conn = sqlite3.connect('internal_tools.db')
    cursor = conn.cursor()
    
    # 支持GET请求的端点测试
    endpoint_url = request.args.get('endpoint_url') or request.args.get('url') or request.args.get('api') or request.args.get('service')
    if endpoint_url:
        try:
            print(f"[SSRF] 配置测试: {endpoint_url}")
            # 支持多种协议的端点测试
            if endpoint_url.startswith('file://'):
                flash('端点测试成功！检测到文件系统访问', 'success')
            elif '***************' in endpoint_url:
                flash('端点测试成功！检测到云服务元数据访问', 'success')
            elif any(port in endpoint_url for port in [':3306', ':6379', ':5432', ':1433']):
                flash('端点测试成功！检测到数据库服务连接', 'success')
            else:
                response = requests.get(endpoint_url, timeout=10)
                flash(f'端点测试成功！状态码: {response.status_code}', 'success')
        except Exception as e:
            flash(f'端点测试失败: {str(e)}', 'error')

    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'test_endpoint':
            endpoint_url = request.form.get('endpoint_url', '').strip()
            if endpoint_url:
                try:
                    print(f"[SSRF] 配置测试: {endpoint_url}")
                    
                    # 故意不做任何过滤，存在SSRF漏洞
                    # 支持多种协议的端点测试
                    if endpoint_url.startswith('file://'):
                        flash('端点测试成功！检测到文件系统访问', 'success')
                    elif '***************' in endpoint_url:
                        flash('端点测试成功！检测到云服务元数据访问', 'success')
                    elif any(port in endpoint_url for port in [':3306', ':6379', ':5432', ':1433']):
                        flash('端点测试成功！检测到数据库服务连接', 'success')
                    else:
                        response = requests.get(endpoint_url, timeout=10)
                        flash(f'端点测试成功！状态码: {response.status_code}', 'success')
                    
                except Exception as e:
                    flash(f'端点测试失败: {str(e)}', 'error')
        
        elif action == 'update_config':
            config_key = request.form.get('config_key')
            config_value = request.form.get('config_value')
            
            cursor.execute('UPDATE system_config SET config_value = ?, updated_at = CURRENT_TIMESTAMP WHERE config_key = ?',
                         (config_value, config_key))
            conn.commit()
            flash('配置更新成功！', 'success')
    
    cursor.execute('SELECT config_key, config_value, description FROM system_config ORDER BY config_key')
    configs = cursor.fetchall()
    conn.close()
    
    return render_template('config_manager.html',
                         username='admin',
                         role='admin',
                         configs=configs)

# 创建其他模板文件的路由处理...

if __name__ == '__main__':
    print("🏢 启动企业内部工具系统...")
    print("🌐 访问地址: http://localhost:5002")
    print("👤 默认账户:")
    print("   管理员 - 用户名: admin, 密码: admin123")
    print("   普通用户 - 用户名: user, 密码: user123")
    print("⚠️  安全警告: 此系统存在SSRF漏洞，仅用于安全测试！")
    print("="*60)
    
    app.run(host='0.0.0.0', port=5002, debug=True)
