<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置管理 - 企业内部工具系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f6fa;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 24px;
        }
        
        .nav-links {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .tool-header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .tool-header h2 {
            color: #2f3542;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .tool-header p {
            color: #57606f;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .test-endpoint {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .test-endpoint h3 {
            color: #2f3542;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2f3542;
            font-weight: 600;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .config-table {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .config-table h3 {
            color: #2f3542;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .table th {
            background: #f8f9fa;
            color: #2f3542;
            font-weight: 600;
        }
        
        .table td {
            color: #57606f;
        }
        
        .config-key {
            font-family: 'Courier New', monospace;
            background: #e7f3ff;
            padding: 4px 8px;
            border-radius: 4px;
            color: #004085;
        }
        
        .config-value {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            max-width: 300px;
            word-break: break-all;
        }
        
        .config-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-small {
            background: #667eea;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-small:hover {
            background: #5a67d8;
        }
        
        .btn-test {
            background: #28a745;
        }
        
        .btn-test:hover {
            background: #218838;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .examples {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .examples h4 {
            color: #004085;
            margin-bottom: 15px;
        }
        
        .example-item {
            background: white;
            padding: 10px 15px;
            border-radius: 6px;
            margin-bottom: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #2f3542;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .example-item:hover {
            background: #f0f8ff;
            transform: translateX(5px);
        }
        
        .example-item:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>⚙️ 系统配置管理</h1>
            <div class="nav-links">
                <a href="{{ url_for('dashboard') }}">🏠 仪表板</a>
                <a href="{{ url_for('logout') }}">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="tool-header">
            <h2>🔧 系统配置管理</h2>
            <p>管理系统配置参数，测试API端点连接性。仅管理员可访问此功能。</p>
        </div>
        
        <div class="test-endpoint">
            <h3>🌐 端点连接测试</h3>
            <form method="POST">
                <input type="hidden" name="action" value="test_endpoint">
                <div class="form-group">
                    <label for="endpoint_url">测试端点URL</label>
                    <input type="url" id="endpoint_url" name="endpoint_url" placeholder="http://api.internal.com/health" required>
                </div>
                <button type="submit" class="submit-btn">🚀 测试连接</button>
            </form>
            
            <div class="examples">
                <h4>💡 测试示例</h4>
                <div class="example-item" onclick="fillEndpoint('http://127.0.0.1:3306')">http://127.0.0.1:3306 - 本地MySQL</div>
                <div class="example-item" onclick="fillEndpoint('http://192.168.1.1/api/status')">http://192.168.1.1/api/status - 内网API</div>
                <div class="example-item" onclick="fillEndpoint('file:///C:/Windows/win.ini')">file:///C:/Windows/win.ini - 系统文件</div>
                <div class="example-item" onclick="fillEndpoint('http://169.254.169.254/latest/meta-data/')">http://169.254.169.254/latest/meta-data/ - 云元数据</div>
            </div>
        </div>
        
        <div class="config-table">
            <h3>📋 系统配置列表</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>配置键</th>
                        <th>配置值</th>
                        <th>描述</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for config in configs %}
                    <tr>
                        <td><span class="config-key">{{ config[0] }}</span></td>
                        <td><span class="config-value">{{ config[1] }}</span></td>
                        <td>{{ config[2] }}</td>
                        <td>
                            <div class="config-actions">
                                <button class="btn-small btn-test" onclick="testConfig('{{ config[1] }}')">测试</button>
                                <button class="btn-small" onclick="editConfig('{{ config[0] }}', '{{ config[1] }}')">编辑</button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 编辑配置模态框 -->
    <div id="editModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 15px; width: 90%; max-width: 500px;">
            <h3 style="margin-bottom: 20px; color: #2f3542;">编辑配置</h3>
            <form method="POST" id="editForm">
                <input type="hidden" name="action" value="update_config">
                <input type="hidden" name="config_key" id="editKey">
                <div class="form-group">
                    <label for="editValue">配置值</label>
                    <input type="text" id="editValue" name="config_value" required>
                </div>
                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" onclick="closeModal()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">取消</button>
                    <button type="submit" class="submit-btn">保存</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        function fillEndpoint(url) {
            document.getElementById('endpoint_url').value = url;
        }
        
        function testConfig(url) {
            document.getElementById('endpoint_url').value = url;
            document.getElementById('endpoint_url').scrollIntoView();
        }
        
        function editConfig(key, value) {
            document.getElementById('editKey').value = key;
            document.getElementById('editValue').value = value;
            document.getElementById('editModal').style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('editModal').style.display = 'none';
        }
        
        // 点击模态框外部关闭
        document.getElementById('editModal').onclick = function(e) {
            if (e.target === this) {
                closeModal();
            }
        }
    </script>
</body>
</html>
