<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业内部管理系统 - TechCorp Solutions</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .logo h1 {
            color: #2c3e50;
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .nav-menu {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-menu a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-menu a:hover {
            color: #667eea;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 3rem 2rem;
        }
        
        .hero {
            text-align: center;
            color: white;
            margin-bottom: 4rem;
        }
        
        .hero h2 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 300;
        }
        
        .hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .service-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .service-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            color: white;
        }
        
        .service-card h3 {
            color: #2c3e50;
            font-size: 1.3rem;
            margin-bottom: 1rem;
        }
        
        .service-card p {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .service-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: transform 0.3s ease;
        }
        
        .service-link:hover {
            transform: scale(1.05);
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            color: white;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            opacity: 0.9;
            font-size: 1rem;
        }
        
        .footer {
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">TC</div>
                <h1>TechCorp Solutions</h1>
            </div>
            <nav class="nav-menu">
                <a href="/">首页</a>
                <a href="/about">关于我们</a>
                <a href="/services">服务</a>
                <a href="/contact">联系</a>
            </nav>
        </div>
    </div>
    
    <div class="container">
        <div class="hero">
            <h2>企业内部管理系统</h2>
            <p>为您的企业提供全方位的数字化解决方案，提升工作效率，优化业务流程</p>
        </div>
        
        <div class="services-grid">
            <div class="service-card">
                <div class="service-icon">🔗</div>
                <h3>URL连通性检查</h3>
                <p>实时监控内外部服务状态，确保业务连续性。支持批量检测和自动报警功能。</p>
                <a href="/url-checker" class="service-link">立即使用</a>
            </div>
            
            <div class="service-card">
                <div class="service-icon">🔔</div>
                <h3>Webhook集成服务</h3>
                <p>强大的Webhook测试和管理工具，支持多种回调协议，简化系统集成流程。</p>
                <a href="/webhook-tester" class="service-link">立即使用</a>
            </div>
            
            <div class="service-card">
                <div class="service-icon">🖼️</div>
                <h3>图片代理服务</h3>
                <p>高效的图片处理和代理服务，支持多种格式转换，优化加载速度。</p>
                <a href="/image-proxy" class="service-link">立即使用</a>
            </div>
            
            <div class="service-card">
                <div class="service-icon">⚙️</div>
                <h3>系统配置管理</h3>
                <p>集中化的配置管理平台，支持多环境部署，确保系统稳定运行。</p>
                <a href="/config-manager" class="service-link">立即使用</a>
            </div>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">99.9%</div>
                <div class="stat-label">系统可用性</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">1000+</div>
                <div class="stat-label">企业客户</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">24/7</div>
                <div class="stat-label">技术支持</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">5年+</div>
                <div class="stat-label">行业经验</div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>&copy; 2024 TechCorp Solutions. 保留所有权利。</p>
    </div>
</body>
</html>
