<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL连通性检查 - 企业内部工具系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f6fa;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 24px;
        }
        
        .nav-links {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .tool-header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .tool-header h2 {
            color: #2f3542;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .tool-header p {
            color: #57606f;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2f3542;
            font-weight: 600;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .result-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .result-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .result-header h3 {
            color: #2f3542;
            font-size: 20px;
        }
        
        .result-success {
            color: #28a745;
        }
        
        .result-error {
            color: #dc3545;
        }
        
        .result-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
        }
        
        .result-item:last-child {
            margin-bottom: 0;
        }
        
        .result-item strong {
            color: #2f3542;
            display: block;
            margin-bottom: 5px;
        }
        
        .result-item span {
            color: #57606f;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .content-preview {
            background: #2f3542;
            color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .examples {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .examples h4 {
            color: #004085;
            margin-bottom: 15px;
        }
        
        .example-item {
            background: white;
            padding: 10px 15px;
            border-radius: 6px;
            margin-bottom: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #2f3542;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .example-item:hover {
            background: #f0f8ff;
            transform: translateX(5px);
        }
        
        .example-item:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🌐 URL连通性检查</h1>
            <div class="nav-links">
                <a href="{{ url_for('dashboard') }}">🏠 仪表板</a>
                <a href="{{ url_for('logout') }}">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="tool-header">
            <h2>🔍 URL连通性检查工具</h2>
            <p>检查内部和外部URL的连通性状态，监控服务可用性。支持HTTP/HTTPS协议，可用于验证API端点、Web服务等的可访问性。</p>
        </div>
        
        <div class="form-container">
            <form method="POST">
                <div class="form-group">
                    <label for="service_name">服务名称</label>
                    <input type="text" id="service_name" name="service_name" placeholder="例如: 内部API服务" value="Manual Check">
                </div>
                
                <div class="form-group">
                    <label for="url">目标URL</label>
                    <input type="url" id="url" name="url" placeholder="https://example.com" required>
                </div>
                
                <button type="submit" class="submit-btn">🚀 开始检查</button>
            </form>
            
            <div class="examples">
                <h4>💡 测试示例</h4>
                <div class="example-item" onclick="fillUrl('http://127.0.0.1:3306')">http://127.0.0.1:3306 - 本地MySQL服务</div>
                <div class="example-item" onclick="fillUrl('http://***********')">http://*********** - 内网路由器</div>
                <div class="example-item" onclick="fillUrl('file:///C:/Windows/System32/drivers/etc/hosts')">file:///C:/Windows/System32/drivers/etc/hosts - 系统文件</div>
                <div class="example-item" onclick="fillUrl('http://***************/latest/meta-data/')">http://***************/latest/meta-data/ - AWS元数据</div>
                <div class="example-item" onclick="fillUrl('gopher://127.0.0.1:6379/_*1%0d%0a$4%0d%0ainfo%0d%0a')">gopher://127.0.0.1:6379/... - Gopher协议</div>
            </div>
        </div>
        
        {% if result %}
        <div class="result-container">
            <div class="result-header">
                <h3>📊 检查结果</h3>
                {% if result.status == 'success' %}
                    <span class="result-success">✅ 成功</span>
                {% else %}
                    <span class="result-error">❌ 失败</span>
                {% endif %}
            </div>
            
            {% if result.status == 'success' %}
                <div class="result-item">
                    <strong>目标URL:</strong>
                    <span>{{ result.url }}</span>
                </div>
                
                <div class="result-item">
                    <strong>HTTP状态码:</strong>
                    <span>{{ result.status_code }}</span>
                </div>
                
                <div class="result-item">
                    <strong>响应时间:</strong>
                    <span>{{ result.response_time }}秒</span>
                </div>
                
                <div class="result-item">
                    <strong>内容长度:</strong>
                    <span>{{ result.content_length }} bytes</span>
                </div>
                
                <div class="result-item">
                    <strong>响应头信息:</strong>
                    <div class="content-preview">{{ result.headers | tojson(indent=2) }}</div>
                </div>
                
                <div class="result-item">
                    <strong>响应内容预览:</strong>
                    <div class="content-preview">{{ result.content_preview }}</div>
                </div>
            {% else %}
                <div class="result-item">
                    <strong>目标URL:</strong>
                    <span>{{ result.url }}</span>
                </div>
                
                <div class="result-item">
                    <strong>错误信息:</strong>
                    <span style="color: #dc3545;">{{ result.error }}</span>
                </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
    
    <script>
        function fillUrl(url) {
            document.getElementById('url').value = url;
        }
    </script>
</body>
</html>
