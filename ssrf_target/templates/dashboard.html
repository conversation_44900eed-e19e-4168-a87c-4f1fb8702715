<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板 - 企业内部工具系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f6fa;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 24px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-info span {
            background: rgba(255,255,255,0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #2f3542;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #57606f;
            font-size: 14px;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .tool-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .tool-card h3 {
            color: #2f3542;
            margin-bottom: 15px;
            font-size: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .tool-card p {
            color: #57606f;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .tool-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-block;
        }
        
        .tool-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .recent-logs {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .recent-logs h3 {
            color: #2f3542;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .log-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .log-table th,
        .log-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .log-table th {
            background: #f8f9fa;
            color: #2f3542;
            font-weight: 600;
        }
        
        .log-table td {
            color: #57606f;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #cce7ff;
            color: #004085;
            border: 1px solid #b3d9ff;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🏢 企业内部工具系统</h1>
            <div class="user-info">
                <span>👤 {{ username }}</span>
                <span>🔑 {{ role }}</span>
                <a href="{{ url_for('logout') }}" class="logout-btn">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>📊 今日检查</h3>
                <div class="stat-number">{{ today_checks }}</div>
                <div class="stat-label">次数</div>
            </div>
            <div class="stat-card">
                <h3>✅ 成功检查</h3>
                <div class="stat-number">{{ success_checks }}</div>
                <div class="stat-label">次数</div>
            </div>
            <div class="stat-card">
                <h3>📈 成功率</h3>
                <div class="stat-number">{% if today_checks > 0 %}{{ "%.1f"|format((success_checks / today_checks) * 100) }}%{% else %}0%{% endif %}</div>
                <div class="stat-label">百分比</div>
            </div>
        </div>
        
        <div class="tools-grid">
            <div class="tool-card">
                <h3>🌐 URL连通性检查</h3>
                <p>检查内部和外部URL的连通性状态，监控服务可用性。支持HTTP/HTTPS协议。</p>
                <a href="{{ url_for('url_checker') }}" class="tool-btn">开始检查</a>
            </div>
            
            <div class="tool-card">
                <h3>📞 Webhook测试工具</h3>
                <p>测试Webhook端点的响应性能，验证回调接口的正确性。</p>
                <a href="{{ url_for('webhook_tester') }}" class="tool-btn">测试Webhook</a>
            </div>
            
            <div class="tool-card">
                <h3>🖼️ 图片代理服务</h3>
                <p>代理获取外部图片资源，用于内网环境下的图片访问。</p>
                <a href="{{ url_for('image_proxy') }}" class="tool-btn">代理图片</a>
            </div>
            
            {% if role == 'admin' %}
            <div class="tool-card">
                <h3>⚙️ 系统配置管理</h3>
                <p>管理系统配置参数，测试API端点连接性。仅管理员可访问。</p>
                <a href="{{ url_for('config_manager') }}" class="tool-btn">配置管理</a>
            </div>
            {% endif %}
        </div>
        
        <div class="recent-logs">
            <h3>📋 最近检查记录</h3>
            {% if recent_logs %}
            <table class="log-table">
                <thead>
                    <tr>
                        <th>服务名称</th>
                        <th>URL</th>
                        <th>状态</th>
                        <th>响应时间</th>
                        <th>检查时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in recent_logs %}
                    <tr>
                        <td>{{ log[0] }}</td>
                        <td>{{ log[1][:50] }}{% if log[1]|length > 50 %}...{% endif %}</td>
                        <td>
                            <span class="status-{{ log[2] }}">
                                {% if log[2] == 'success' %}✅ 成功{% else %}❌ 失败{% endif %}
                            </span>
                        </td>
                        <td>{% if log[3] %}{{ "%.3f"|format(log[3]) }}s{% else %}-{% endif %}</td>
                        <td>{{ log[4] }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p style="text-align: center; color: #57606f; padding: 20px;">暂无检查记录</p>
            {% endif %}
        </div>
    </div>
</body>
</html>
