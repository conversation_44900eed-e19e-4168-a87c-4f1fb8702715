# 🏢 企业内部工具系统 - SSRF测试靶场

## 概述
这是一个模拟真实企业内部工具系统的SSRF测试靶场，提供完整的用户认证和多种业务功能，每个功能都故意存在SSRF漏洞。

## 🎯 系统特性

### 🔐 用户认证系统
- 完整的登录/登出功能
- 角色权限管理（管理员/普通用户）
- 会话管理和安全控制

### 🛠️ 业务功能模块
1. **URL连通性检查** - 监控内外部服务状态
2. **Webhook测试工具** - 验证回调接口响应
3. **图片代理服务** - 代理获取外部图片资源
4. **系统配置管理** - 管理API端点和系统参数（仅管理员）

### 📊 数据管理
- SQLite数据库存储
- 用户信息管理
- 系统配置存储
- 监控日志记录

### 🎨 界面设计
- 现代化响应式设计
- 专业的企业级UI风格
- 实时状态反馈
- 友好的用户体验

## 🚀 使用方法

### 启动系统
```bash
cd ssrf_target
python app.py
```

### 访问地址
```
http://localhost:5002
```

### 默认账户
- **管理员**: 用户名 `admin`, 密码 `admin123`
- **普通用户**: 用户名 `user`, 密码 `user123`

## 🎯 SSRF测试点

### URL连通性检查
- 参数: `url`, `service_name`
- 测试载荷:
  - `http://127.0.0.1:3306` - 本地MySQL
  - `file:///C:/Windows/System32/drivers/etc/hosts` - 文件读取
  - `http://***************/latest/meta-data/` - 云元数据

### Webhook测试工具
- 参数: `webhook_url`, `test_data`
- 测试载荷:
  - `http://127.0.0.1:6379` - Redis服务
  - `http://*************:3000/api/callback` - 内网API

### 图片代理服务
- 参数: `image_url`
- 测试载荷:
  - `file:///C:/Windows/win.ini` - 系统文件
  - `ftp://127.0.0.1/image.jpg` - FTP协议

### 配置管理（管理员）
- 参数: `endpoint_url`
- 测试载荷:
  - `gopher://127.0.0.1:6379/_*1%0d%0a$4%0d%0ainfo%0d%0a` - Gopher协议
  - `http://***************/metadata/instance` - Azure元数据

## 🔧 技术架构

### 后端技术
- Flask Web框架
- SQLite数据库
- requests HTTP库
- 会话管理

### 前端技术
- 响应式HTML5/CSS3
- 现代化UI设计
- JavaScript交互
- 实时数据更新

### 安全特性（故意缺失）
- ❌ 无URL白名单验证
- ❌ 无协议限制
- ❌ 无内网IP过滤
- ❌ 无请求频率限制

## ⚠️ 安全警告
**重要**: 这是一个故意存在SSRF漏洞的测试环境，包含以下安全风险：
- 可访问内网服务
- 可读取本地文件
- 可探测内网结构
- 可访问云服务元数据

仅用于授权的安全测试和教育目的！

## 📁 文件结构
```
ssrf_target_package/
├── app.py                    # 主应用程序
├── templates/                # HTML模板
│   ├── login.html           # 登录页面
│   ├── dashboard.html       # 仪表板
│   ├── url_checker.html     # URL检查工具
│   ├── webhook_tester.html  # Webhook测试
│   ├── image_proxy.html     # 图片代理
│   └── config_manager.html  # 配置管理
├── internal_tools.db        # SQLite数据库（自动创建）
└── README.md               # 说明文档
```

## 🔍 检测建议
使用此靶场测试SSRF检测工具时，建议关注：
1. 参数发现能力
2. 协议支持范围
3. 内网探测效果
4. 文件读取检测
5. 云服务元数据访问
