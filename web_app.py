from flask import Flask, render_template, request, jsonify
import subprocess
import threading
import os
import sys
import time
from datetime import datetime
import logging

logging.getLogger('werkzeug').disabled = True

app = Flask(__name__)

# 添加CORS支持
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

# 全局变量存储检测结果
detection_result = ""
is_running = False
detection_process = None
start_time = None
stop_requested = False
vulnerability_details = []  # 存储漏洞详情

def parse_vulnerability_details(output_text):
    """解析检测输出中的漏洞详情"""
    global vulnerability_details
    vulnerability_details = []

    lines = output_text.split('\n')
    current_vuln = {}

    for line in lines:
        line = line.strip()

        # 检测到漏洞标记 - 支持多种格式
        if ('🚨 存在漏洞' in line or '🔒 安全' in line or
            '🚨 发现严重SQL注入漏洞' in line or '🟢 未发现SQL注入漏洞' in line or
            '🔴 发现严重SQL注入漏洞' in line or '🔴 发现SQL注入漏洞' in line):
            if current_vuln:
                vulnerability_details.append(current_vuln)

            # 判断漏洞类型
            vuln_type = '安全'  # 默认安全
            if ('🚨 存在漏洞' in line or '🚨 发现' in line or '🔴 发现' in line):
                vuln_type = '存在漏洞'
            elif ('🔒 安全' in line or '🟢 未发现' in line):
                vuln_type = '安全'

            current_vuln = {
                'type': vuln_type,
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'payload': '',
                'status_code': '',
                'description': ''
            }

        # 提取测试用例（payload）
        elif '测试用例:' in line:
            if current_vuln:
                current_vuln['payload'] = line.replace('测试用例:', '').strip()

        # 提取状态码
        elif '状态码:' in line:
            if current_vuln:
                current_vuln['status_code'] = line.replace('状态码:', '').strip()

        # 提取AI分析描述
        elif '发现的安全问题:' in line:
            if current_vuln:
                current_vuln['description'] = line.replace('发现的安全问题:', '').strip()

    # 添加最后一个漏洞
    if current_vuln:
        vulnerability_details.append(current_vuln)

    return vulnerability_details

def run_detection(target_url, vulnerability_types):
    """运行检测"""
    global detection_result, is_running, detection_process, start_time, stop_requested
    is_running = True
    stop_requested = False
    start_time = time.time()
    vuln_types_str = ", ".join(vulnerability_types)
    detection_result = f"🎯 正在检测目标: {target_url}\n📋 检测类型: {vuln_types_str}\n⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n请等待检测完成...\n\n"

    try:
        # 根据漏洞类型选择不同的检测脚本
        if 'ssrf' in vulnerability_types:
            # 使用完美的AI驱动SSRF检测系统
            cmd = [sys.executable, 'perfect_ssrf_detector.py', target_url]
        else:
            # 使用SQL注入检测脚本
            cmd = [sys.executable, 'browser_ai_modified.py', target_url] + vulnerability_types
        print(f"🎯 启动检测进程: {' '.join(cmd)}")
        detection_process = subprocess.Popen(cmd,
           stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
           text=True, encoding='utf-8', bufsize=1, universal_newlines=True)

        # 收集所有输出，添加35分钟超时保护
        all_output = ""
        capture_result = False
        result_content = ""
        web_timeout = 35 * 60  # 35分钟 = 2100秒
        start_time_process = time.time()

        # 实时读取输出，带超时检查
        while True:
            # 检查是否请求停止
            if stop_requested:
                print("❌ 收到停止请求，正在终止检测...")
                detection_result += "\n❌ 检测已被用户停止\n"
                if detection_process.poll() is None:
                    detection_process.terminate()
                    try:
                        detection_process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        detection_process.kill()
                break

            # 检查Web层超时（35分钟）
            if time.time() - start_time_process > web_timeout:
                print("⏰ Web层检测超时（35分钟），强制终止进程")
                detection_result += "\n⏰ 检测超时：35分钟内未完成，已强制终止"
                if detection_process.poll() is None:
                    detection_process.terminate()
                    try:
                        detection_process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        detection_process.kill()
                break

            # 检查进程是否还在运行
            if detection_process.poll() is not None:
                # 进程已结束，读取剩余输出
                remaining_output = detection_process.stdout.read()
                if remaining_output:
                    all_output += remaining_output
                    print(f"{remaining_output.strip()}")
                break

            # 读取一行输出
            try:
                line = detection_process.stdout.readline()
                if line:
                    if line.strip():
                        all_output += line
                        # 实时输出到控制台
                        print(f"{line.strip()}")

                        # 检测到结果开始标记
                        if "检测结果:" in line or "综合安全检测报告" in line or "最终检测报告" in line:
                            capture_result = True
                            detection_result = f"🎯 目标: {target_url}\n📋 检测类型: {', '.join(vulnerability_types)}\n⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                            print(vulnerability_types)
                            if 'ssrf' in ', '.join(vulnerability_types):
                                detection_result += "🛠️ 调用工具: nmap , Burpsuite专业版\n\n"
                                print("🛠️ 调用工具: nmap.exe , Burpsuite专业版\n\n")
                            elif 'sql_injection' in ', '.join(vulnerability_types) :
                                detection_result += "🛠️ 调用工具: browser use\n\n"
                                print("🛠️ 调用工具: browser_use\n\n")
                            else :
                                detection_result += "🛠️ 调用工具: 无\n\n"
                                print("🛠️ 调用工具: 无\n\n")
                            print(f"[检测完成] 开始捕获结果")
                            continue

                        # 如果在结果区域，收集结果内容
                        if capture_result:
                            # 跳过分隔线
                            if "=" in line and len(line.strip()) > 10:
                                continue
                            # 收集实际结果内容
                            if line.strip():
                                result_content += line
                else:
                    time.sleep(10)  # 减少日志输出频率
            except Exception as e:
                print(f"读取输出时出错: {e}")
                break

        # 将收集到的结果添加到最终输出
        if result_content.strip():
            detection_result += result_content
            # 解析漏洞详情
            parse_vulnerability_details(all_output)
        else:
            # 如果没有捕获到结果，显示完整输出的最后部分
            lines = all_output.split('\n')
            relevant_lines = []
            found_result = False
            for line in lines:
                if "检测结果:" in line or found_result:
                    found_result = True
                    relevant_lines.append(line)

            if relevant_lines:
                detection_result += '\n'.join(relevant_lines[-20:])  # 显示最后20行
                # 解析漏洞详情
                parse_vulnerability_details('\n'.join(relevant_lines))
            else:
                detection_result += "检测过程已完成，但未捕获到明确的结果输出。"

        # 等待进程完成
        detection_process.wait()

        # 计算总耗时
        if start_time:
            total_time = time.time() - start_time
            minutes = int(total_time // 60)
            seconds = int(total_time % 60)
            detection_result += f"\n检测总耗时: {minutes}分{seconds}秒\n"

        if detection_process.returncode != 0:
            detection_result += f"\n检测异常结束，返回码: {detection_process.returncode}\n"

    except Exception as e:
        detection_result += f"\n运行出错: {str(e)}\n"

    finally:
        # 确保进程被正确终止
        if detection_process and detection_process.poll() is None:
            try:
                detection_process.terminate()
                detection_process.wait(timeout=5)
                print("🔒 检测进程已正常终止")
            except:
                try:
                    detection_process.kill()
                    print("💀 检测进程已强制终止")
                except:
                    pass
        is_running = False
        detection_process = None

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/start_detection', methods=['POST'])
def start_detection():
    global detection_result, is_running

    if is_running:
        return jsonify({'success': False, 'message': '检测正在进行中'})

    data = request.get_json()
    target_url = data.get('target_url', '').strip()
    vulnerability_types = data.get('vulnerability_types', [])

    if not target_url:
        return jsonify({'success': False, 'message': '请输入目标URL'})

    if not vulnerability_types:
        return jsonify({'success': False, 'message': '请选择至少一种漏洞类型'})

    # 重置结果
    detection_result = ""

    # 启动检测线程
    thread = threading.Thread(target=run_detection, args=(target_url, vulnerability_types))
    thread.daemon = True
    thread.start()

    return jsonify({'success': True, 'message': f'检测已启动，类型: {", ".join(vulnerability_types)}'})

@app.route('/stop_detection', methods=['POST'])
def stop_detection():
    """停止正在运行的检测"""
    global detection_process, is_running, detection_result

    if not is_running:
        return jsonify({'success': False, 'message': '没有正在运行的检测'})

    try:
        if detection_process and detection_process.poll() is None:
            detection_process.terminate()
            detection_process.wait(timeout=5)
            detection_result += "\n🛑 检测被用户手动停止"
            print("🛑 用户手动停止了检测")

        is_running = False
        detection_process = None
        return jsonify({'success': True, 'message': '检测已停止'})

    except Exception as e:
        print(f"停止检测时出错: {e}")
        return jsonify({'success': False, 'message': f'停止检测失败: {str(e)}'})

@app.route('/clear_result', methods=['POST'])
def clear_result():
    """清空检测结果"""
    global detection_result, is_running

    if is_running:
        return jsonify({'success': False, 'message': '检测正在进行中，无法清空结果'})

    detection_result = ""
    return jsonify({'success': True, 'message': '结果已清空'})

@app.route('/get_result')
def get_result():
    # 计算当前运行时间
    current_time = ""
    if is_running and start_time:
        elapsed = time.time() - start_time
        minutes = int(elapsed // 60)
        seconds = int(elapsed % 60)
        current_time = f"{minutes}分{seconds}秒"

    return jsonify({
        'result': detection_result,
        'is_running': is_running,
        'current_time': current_time,
        'vulnerabilities': vulnerability_details
    })

if __name__ == '__main__':
    print("启动渗透测试智能化检测Web界面...")
    print("访问地址: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
