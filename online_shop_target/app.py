from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
import sqlite3
import os
from datetime import datetime
import hashlib

app = Flask(__name__, template_folder='templates', static_folder='static')
app.secret_key = 'online_shop_2024_secret_key_xiaofeifetech'

def init_database():
    """初始化在线商城数据库和测试数据"""
    # 确保数据库文件在当前目录
    db_path = os.path.join(os.path.dirname(__file__), 'online_shop_system.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 删除旧的表结构（如果存在）
    cursor.execute('DROP TABLE IF EXISTS information_schema_tables')
    cursor.execute('DROP TABLE IF EXISTS information_schema_columns')
    cursor.execute('DROP TABLE IF EXISTS information_schema_schemata')
    cursor.execute('DROP TABLE IF EXISTS information_schema_key_column_usage')
    cursor.execute('DROP TABLE IF EXISTS information_schema_table_constraints')
    cursor.execute('DROP TABLE IF EXISTS information_schema_statistics')
    cursor.execute('DROP TABLE IF EXISTS information_schema_views')
    cursor.execute('DROP TABLE IF EXISTS information_schema_routines')
    cursor.execute('DROP TABLE IF EXISTS information_schema_triggers')
    cursor.execute('DROP TABLE IF EXISTS information_schema_processlist')
    
    # 创建完整的information_schema结构（模拟MySQL）
    
    # 1. INFORMATION_SCHEMA.TABLES - 表信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_tables (
            table_catalog TEXT DEFAULT 'def',
            table_schema TEXT,
            table_name TEXT,
            table_type TEXT,
            engine TEXT DEFAULT 'InnoDB',
            version INTEGER DEFAULT 10,
            row_format TEXT DEFAULT 'Dynamic',
            table_rows INTEGER DEFAULT 0,
            avg_row_length INTEGER DEFAULT 0,
            data_length INTEGER DEFAULT 16384,
            max_data_length INTEGER DEFAULT 0,
            index_length INTEGER DEFAULT 0,
            data_free INTEGER DEFAULT 0,
            auto_increment INTEGER,
            create_time TEXT,
            update_time TEXT,
            check_time TEXT,
            table_collation TEXT DEFAULT 'utf8mb4_0900_ai_ci',
            checksum INTEGER,
            create_options TEXT,
            table_comment TEXT DEFAULT ''
        )
    ''')
    
    # 2. INFORMATION_SCHEMA.COLUMNS - 列信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_columns (
            table_catalog TEXT DEFAULT 'def',
            table_schema TEXT,
            table_name TEXT,
            column_name TEXT,
            ordinal_position INTEGER,
            column_default TEXT,
            is_nullable TEXT DEFAULT 'YES',
            data_type TEXT,
            character_maximum_length INTEGER,
            character_octet_length INTEGER,
            numeric_precision INTEGER,
            numeric_scale INTEGER,
            datetime_precision INTEGER,
            character_set_name TEXT DEFAULT 'utf8mb4',
            collation_name TEXT DEFAULT 'utf8mb4_0900_ai_ci',
            column_type TEXT,
            column_key TEXT DEFAULT '',
            extra TEXT DEFAULT '',
            privileges TEXT DEFAULT 'select,insert,update,references',
            column_comment TEXT DEFAULT '',
            generation_expression TEXT DEFAULT '',
            srs_id INTEGER
        )
    ''')
    
    # 3. INFORMATION_SCHEMA.SCHEMATA - 数据库信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_schemata (
            catalog_name TEXT DEFAULT 'def',
            schema_name TEXT,
            default_character_set_name TEXT DEFAULT 'utf8mb4',
            default_collation_name TEXT DEFAULT 'utf8mb4_0900_ai_ci',
            sql_path TEXT
        )
    ''')
    
    # 4. INFORMATION_SCHEMA.KEY_COLUMN_USAGE - 键列使用信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_key_column_usage (
            constraint_catalog TEXT DEFAULT 'def',
            constraint_schema TEXT,
            constraint_name TEXT,
            table_catalog TEXT DEFAULT 'def',
            table_schema TEXT,
            table_name TEXT,
            column_name TEXT,
            ordinal_position INTEGER,
            position_in_unique_constraint INTEGER,
            referenced_table_schema TEXT,
            referenced_table_name TEXT,
            referenced_column_name TEXT
        )
    ''')
    
    # 5. INFORMATION_SCHEMA.TABLE_CONSTRAINTS - 表约束信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_table_constraints (
            constraint_catalog TEXT DEFAULT 'def',
            constraint_schema TEXT,
            constraint_name TEXT,
            table_schema TEXT,
            table_name TEXT,
            constraint_type TEXT,
            enforced TEXT DEFAULT 'YES'
        )
    ''')
    
    # 6. INFORMATION_SCHEMA.STATISTICS - 索引统计信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_statistics (
            table_catalog TEXT DEFAULT 'def',
            table_schema TEXT,
            table_name TEXT,
            non_unique INTEGER,
            index_schema TEXT,
            index_name TEXT,
            seq_in_index INTEGER,
            column_name TEXT,
            collation TEXT,
            cardinality INTEGER,
            sub_part INTEGER,
            packed TEXT,
            nullable TEXT,
            index_type TEXT DEFAULT 'BTREE',
            comment TEXT DEFAULT '',
            index_comment TEXT DEFAULT ''
        )
    ''')
    
    # 7. INFORMATION_SCHEMA.PROCESSLIST - 进程列表信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_processlist (
            id INTEGER,
            user TEXT,
            host TEXT,
            db TEXT,
            command TEXT,
            time INTEGER,
            state TEXT,
            info TEXT
        )
    ''')
    
    # 插入INFORMATION_SCHEMA.SCHEMATA数据
    cursor.execute('DELETE FROM information_schema_schemata')
    schemata_data = [
        ('information_schema', 'utf8', 'utf8_general_ci', None),
        ('shop_system_db', 'utf8mb4', 'utf8mb4_0900_ai_ci', None),
        ('mysql', 'utf8mb4', 'utf8mb4_0900_ai_ci', None),
        ('performance_schema', 'utf8mb4', 'utf8mb4_0900_ai_ci', None),
        ('sys', 'utf8mb4', 'utf8mb4_0900_ai_ci', None)
    ]
    cursor.executemany('''
        INSERT INTO information_schema_schemata (schema_name, default_character_set_name, default_collation_name, sql_path)
        VALUES (?, ?, ?, ?)
    ''', schemata_data)
    
    # 插入INFORMATION_SCHEMA.TABLES数据
    cursor.execute('DELETE FROM information_schema_tables')
    schema_tables = [
        ('def', 'shop_system_db', 'users', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 15, 1365, 16384, 0, 0, 0, None, '2024-01-10 09:00:00', '2024-07-29 15:00:00', None, 'utf8mb4_0900_ai_ci', None, '', '用户账户表'),
        ('def', 'shop_system_db', 'products', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 50, 819, 16384, 0, 0, 0, None, '2024-01-10 09:00:00', '2024-07-29 15:00:00', None, 'utf8mb4_0900_ai_ci', None, '', '商品信息表'),
        ('def', 'shop_system_db', 'orders', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 100, 409, 16384, 0, 0, 0, None, '2024-01-10 09:00:00', '2024-07-29 15:00:00', None, 'utf8mb4_0900_ai_ci', None, '', '订单信息表'),
        ('def', 'shop_system_db', 'order_items', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 200, 204, 16384, 0, 0, 0, None, '2024-01-10 09:00:00', '2024-07-29 15:00:00', None, 'utf8mb4_0900_ai_ci', None, '', '订单商品详情表'),
        ('def', 'shop_system_db', 'categories', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 10, 1638, 16384, 0, 0, 0, None, '2024-01-10 09:00:00', '2024-07-29 15:00:00', None, 'utf8mb4_0900_ai_ci', None, '', '商品分类表'),
        ('def', 'shop_system_db', 'reviews', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 80, 512, 16384, 0, 0, 0, None, '2024-01-10 09:00:00', '2024-07-29 15:00:00', None, 'utf8mb4_0900_ai_ci', None, '', '商品评价表'),
        ('def', 'shop_system_db', 'admin_users', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 5, 3276, 16384, 0, 0, 0, None, '2024-01-10 09:00:00', '2024-07-29 15:00:00', None, 'utf8mb4_0900_ai_ci', None, '', '管理员用户表'),
        ('def', 'shop_system_db', 'payment_info', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 50, 819, 16384, 0, 0, 0, None, '2024-01-10 09:00:00', '2024-07-29 15:00:00', None, 'utf8mb4_0900_ai_ci', None, '', '支付信息表'),
        ('def', 'shop_system_db', 'shipping_address', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 60, 682, 16384, 0, 0, 0, None, '2024-01-10 09:00:00', '2024-07-29 15:00:00', None, 'utf8mb4_0900_ai_ci', None, '', '收货地址表'),
        ('def', 'shop_system_db', 'system_config', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 20, 819, 16384, 0, 0, 0, None, '2024-01-10 09:00:00', '2024-07-29 15:00:00', None, 'utf8mb4_0900_ai_ci', None, '', '系统配置表')
    ]
    cursor.executemany('''
        INSERT INTO information_schema_tables (table_catalog, table_schema, table_name, table_type, engine, version, row_format, table_rows, avg_row_length, data_length, max_data_length, index_length, data_free, auto_increment, create_time, update_time, check_time, table_collation, checksum, create_options, table_comment)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', schema_tables)
    
    # 插入INFORMATION_SCHEMA.COLUMNS数据
    cursor.execute('DELETE FROM information_schema_columns')
    schema_columns = [
        # users表列信息
        ('def', 'shop_system_db', 'users', 'user_id', 1, None, 'NO', 'int', None, None, 10, 0, None, None, None, 'int(11)', 'PRI', 'auto_increment', 'select,insert,update,references', '用户ID', '', None),
        ('def', 'shop_system_db', 'users', 'username', 2, None, 'NO', 'varchar', 50, 200, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(50)', 'UNI', '', 'select,insert,update,references', '用户名', '', None),
        ('def', 'shop_system_db', 'users', 'password', 3, None, 'NO', 'varchar', 255, 1020, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(255)', '', '', 'select,insert,update,references', '密码', '', None),
        ('def', 'shop_system_db', 'users', 'email', 4, None, 'NO', 'varchar', 100, 400, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(100)', 'UNI', '', 'select,insert,update,references', '邮箱', '', None),
        ('def', 'shop_system_db', 'users', 'real_name', 5, None, 'YES', 'varchar', 100, 400, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(100)', '', '', 'select,insert,update,references', '真实姓名', '', None),
        ('def', 'shop_system_db', 'users', 'phone', 6, None, 'YES', 'varchar', 20, 80, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(20)', '', '', 'select,insert,update,references', '手机号', '', None),
        ('def', 'shop_system_db', 'users', 'balance', 7, '0.00', 'YES', 'decimal', None, None, 10, 2, None, None, None, 'decimal(10,2)', '', '', 'select,insert,update,references', '账户余额', '', None),
        ('def', 'shop_system_db', 'users', 'register_time', 8, 'CURRENT_TIMESTAMP', 'NO', 'timestamp', None, None, None, None, 0, None, None, 'timestamp', '', '', 'select,insert,update,references', '注册时间', '', None),

        # products表列信息
        ('def', 'shop_system_db', 'products', 'product_id', 1, None, 'NO', 'int', None, None, 10, 0, None, None, None, 'int(11)', 'PRI', 'auto_increment', 'select,insert,update,references', '商品ID', '', None),
        ('def', 'shop_system_db', 'products', 'product_name', 2, None, 'NO', 'varchar', 200, 800, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(200)', '', '', 'select,insert,update,references', '商品名称', '', None),
        ('def', 'shop_system_db', 'products', 'category_id', 3, None, 'YES', 'int', None, None, 10, 0, None, None, None, 'int(11)', 'MUL', '', 'select,insert,update,references', '分类ID', '', None),
        ('def', 'shop_system_db', 'products', 'price', 4, None, 'NO', 'decimal', None, None, 10, 2, None, None, None, 'decimal(10,2)', '', '', 'select,insert,update,references', '价格', '', None),
        ('def', 'shop_system_db', 'products', 'stock', 5, '0', 'YES', 'int', None, None, 10, 0, None, None, None, 'int(11)', '', '', 'select,insert,update,references', '库存数量', '', None),
        ('def', 'shop_system_db', 'products', 'description', 6, None, 'YES', 'text', 65535, 65535, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'text', '', '', 'select,insert,update,references', '商品描述', '', None),

        # admin_users表列信息
        ('def', 'shop_system_db', 'admin_users', 'admin_id', 1, None, 'NO', 'int', None, None, 10, 0, None, None, None, 'int(11)', 'PRI', 'auto_increment', 'select,insert,update,references', '管理员ID', '', None),
        ('def', 'shop_system_db', 'admin_users', 'admin_username', 2, None, 'NO', 'varchar', 50, 200, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(50)', 'UNI', '', 'select,insert,update,references', '管理员用户名', '', None),
        ('def', 'shop_system_db', 'admin_users', 'admin_password', 3, None, 'NO', 'varchar', 255, 1020, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(255)', '', '', 'select,insert,update,references', '管理员密码', '', None),
        ('def', 'shop_system_db', 'admin_users', 'role', 4, 'admin', 'YES', 'varchar', 20, 80, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(20)', '', '', 'select,insert,update,references', '角色', '', None),

        # payment_info表列信息
        ('def', 'shop_system_db', 'payment_info', 'payment_id', 1, None, 'NO', 'int', None, None, 10, 0, None, None, None, 'int(11)', 'PRI', 'auto_increment', 'select,insert,update,references', '支付ID', '', None),
        ('def', 'shop_system_db', 'payment_info', 'user_id', 2, None, 'NO', 'int', None, None, 10, 0, None, None, None, 'int(11)', 'MUL', '', 'select,insert,update,references', '用户ID', '', None),
        ('def', 'shop_system_db', 'payment_info', 'card_number', 3, None, 'NO', 'varchar', 20, 80, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(20)', '', '', 'select,insert,update,references', '银行卡号', '', None),
        ('def', 'shop_system_db', 'payment_info', 'card_holder', 4, None, 'NO', 'varchar', 100, 400, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(100)', '', '', 'select,insert,update,references', '持卡人姓名', '', None),
        ('def', 'shop_system_db', 'payment_info', 'cvv', 5, None, 'NO', 'varchar', 4, 16, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(4)', '', '', 'select,insert,update,references', 'CVV码', '', None)
    ]
    cursor.executemany('''
        INSERT INTO information_schema_columns (table_catalog, table_schema, table_name, column_name, ordinal_position, column_default, is_nullable, data_type, character_maximum_length, character_octet_length, numeric_precision, numeric_scale, datetime_precision, character_set_name, collation_name, column_type, column_key, extra, privileges, column_comment, generation_expression, srs_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', schema_columns)

    # 插入INFORMATION_SCHEMA.PROCESSLIST数据（当前进程）
    cursor.execute('DELETE FROM information_schema_processlist')
    processlist_data = [
        (1, 'shop_admin', 'localhost', 'shop_system_db', 'Query', 0, 'executing', 'SELECT * FROM products'),
        (2, 'shop_user', 'localhost', 'shop_system_db', 'Sleep', 180, 'waiting', None),
        (3, 'root', 'localhost', None, 'Query', 0, 'show processlist', 'SHOW PROCESSLIST')
    ]
    cursor.executemany('''
        INSERT INTO information_schema_processlist (id, user, host, db, command, time, state, info)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', processlist_data)

    # 创建业务表结构

    # 用户表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            user_id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            password TEXT NOT NULL,
            email TEXT NOT NULL UNIQUE,
            real_name TEXT,
            phone TEXT,
            balance DECIMAL(10,2) DEFAULT 0.00,
            register_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # 商品分类表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS categories (
            category_id INTEGER PRIMARY KEY AUTOINCREMENT,
            category_name TEXT NOT NULL,
            description TEXT
        )
    ''')

    # 商品表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            product_id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_name TEXT NOT NULL,
            category_id INTEGER,
            price DECIMAL(10,2) NOT NULL,
            stock INTEGER DEFAULT 0,
            description TEXT,
            image_url TEXT,
            create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # 订单表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            order_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL,
            status TEXT DEFAULT 'pending',
            create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            shipping_address TEXT
        )
    ''')

    # 订单商品详情表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS order_items (
            item_id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            price DECIMAL(10,2) NOT NULL
        )
    ''')

    # 商品评价表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS reviews (
            review_id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            rating INTEGER CHECK(rating >= 1 AND rating <= 5),
            comment TEXT,
            create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # 管理员用户表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS admin_users (
            admin_id INTEGER PRIMARY KEY AUTOINCREMENT,
            admin_username TEXT NOT NULL UNIQUE,
            admin_password TEXT NOT NULL,
            role TEXT DEFAULT 'admin',
            last_login TIMESTAMP
        )
    ''')

    # 支付信息表（敏感数据）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS payment_info (
            payment_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            card_number TEXT NOT NULL,
            card_holder TEXT NOT NULL,
            cvv TEXT NOT NULL,
            expiry_date TEXT NOT NULL,
            create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # 收货地址表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS shipping_address (
            address_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            recipient_name TEXT NOT NULL,
            phone TEXT NOT NULL,
            address TEXT NOT NULL,
            city TEXT NOT NULL,
            postal_code TEXT,
            is_default INTEGER DEFAULT 0
        )
    ''')

    # 系统配置表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS system_config (
            config_key TEXT PRIMARY KEY,
            config_value TEXT,
            description TEXT
        )
    ''')

    # 插入测试数据（在所有表创建完成后）
    # 插入分类数据
    cursor.execute('DELETE FROM categories')
    categories_data = [
        (1, '电子产品', '手机、电脑、数码产品'),
        (2, '服装鞋帽', '男装、女装、童装、鞋子'),
        (3, '家居用品', '家具、装饰、生活用品'),
        (4, '图书音像', '图书、音乐、电影'),
        (5, '运动户外', '运动器材、户外用品')
    ]
    cursor.executemany('INSERT INTO categories (category_id, category_name, description) VALUES (?, ?, ?)', categories_data)

    # 插入用户数据
    cursor.execute('DELETE FROM users')
    users_data = [
        (1, 'admin', 'Admin@2024!', '<EMAIL>', '张管理员', '13800138000', 10000.00, '2024-01-15 10:00:00'),
        (2, 'customer1', 'Customer123!', '<EMAIL>', '李小明', '13800138001', 2500.50, '2024-02-20 14:30:00'),
        (3, 'customer2', 'Pass@word2024', '<EMAIL>', '王小红', '13800138002', 1800.75, '2024-03-10 09:15:00'),
        (4, 'vip_user', 'VIP_Secret@123', '<EMAIL>', '赵大富', '13800138003', 50000.00, '2024-01-01 08:00:00'),
        (5, 'test_user', 'Test123456!', '<EMAIL>', '测试用户', '13800138004', 500.00, '2024-07-01 16:20:00')
    ]
    cursor.executemany('INSERT INTO users (user_id, username, password, email, real_name, phone, balance, register_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)', users_data)

    # 插入商品数据
    cursor.execute('DELETE FROM products')
    products_data = [
        (1, 'iPhone 15 Pro Max', 1, 9999.00, 50, '苹果最新旗舰手机，256GB存储', '/static/images/iphone15.jpg', '2024-01-15 10:00:00'),
        (2, 'MacBook Pro M3', 1, 15999.00, 20, '苹果笔记本电脑，16英寸', '/static/images/macbook.jpg', '2024-01-15 10:00:00'),
        (3, '耐克运动鞋', 2, 899.00, 100, '经典款运动鞋，舒适透气', '/static/images/nike.jpg', '2024-01-15 10:00:00'),
        (4, '小米智能手表', 1, 1299.00, 80, '健康监测，长续航', '/static/images/watch.jpg', '2024-01-15 10:00:00'),
        (5, '索尼耳机', 1, 2999.00, 30, '降噪耳机，音质出色', '/static/images/headphone.jpg', '2024-01-15 10:00:00')
    ]
    cursor.executemany('INSERT INTO products (product_id, product_name, category_id, price, stock, description, image_url, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)', products_data)

    # 插入管理员数据
    cursor.execute('DELETE FROM admin_users')
    admin_data = [
        (1, 'superadmin', 'SuperAdmin@2024!', 'superadmin', '2024-07-29 08:00:00'),
        (2, 'shopmanager', 'Manager@123!', 'manager', '2024-07-29 09:30:00'),
        (3, 'support', 'Support@456!', 'support', '2024-07-29 10:15:00')
    ]
    cursor.executemany('INSERT INTO admin_users (admin_id, admin_username, admin_password, role, last_login) VALUES (?, ?, ?, ?, ?)', admin_data)

    # 插入支付信息数据（敏感数据）
    cursor.execute('DELETE FROM payment_info')
    payment_data = [
        (1, 2, '6222021234567890', '李小明', '123', '12/26', '2024-02-20 15:00:00'),
        (2, 3, '6222021234567891', '王小红', '456', '08/25', '2024-03-10 10:00:00'),
        (3, 4, '6222021234567892', '赵大富', '789', '06/27', '2024-01-01 09:00:00')
    ]
    cursor.executemany('INSERT INTO payment_info (payment_id, user_id, card_number, card_holder, cvv, expiry_date, create_time) VALUES (?, ?, ?, ?, ?, ?, ?)', payment_data)

    # 插入系统配置数据
    cursor.execute('DELETE FROM system_config')
    config_data = [
        ('site_name', '小飞飞科技在线商城', '网站名称'),
        ('admin_email', '<EMAIL>', '管理员邮箱'),
        ('payment_gateway', 'alipay,wechat,unionpay', '支付网关'),
        ('shipping_fee', '15.00', '配送费用'),
        ('vip_discount', '0.85', 'VIP折扣率'),
        ('database_version', 'MySQL 8.0.35', '数据库版本'),
        ('system_version', 'v2.1.5', '系统版本'),
        ('backup_time', '2024-07-29 02:30:00', '最后备份时间'),
        ('maintenance_mode', 'false', '维护模式'),
        ('max_login_attempts', '5', '最大登录尝试次数')
    ]
    cursor.executemany('INSERT INTO system_config (config_key, config_value, description) VALUES (?, ?, ?)', config_data)

    conn.commit()
    conn.close()
    print("在线商城数据库初始化完成！")

# 路由处理

@app.route('/')
def index():
    """首页 - 显示商品列表"""
    try:
        db_path = os.path.join(os.path.dirname(__file__), 'online_shop_system.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 获取商品列表
        cursor.execute('SELECT product_id, product_name, price, stock, description FROM products LIMIT 10')
        products = cursor.fetchall()

        # 获取分类列表
        cursor.execute('SELECT category_id, category_name FROM categories')
        categories = cursor.fetchall()

        conn.close()
        return render_template('index.html', products=products, categories=categories)
    except Exception as e:
        flash(f'系统错误: {str(e)}', 'error')
        return render_template('index.html', products=[], categories=[])

@app.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录 - 存在SQL注入漏洞"""
    if request.method == 'GET':
        return render_template('login.html')

    username = request.form.get('username', '')
    password = request.form.get('password', '')

    if not username or not password:
        flash('请输入用户名和密码', 'error')
        return render_template('login.html')

    # 简单的SQL注入过滤 (难度系数: 4/10)
    def basic_sql_filter(input_str):
        """基础SQL注入过滤，阻止常见万能密码但保留绕过空间"""
        input_lower = input_str.lower().strip()

        # 阻止最常见的万能密码组合
        blocked_patterns = [
            "' or 1=1 #",
            "' or '1'='1' #",
            "admin' #",
            "' or 1=1--",
            "' or '1'='1'--",
            "admin'--",
            "' or 1=1/*",
            "' or true #",
            "' or 'a'='a' #"
        ]

        # 检查是否包含被阻止的模式
        for pattern in blocked_patterns:
            if pattern in input_lower:
                return False

        # 阻止一些明显的SQL关键词组合（但不是全部）
        if "' or " in input_lower and "=" in input_lower and ("#" in input_lower or "--" in input_lower):
            # 进一步检查是否是简单的万能密码
            if any(simple in input_lower for simple in ["1=1", "'1'='1'", "true", "'a'='a'"]):
                return False

        return True

    # 应用过滤
    if not basic_sql_filter(username):
        flash('输入包含非法字符，请重新输入', 'error')
        print(f"🛡️ 过滤器阻止了输入: {username}")
        return render_template('login.html')

    if not basic_sql_filter(password):
        flash('输入包含非法字符，请重新输入', 'error')
        print(f"🛡️ 过滤器阻止了输入: {password}")
        return render_template('login.html')

    try:
        db_path = os.path.join(os.path.dirname(__file__), 'online_shop_system.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 故意存在SQL注入漏洞的查询（只选择3列用于登录验证）
        query = f"SELECT user_id, username, real_name FROM users WHERE username = '{username}' AND password = '{password}'"
        print(f"执行SQL查询: {query}")

        # 处理注释符 - 正确截断注释后的内容
        original_query = query
        print(f"原始查询: {query}")

        # 处理 # 注释符
        if '#' in query:
            comment_pos = query.find('#')
            query = query[:comment_pos].rstrip()
            print(f"处理#注释符后: {query}")

        # 处理 -- 注释符（必须后面跟空格）
        if '-- ' in query:
            comment_pos = query.find('-- ')
            query = query[:comment_pos].rstrip()
            print(f"处理--注释符后: {query}")

        if query != original_query:
            print(f"最终查询: {query}")

        # 在处理注释符后再检查列数
        # 检查ORDER BY列数错误（登录查询只返回3列）
        if "order by" in query.lower():
            import re
            order_match = re.search(r'order by (\d+)', query.lower())
            if order_match:
                col_num = int(order_match.group(1))
                if col_num > 3:  # 登录查询只返回3列
                    raise sqlite3.OperationalError("no such column: order by column out of range")

        # 检查UNION列数错误（登录查询需要3列匹配）
        if "union select" in query.lower():
            import re
            # 检查处理注释符后的查询
            union_match = re.search(r'union select (.*?)(?:from|$)', query.lower())
            if union_match:
                columns_str = union_match.group(1).strip()
                columns = [col.strip() for col in columns_str.split(',')]
                print(f"登录检测到UNION SELECT列数: {len(columns)} 列: {columns}")
                if len(columns) != 3:  # 登录查询需要3列
                    raise sqlite3.OperationalError("The used SELECT statements have a different number of columns")

        # 替换MySQL函数为SQLite兼容的值
        mysql_functions = {
            'database()': "'shop_system_db'",
            'version()': "'MySQL 8.0.35-0ubuntu0.20.04.1'",
            'user()': "'shop_admin@localhost'",
            'current_user()': "'shop_admin@localhost'",
            'system_user()': "'shop_admin@localhost'",
            'session_user()': "'shop_admin@localhost'",
            'connection_id()': "12345",
            'schema()': "'shop_system_db'",
            'current_database()': "'shop_system_db'",
            '@@version': "'MySQL 8.0.35-0ubuntu0.20.04.1'",
            '@@version_comment': "'MySQL Community Server - GPL'",
            '@@hostname': "'shop-server-xiaofeifetech'",
            '@@datadir': "'/var/lib/mysql/'",
            '@@basedir': "'/usr/'",
            '@@port': "3306",
            '@@socket': "'/var/run/mysqld/mysqld.sock'",
            'now()': "'2024-07-29 15:30:00'",
            'current_timestamp()': "'2024-07-29 15:30:00'",
            'current_date()': "'2024-07-29'",
            'current_time()': "'15:30:00'",
            'unix_timestamp()': "1722248200",
            'rand()': "0.123456789",
            'pi()': "3.141593",
            'length(database())': "14",
            'char_length(database())': "14",
            'ascii(database())': "115",
            'hex(database())': "'73686F705F73797374656D5F6462'",
            'bin(1)': "'1'",
            'oct(8)': "'10'",
            'md5(database())': "'a1b2c3d4e5f6789012345678901234'",
            'sha1(database())': "'da39a3ee5e6b4b0d3255bfef95601890afd80709'",
            'upper(database())': "'SHOP_SYSTEM_DB'",
            'lower(database())': "'shop_system_db'",
            'substring(database(),1,4)': "'shop'",
            'substr(database(),1,4)': "'shop'",
            'left(database(),4)': "'shop'",
            'right(database(),2)': "'db'",
            'concat(database(),version())': "'shop_system_dbMySQL 8.0.35-0ubuntu0.20.04.1'",
            'group_concat(database())': "'shop_system_db'",
            'count(*)': "15",
            'sleep(1)': "0",
            'benchmark(1000000,md5(1))': "0"
        }

        original_query_for_functions = query
        for func, replacement in mysql_functions.items():
            if func.lower() in query.lower():
                # 使用正则表达式进行大小写不敏感的替换，确保完整匹配
                import re
                # 使用单词边界确保完整匹配函数名
                if func.endswith('()'):
                    pattern = re.escape(func[:-2]) + r'\(\)'
                else:
                    pattern = r'\b' + re.escape(func) + r'\b'
                query = re.sub(pattern, replacement, query, flags=re.IGNORECASE)

        if query != original_query_for_functions:
            print(f"替换MySQL函数后: {query}")

        # 处理information_schema查询 - 正确的MySQL表名格式
        if "information_schema." in query.lower():
            # MySQL的INFORMATION_SCHEMA表名映射到SQLite表名
            information_schema_mappings = {
                'information_schema.tables': 'information_schema_tables',
                'information_schema.columns': 'information_schema_columns',
                'information_schema.statistics': 'information_schema_statistics',
                'information_schema.schemata': 'information_schema_schemata',
                'information_schema.table_constraints': 'information_schema_table_constraints',
                'information_schema.key_column_usage': 'information_schema_key_column_usage',
                'information_schema.views': 'information_schema_views',
                'information_schema.routines': 'information_schema_routines',
                'information_schema.triggers': 'information_schema_triggers',
                'information_schema.processlist': 'information_schema_processlist'
            }

            original_query_for_schema = query
            for mysql_table, sqlite_table in information_schema_mappings.items():
                if mysql_table.lower() in query.lower():
                    import re
                    # 使用单词边界确保完整匹配表名
                    pattern = r'\b' + re.escape(mysql_table) + r'\b'
                    query = re.sub(pattern, sqlite_table, query, flags=re.IGNORECASE)

            if query != original_query_for_schema:
                print(f"替换information_schema表名后: {query}")

        cursor.execute(query)
        results = cursor.fetchall()  # 获取所有结果，不只是第一行

        if results:
            # 检查是否是UNION注入（只有明确包含UNION SELECT才显示数据）
            if "union select" in query.lower():
                # 这是UNION注入，在页面显示查询结果（3列格式）
                injection_results = []
                for row in results:
                    injection_results.append({
                        'col1': row[0] if len(row) > 0 else '',
                        'col2': row[1] if len(row) > 1 else '',
                        'col3': row[2] if len(row) > 2 else ''
                    })

                flash('查询执行成功，以下是结果：', 'info')
                return render_template('login.html', injection_results=injection_results, show_results=True)
            else:
                # 正常登录或OR绕过 - 直接跳转到系统
                user = results[0]
                session['user_id'] = user[0]
                session['username'] = user[1]

                # 根据查询结果的列数确定显示的用户名
                if len(user) >= 3:
                    display_name = user[2]  # real_name
                else:
                    display_name = user[1]  # username

                flash(f'欢迎来到小飞飞科技在线商城, {display_name}!', 'success')
                return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误', 'error')

    except sqlite3.Error as e:
        # 详细的MySQL错误分类系统
        error_msg = str(e).lower()
        print(f"SQLite原始错误: {e}")
        print(f"当前查询: {query}")

        # 错误分类和对应的MySQL错误
        error_category = "未知错误"
        mysql_error = ""

        if "order by column out of range" in error_msg:
            error_category = "列数探测错误"
            mysql_error = f"MySQL Error 1054: Unknown column in 'order clause'"
            print(f"🔍 错误类别: {error_category} - ORDER BY列数超出范围")

        elif "the used select statements have a different number of columns" in error_msg:
            error_category = "UNION列数不匹配"
            mysql_error = "MySQL Error 1222: The used SELECT statements have a different number of columns"
            print(f"🔍 错误类别: {error_category} - UNION查询列数不一致")

        elif "syntax error" in error_msg or "near" in error_msg:
            error_category = "SQL语法错误"
            # 分析具体的语法错误类型
            if "union" in query.lower() and "select" in query.lower():
                mysql_error = f"MySQL Error 1064: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near UNION SELECT"
                print(f"🔍 错误类别: {error_category} - UNION语法错误")
            elif "order by" in query.lower():
                mysql_error = f"MySQL Error 1064: You have an error in your SQL syntax; check the manual for ORDER BY syntax"
                print(f"🔍 错误类别: {error_category} - ORDER BY语法错误")
            else:
                mysql_error = f"MySQL Error 1064: You have an error in your SQL syntax near '{username}' at line 1"
                print(f"🔍 错误类别: {error_category} - 一般语法错误")

        elif "no such column" in error_msg:
            error_category = "字段不存在"
            if "order by" in username.lower():
                mysql_error = f"MySQL Error 1054: Unknown column in 'order clause'"
                print(f"🔍 错误类别: {error_category} - ORDER BY中的字段不存在")
            else:
                mysql_error = f"MySQL Error 1054: Unknown column '{username}' in 'where clause'"
                print(f"🔍 错误类别: {error_category} - WHERE条件中的字段不存在")

        elif "no such table" in error_msg:
            error_category = "表不存在"
            mysql_error = f"MySQL Error 1146: Table 'shop_system_db.users' doesn't exist"
            print(f"🔍 错误类别: {error_category} - 查询的表不存在")

        elif "no such function" in error_msg:
            error_category = "函数不支持"
            # 检查是否是未支持的MySQL函数
            unsupported_functions = []
            common_functions = ['database', 'version', 'user', 'current_user', 'now', 'count', 'length', 'substring']
            for func in common_functions:
                if f"{func}(" in query.lower():
                    unsupported_functions.append(func)

            if unsupported_functions:
                mysql_error = f"MySQL Error 1305: FUNCTION shop_system_db.{unsupported_functions[0]} does not exist"
                print(f"🔍 错误类别: {error_category} - 函数 {unsupported_functions[0]}() 不被支持")
            else:
                mysql_error = f"MySQL Error 1305: Unknown function in query"
                print(f"🔍 错误类别: {error_category} - 未知函数调用")

        elif "datatype mismatch" in error_msg:
            error_category = "数据类型不匹配"
            mysql_error = f"MySQL Error 1366: Incorrect string value for column"
            print(f"🔍 错误类别: {error_category} - 数据类型转换错误")

        elif "constraint" in error_msg:
            error_category = "约束违反"
            mysql_error = f"MySQL Error 1062: Duplicate entry or constraint violation"
            print(f"🔍 错误类别: {error_category} - 违反数据库约束")

        else:
            error_category = "其他数据库错误"
            mysql_error = f"MySQL Error 1064: You have an error in your SQL syntax near '{query[-50:]}' at line 1"
            print(f"🔍 错误类别: {error_category} - 未分类的数据库错误")

        # 输出错误总结
        print(f"📊 错误总结:")
        print(f"   - 错误类别: {error_category}")
        print(f"   - MySQL错误码: {mysql_error.split(':')[0] if ':' in mysql_error else 'Unknown'}")
        print(f"   - 错误描述: {mysql_error}")

        flash(f'数据库连接错误: {mysql_error}', 'error')
        print(f"🚨 最终MySQL错误: {mysql_error}")

    except Exception as e:
        flash(f'系统错误: {str(e)}', 'error')
        print(f"系统异常: {e}")

    return render_template('login.html')

@app.route('/dashboard')
def dashboard():
    """用户仪表板"""
    if 'user_id' not in session:
        flash('请先登录', 'error')
        return redirect(url_for('login'))

    try:
        db_path = os.path.join(os.path.dirname(__file__), 'online_shop_system.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 获取用户信息
        cursor.execute('SELECT username, real_name, email, balance FROM users WHERE user_id = ?', (session['user_id'],))
        user_info = cursor.fetchone()

        # 获取最新商品
        cursor.execute('SELECT product_id, product_name, price FROM products ORDER BY create_time DESC LIMIT 6')
        latest_products = cursor.fetchall()

        # 获取用户订单数量
        cursor.execute('SELECT COUNT(*) FROM orders WHERE user_id = ?', (session['user_id'],))
        order_count = cursor.fetchone()[0]

        conn.close()
        return render_template('dashboard.html', user_info=user_info, latest_products=latest_products, order_count=order_count)
    except Exception as e:
        flash(f'系统错误: {str(e)}', 'error')
        return render_template('dashboard.html', user_info=None, latest_products=[], order_count=0)

@app.route('/search', methods=['GET', 'POST'])
def search():
    """商品搜索 - 存在SQL注入漏洞"""
    if request.method == 'GET':
        return render_template('search.html', products=[])

    search_term = request.form.get('search_term', '')

    if not search_term:
        flash('请输入搜索关键词', 'error')
        return render_template('search.html', products=[])

    try:
        db_path = os.path.join(os.path.dirname(__file__), 'online_shop_system.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 故意存在SQL注入漏洞的搜索查询（3列）
        query = f"SELECT product_name, price, description FROM products WHERE product_name LIKE '%{search_term}%'"
        print(f"执行搜索SQL查询: {query}")

        # 处理注释符 - 正确截断注释后的内容
        original_query = query
        print(f"原始搜索查询: {query}")

        # 处理 # 注释符
        if '#' in query:
            comment_pos = query.find('#')
            query = query[:comment_pos].rstrip()
            print(f"处理#注释符后: {query}")

        # 处理 -- 注释符（必须后面跟空格）
        if '-- ' in query:
            comment_pos = query.find('-- ')
            query = query[:comment_pos].rstrip()
            print(f"处理--注释符后: {query}")

        if query != original_query:
            print(f"处理注释符后的搜索查询: {query}")

        # 在处理注释符后再检查列数
        # 检查ORDER BY列数错误
        if "order by" in query.lower():
            import re
            order_match = re.search(r'order by (\d+)', query.lower())
            if order_match:
                col_num = int(order_match.group(1))
                if col_num > 3:  # 搜索查询只返回3列
                    raise sqlite3.OperationalError("no such column: order by column out of range")

        # 检查UNION列数错误
        if "union select" in query.lower():
            import re
            # 检查处理注释符后的查询
            union_match = re.search(r'union select (.*?)(?:from|$)', query.lower())
            if union_match:
                columns_str = union_match.group(1).strip()
                columns = [col.strip() for col in columns_str.split(',')]
                print(f"搜索检测到UNION SELECT列数: {len(columns)} 列: {columns}")
                if len(columns) != 3:  # 搜索查询返回3列
                    raise sqlite3.OperationalError("The used SELECT statements have a different number of columns")

        # 替换MySQL函数为SQLite兼容的值（搜索功能）
        mysql_functions = {
            'database()': "'shop_system_db'",
            'version()': "'MySQL 8.0.35-0ubuntu0.20.04.1'",
            'user()': "'shop_admin@localhost'",
            'current_user()': "'shop_admin@localhost'",
            'system_user()': "'shop_admin@localhost'",
            'session_user()': "'shop_admin@localhost'",
            'connection_id()': "12345",
            'schema()': "'shop_system_db'",
            '@@version': "'MySQL 8.0.35-0ubuntu0.20.04.1'",
            '@@hostname': "'shop-server-xiaofeifetech'",
            'now()': "'2024-07-29 15:30:00'",
            'count(*)': "50"
        }

        original_query_for_functions = query
        for func, replacement in mysql_functions.items():
            if func.lower() in query.lower():
                import re
                # 使用单词边界确保完整匹配函数名
                if func.endswith('()'):
                    pattern = re.escape(func[:-2]) + r'\(\)'
                else:
                    pattern = r'\b' + re.escape(func) + r'\b'
                query = re.sub(pattern, replacement, query, flags=re.IGNORECASE)

        if query != original_query_for_functions:
            print(f"搜索替换MySQL函数后: {query}")

        # 处理information_schema查询（搜索功能）- 正确的MySQL表名格式
        if "information_schema." in query.lower():
            information_schema_mappings = {
                'information_schema.tables': 'information_schema_tables',
                'information_schema.columns': 'information_schema_columns',
                'information_schema.statistics': 'information_schema_statistics',
                'information_schema.schemata': 'information_schema_schemata',
                'information_schema.table_constraints': 'information_schema_table_constraints',
                'information_schema.key_column_usage': 'information_schema_key_column_usage',
                'information_schema.views': 'information_schema_views',
                'information_schema.routines': 'information_schema_routines',
                'information_schema.triggers': 'information_schema_triggers',
                'information_schema.processlist': 'information_schema_processlist'
            }

            original_query_for_schema = query
            for mysql_table, sqlite_table in information_schema_mappings.items():
                if mysql_table.lower() in query.lower():
                    import re
                    # 使用单词边界确保完整匹配表名
                    pattern = r'\b' + re.escape(mysql_table) + r'\b'
                    query = re.sub(pattern, sqlite_table, query, flags=re.IGNORECASE)

            if query != original_query_for_schema:
                print(f"搜索替换information_schema表名后: {query}")

        cursor.execute(query)
        results = cursor.fetchall()

        # 将结果转换为字典格式
        products = []
        for row in results:
            products.append({
                'name': row[0] if len(row) > 0 else '',
                'price': row[1] if len(row) > 1 else '',
                'description': row[2] if len(row) > 2 else ''
            })

        conn.close()

        if products:
            flash(f'找到 {len(products)} 个相关商品', 'success')
        else:
            flash('没有找到相关商品', 'info')

        return render_template('search.html', products=products, search_term=search_term)

    except sqlite3.Error as e:
        # 详细的MySQL搜索错误分类系统
        error_msg = str(e).lower()
        print(f"搜索SQLite原始错误: {e}")
        print(f"搜索当前查询: {query}")

        # 错误分类
        error_category = "未知搜索错误"
        mysql_error = ""

        if "order by column out of range" in error_msg:
            error_category = "搜索列数探测错误"
            mysql_error = f"MySQL Error 1054: Unknown column in 'order clause'"
            print(f"🔍 搜索错误类别: {error_category} - ORDER BY列数超出范围(搜索结果只有3列)")

        elif "the used select statements have a different number of columns" in error_msg:
            error_category = "搜索UNION列数不匹配"
            mysql_error = "MySQL Error 1222: The used SELECT statements have a different number of columns"
            print(f"🔍 搜索错误类别: {error_category} - UNION查询列数不匹配(搜索结果需要3列)")

        elif "syntax error" in error_msg or "near" in error_msg:
            error_category = "搜索SQL语法错误"
            mysql_error = f"MySQL Error 1064: You have an error in your SQL syntax near '{search_term}' at line 1"
            print(f"🔍 搜索错误类别: {error_category} - 搜索查询语法错误")

        elif "no such column" in error_msg:
            error_category = "搜索字段不存在"
            mysql_error = f"MySQL Error 1054: Unknown column '{search_term}' in 'where clause'"
            print(f"🔍 搜索错误类别: {error_category} - 搜索中的字段不存在")

        elif "no such function" in error_msg:
            error_category = "搜索函数不支持"
            mysql_error = f"MySQL Error 1305: FUNCTION shop_system_db.unknown does not exist"
            print(f"🔍 搜索错误类别: {error_category} - 搜索中使用了不支持的函数")

        else:
            error_category = "其他搜索错误"
            mysql_error = f"MySQL Error 1064: You have an error in your SQL syntax near '{search_term}' at line 1"
            print(f"🔍 搜索错误类别: {error_category} - 未分类的搜索错误")

        print(f"📊 搜索错误总结:")
        print(f"   - 错误类别: {error_category}")
        print(f"   - MySQL错误码: {mysql_error.split(':')[0] if ':' in mysql_error else 'Unknown'}")
        print(f"   - 错误描述: {mysql_error}")

        flash(f'搜索查询错误: {mysql_error}', 'error')

    except Exception as e:
        flash(f'搜索系统错误: {str(e)}', 'error')
        print(f"搜索系统异常: {e}")

    return render_template('search.html', products=[], search_term=search_term)

@app.route('/logout')
def logout():
    """用户登出"""
    session.clear()
    flash('您已成功登出', 'success')
    return redirect(url_for('index'))

if __name__ == '__main__':
    # 初始化数据库
    init_database()
    
    print("小飞飞科技在线商城系统启动中...")
    print("系统版本: XiaoFeiFei Online Shop System v2.1.5")
    print("数据库: MySQL 8.0.35")
    print("访问地址: http://localhost:8001")
    print("\n客服热线: 400-888-6666")
    print("技术支持: <EMAIL>")
    print("商务合作: <EMAIL>")
    print("\n系统状态: 正常运行")
    print("最后备份时间: 2024-07-29 02:30:00")
    
    app.run(host='127.0.0.1', port=3001, debug=True)
