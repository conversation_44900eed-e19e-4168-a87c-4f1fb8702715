<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户中心 - 小飞飞科技在线商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }
        
        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: #667eea;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-info {
            color: #333;
            font-weight: 500;
        }
        
        .btn {
            padding: 0.5rem 1.5rem;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .welcome-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .welcome-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .welcome-subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
        }
        
        .user-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .stat-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .dashboard-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .card-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .card-icon {
            font-size: 1.5rem;
        }
        
        .search-section {
            margin-bottom: 2rem;
        }
        
        .search-form {
            display: flex;
            background: white;
            border-radius: 50px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .search-input {
            flex: 1;
            padding: 1rem 2rem;
            border: none;
            font-size: 1rem;
            outline: none;
        }
        
        .search-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 1rem 2rem;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
        }
        
        .search-btn:hover {
            background: #5a6fd8;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .product-image {
            height: 150px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #ccc;
        }
        
        .product-info {
            padding: 1rem;
        }
        
        .product-name {
            font-size: 1rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .product-price {
            font-size: 1.2rem;
            color: #e74c3c;
            font-weight: bold;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 1rem;
            border-radius: 15px;
            text-decoration: none;
            text-align: center;
            font-weight: 500;
            transition: transform 0.3s, box-shadow 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .flash-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .flash-message {
            background: #4CAF50;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 5px;
            margin-bottom: 0.5rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .flash-message.error {
            background: #f44336;
        }
        
        .flash-message.info {
            background: #2196F3;
        }
        
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .user-stats {
                grid-template-columns: 1fr 1fr;
            }
            
            .products-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">{{ message }}</div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="{{ url_for('index') }}" class="logo">🛒 小飞飞科技商城</a>
            <nav>
                <ul class="nav-links">
                    <li><a href="{{ url_for('index') }}">首页</a></li>
                    <li><a href="{{ url_for('search') }}">商品搜索</a></li>
                    <li><a href="{{ url_for('dashboard') }}">用户中心</a></li>
                    <li><a href="#orders">我的订单</a></li>
                    <li><a href="#profile">个人资料</a></li>
                </ul>
            </nav>
            <div class="user-menu">
                {% if user_info %}
                <span class="user-info">欢迎，{{ user_info[1] or user_info[0] }}！</span>
                {% endif %}
                <a href="{{ url_for('logout') }}" class="btn btn-outline">退出登录</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Welcome Section -->
        <section class="welcome-section">
            {% if user_info %}
            <h1 class="welcome-title">欢迎回来，{{ user_info[1] or user_info[0] }}！</h1>
            <p class="welcome-subtitle">您的专属购物中心 | 账户余额：¥{{ "%.2f"|format(user_info[3] or 0) }}</p>
            {% else %}
            <h1 class="welcome-title">欢迎来到用户中心！</h1>
            <p class="welcome-subtitle">您的专属购物中心</p>
            {% endif %}
            
            <div class="user-stats">
                <div class="stat-card">
                    <div class="stat-number">{{ order_count or 0 }}</div>
                    <div class="stat-label">我的订单</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ latest_products|length }}</div>
                    <div class="stat-label">推荐商品</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">VIP</div>
                    <div class="stat-label">会员等级</div>
                </div>
            </div>
        </section>

        <!-- Search Section -->
        <section class="search-section">
            <div class="dashboard-card">
                <h2 class="card-title">
                    <span class="card-icon">🔍</span>
                    商品搜索
                </h2>
                <form action="{{ url_for('search') }}" method="POST" class="search-form">
                    <input type="text" name="search_term" class="search-input" placeholder="搜索您想要的商品..." required>
                    <button type="submit" class="search-btn">搜索</button>
                </form>
            </div>
        </section>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Latest Products -->
            <div class="dashboard-card">
                <h2 class="card-title">
                    <span class="card-icon">🛍️</span>
                    推荐商品
                </h2>
                <div class="products-grid">
                    {% for product in latest_products %}
                    <div class="product-card">
                        <div class="product-image">🛍️</div>
                        <div class="product-info">
                            <div class="product-name">{{ product[1] }}</div>
                            <div class="product-price">¥{{ "%.2f"|format(product[2]) }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="dashboard-card">
                <h2 class="card-title">
                    <span class="card-icon">⚡</span>
                    快捷操作
                </h2>
                <div class="quick-actions">
                    <a href="#orders" class="action-btn">
                        <span>📦</span>
                        我的订单
                    </a>
                    <a href="#profile" class="action-btn">
                        <span>👤</span>
                        个人资料
                    </a>
                    <a href="#address" class="action-btn">
                        <span>📍</span>
                        收货地址
                    </a>
                    <a href="#payment" class="action-btn">
                        <span>💳</span>
                        支付方式
                    </a>
                    <a href="#service" class="action-btn">
                        <span>📞</span>
                        客服中心
                    </a>
                    <a href="#settings" class="action-btn">
                        <span>⚙️</span>
                        账户设置
                    </a>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 自动隐藏flash消息
        setTimeout(function() {
            const flashMessages = document.querySelector('.flash-messages');
            if (flashMessages) {
                flashMessages.style.opacity = '0';
                flashMessages.style.transition = 'opacity 0.5s';
                setTimeout(() => flashMessages.remove(), 500);
            }
        }, 3000);
        
        // 商品卡片点击效果
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
        
        // 快捷操作按钮点击效果
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                    // 这里可以添加实际的跳转逻辑
                    alert('功能开发中，敬请期待！');
                }, 150);
            });
        });
    </script>
</body>
</html>
