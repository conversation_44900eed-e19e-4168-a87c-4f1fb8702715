<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 小飞飞科技在线商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 600px;
        }
        
        .login-left {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        
        .login-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .login-title {
            font-size: 2rem;
            margin-bottom: 1rem;
            font-weight: bold;
        }
        
        .login-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        
        .feature-list {
            list-style: none;
            text-align: left;
        }
        
        .feature-item {
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .feature-icon {
            font-size: 1.2rem;
        }
        
        .login-right {
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .form-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 0.5rem;
            text-align: center;
        }
        
        .form-subtitle {
            color: #666;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s, box-shadow 0.3s;
            outline: none;
        }
        
        .form-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-input::placeholder {
            color: #999;
        }
        
        .login-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
            margin-bottom: 1rem;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .login-btn:active {
            transform: translateY(0);
        }
        
        .form-links {
            text-align: center;
            margin-top: 1rem;
        }
        
        .form-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 1rem;
            transition: color 0.3s;
        }
        
        .form-links a:hover {
            color: #5a6fd8;
            text-decoration: underline;
        }
        
        .back-home {
            position: absolute;
            top: 2rem;
            left: 2rem;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            transition: opacity 0.3s;
        }
        
        .back-home:hover {
            opacity: 0.8;
        }
        
        .flash-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .flash-message {
            background: #4CAF50;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 5px;
            margin-bottom: 0.5rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            max-width: 400px;
        }
        
        .flash-message.error {
            background: #f44336;
        }
        
        .flash-message.info {
            background: #2196F3;
        }
        
        .injection-results {
            margin-top: 2rem;
            padding: 1.5rem;
            background: #f0f8ff;
            border-radius: 10px;
            border-left: 4px solid #2196F3;
        }
        
        .injection-results h4 {
            color: #2196F3;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .results-table-container {
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
            background: white;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .results-table th,
        .results-table td {
            padding: 0.8rem 0.6rem;
            text-align: left;
            border: 1px solid #ddd;
            word-break: break-all;
            max-width: 150px;
        }
        
        .results-table th {
            background: #2196F3;
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        .results-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .results-table tr:hover {
            background: #e3f2fd;
        }
        
        .help-section {
            margin-top: 2rem;
            padding: 1.5rem;
            background: #f9f9f9;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .help-section h4 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .help-section p {
            font-size: 0.9rem;
            color: #666;
            margin: 0.5rem 0;
            line-height: 1.4;
        }
        
        @media (max-width: 768px) {
            .login-container {
                grid-template-columns: 1fr;
                max-width: 500px;
            }
            
            .login-left {
                padding: 2rem;
            }
            
            .login-right {
                padding: 2rem;
            }
            
            .back-home {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 1rem;
                justify-content: center;
            }
            
            body {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <a href="{{ url_for('index') }}" class="back-home">
        ← 返回首页
    </a>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">{{ message }}</div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <div class="login-container">
        <!-- Left Side -->
        <div class="login-left">
            <div class="login-logo">🛒</div>
            <h1 class="login-title">小飞飞科技商城</h1>
            <p class="login-subtitle">登录您的账户，享受更多专属服务</p>
            
            <ul class="feature-list">
                <li class="feature-item">
                    <span class="feature-icon">✨</span>
                    <span>专属会员优惠</span>
                </li>
                <li class="feature-item">
                    <span class="feature-icon">🚚</span>
                    <span>免费快速配送</span>
                </li>
                <li class="feature-item">
                    <span class="feature-icon">🔒</span>
                    <span>安全支付保障</span>
                </li>
                <li class="feature-item">
                    <span class="feature-icon">📞</span>
                    <span>24小时客服支持</span>
                </li>
            </ul>
        </div>

        <!-- Right Side -->
        <div class="login-right">
            <h2 class="form-title">用户登录</h2>
            <p class="form-subtitle">请输入您的登录凭据</p>

            <form action="{{ url_for('login') }}" method="POST">
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" id="username" name="username" class="form-input" 
                           placeholder="请输入用户名" required>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" id="password" name="password" class="form-input" 
                           placeholder="请输入密码" required>
                </div>

                <button type="submit" class="login-btn">立即登录</button>
            </form>

            <div class="form-links">
                <a href="#forgot">忘记密码？</a>
                <a href="#register">立即注册</a>
            </div>

            {% if show_results and injection_results %}
            <div class="injection-results">
                <h4>🔍 查询结果</h4>
                <div class="results-table-container">
                    <table class="results-table">
                        <thead>
                            <tr>
                                <th>列1</th>
                                <th>列2</th>
                                <th>列3</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for result in injection_results %}
                            <tr>
                                <td>{{ result.col1 }}</td>
                                <td>{{ result.col2 }}</td>
                                <td>{{ result.col3 }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% else %}
            <div class="help-section">
                <h4>💡 需要帮助？</h4>
                <p>如果您忘记了登录凭据，请联系客服或系统管理员</p>
                <p>新用户请先注册账户，享受更多专属服务</p>
                <p>客服热线：400-888-6666 | 技术支持：<EMAIL></p>
                <p>工作时间：周一至周日 9:00-21:00</p>
            </div>
            {% endif %}
        </div>
    </div>

    <script>
        // 自动隐藏flash消息
        setTimeout(function() {
            const flashMessages = document.querySelector('.flash-messages');
            if (flashMessages) {
                flashMessages.style.opacity = '0';
                flashMessages.style.transition = 'opacity 0.5s';
                setTimeout(() => flashMessages.remove(), 500);
            }
        }, 5000);

        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            
            if (!username || !password) {
                e.preventDefault();
                alert('请填写完整的登录信息');
                return;
            }
        });

        // 输入框焦点效果
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateY(-2px)';
                this.parentElement.style.transition = 'transform 0.3s';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = '';
            });
        });
    </script>
</body>
</html>
