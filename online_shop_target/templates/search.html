<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品搜索 - 小飞飞科技在线商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }
        
        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: #667eea;
        }
        
        .auth-buttons {
            display: flex;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1.5rem;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .search-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .search-subtitle {
            color: #666;
            margin-bottom: 2rem;
        }
        
        .search-form {
            display: flex;
            background: white;
            border-radius: 50px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .search-input {
            flex: 1;
            padding: 1rem 2rem;
            border: none;
            font-size: 1rem;
            outline: none;
        }
        
        .search-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 1rem 2rem;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
        }
        
        .search-btn:hover {
            background: #5a6fd8;
        }
        
        .search-tips {
            margin-top: 1.5rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .results-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .results-title {
            font-size: 1.5rem;
            color: #333;
        }
        
        .results-count {
            color: #667eea;
            font-weight: 500;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }
        
        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            border: 1px solid #f0f0f0;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .product-image {
            height: 200px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: #ccc;
        }
        
        .product-info {
            padding: 1.5rem;
        }
        
        .product-name {
            font-size: 1.1rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }
        
        .product-price {
            font-size: 1.3rem;
            color: #e74c3c;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .product-desc {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .no-results {
            text-align: center;
            padding: 3rem;
            color: #666;
        }
        
        .no-results-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .no-results-text {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }
        
        .no-results-tips {
            font-size: 0.9rem;
            color: #999;
        }
        
        .flash-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .flash-message {
            background: #4CAF50;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 5px;
            margin-bottom: 0.5rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .flash-message.error {
            background: #f44336;
        }
        
        .flash-message.info {
            background: #2196F3;
        }
        
        .search-suggestions {
            margin-top: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .suggestions-title {
            color: #667eea;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .suggestions-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        
        .suggestion-tag {
            background: #667eea;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .suggestion-tag:hover {
            background: #5a6fd8;
        }
        
        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .results-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .products-grid {
                grid-template-columns: 1fr;
            }
            
            .search-form {
                flex-direction: column;
                border-radius: 15px;
            }
            
            .search-input {
                border-radius: 15px 15px 0 0;
            }
            
            .search-btn {
                border-radius: 0 0 15px 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">{{ message }}</div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="{{ url_for('index') }}" class="logo">🛒 小飞飞科技商城</a>
            <nav>
                <ul class="nav-links">
                    <li><a href="{{ url_for('index') }}">首页</a></li>
                    <li><a href="{{ url_for('search') }}">商品搜索</a></li>
                    <li><a href="#categories">分类</a></li>
                    <li><a href="#hot">热门商品</a></li>
                    <li><a href="#contact">联系我们</a></li>
                </ul>
            </nav>
            <div class="auth-buttons">
                <a href="{{ url_for('login') }}" class="btn btn-outline">登录</a>
                <a href="#register" class="btn btn-primary">注册</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Search Section -->
        <section class="search-section">
            <h1 class="search-title">🔍 商品搜索</h1>
            <p class="search-subtitle">找到您心仪的商品，享受优质购物体验</p>
            
            <form action="{{ url_for('search') }}" method="POST" class="search-form">
                <input type="text" name="search_term" class="search-input" 
                       placeholder="请输入商品名称、品牌或关键词..." 
                       value="{{ search_term or '' }}" required>
                <button type="submit" class="search-btn">🔍 搜索</button>
            </form>
            
            <div class="search-tips">
                💡 搜索提示：支持商品名称、品牌、分类等关键词搜索
            </div>
            
            <div class="search-suggestions">
                <div class="suggestions-title">🔥 热门搜索</div>
                <div class="suggestions-list">
                    <span class="suggestion-tag" onclick="searchSuggestion('iPhone')">iPhone</span>
                    <span class="suggestion-tag" onclick="searchSuggestion('MacBook')">MacBook</span>
                    <span class="suggestion-tag" onclick="searchSuggestion('耐克')">耐克</span>
                    <span class="suggestion-tag" onclick="searchSuggestion('小米')">小米</span>
                    <span class="suggestion-tag" onclick="searchSuggestion('索尼')">索尼</span>
                    <span class="suggestion-tag" onclick="searchSuggestion('运动鞋')">运动鞋</span>
                </div>
            </div>
        </section>

        <!-- Results Section -->
        {% if products %}
        <section class="results-section">
            <div class="results-header">
                <h2 class="results-title">搜索结果</h2>
                <span class="results-count">找到 {{ products|length }} 个相关商品</span>
            </div>
            
            <div class="products-grid">
                {% for product in products %}
                <div class="product-card">
                    <div class="product-image">🛍️</div>
                    <div class="product-info">
                        <div class="product-name">{{ product.name }}</div>
                        <div class="product-price">¥{{ product.price }}</div>
                        <div class="product-desc">{{ product.description[:80] }}{% if product.description|length > 80 %}...{% endif %}</div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </section>
        {% elif search_term %}
        <section class="results-section">
            <div class="no-results">
                <div class="no-results-icon">😔</div>
                <div class="no-results-text">没有找到相关商品</div>
                <div class="no-results-tips">
                    请尝试使用其他关键词，或浏览我们的热门商品分类
                </div>
            </div>
        </section>
        {% endif %}
    </main>

    <script>
        // 自动隐藏flash消息
        setTimeout(function() {
            const flashMessages = document.querySelector('.flash-messages');
            if (flashMessages) {
                flashMessages.style.opacity = '0';
                flashMessages.style.transition = 'opacity 0.5s';
                setTimeout(() => flashMessages.remove(), 500);
            }
        }, 3000);
        
        // 商品卡片点击效果
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
        
        // 搜索建议点击
        function searchSuggestion(keyword) {
            document.querySelector('.search-input').value = keyword;
            document.querySelector('.search-form').submit();
        }
        
        // 搜索表单验证
        document.querySelector('.search-form').addEventListener('submit', function(e) {
            const searchTerm = document.querySelector('.search-input').value.trim();
            if (!searchTerm) {
                e.preventDefault();
                alert('请输入搜索关键词');
                return;
            }
        });
        
        // 搜索输入框焦点效果
        const searchInput = document.querySelector('.search-input');
        searchInput.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-2px)';
            this.parentElement.style.boxShadow = '0 15px 40px rgba(0,0,0,0.3)';
        });
        
        searchInput.addEventListener('blur', function() {
            this.parentElement.style.transform = '';
            this.parentElement.style.boxShadow = '0 10px 30px rgba(0,0,0,0.2)';
        });
    </script>
</body>
</html>
