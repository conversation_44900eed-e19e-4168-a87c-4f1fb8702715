<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小飞飞科技在线商城 - 首页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }
        
        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: #667eea;
        }
        
        .auth-buttons {
            display: flex;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1.5rem;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        .hero-section {
            text-align: center;
            padding: 4rem 2rem;
            color: white;
        }
        
        .hero-title {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .search-container {
            max-width: 600px;
            margin: 0 auto 3rem;
            position: relative;
        }
        
        .search-form {
            display: flex;
            background: white;
            border-radius: 50px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .search-input {
            flex: 1;
            padding: 1rem 2rem;
            border: none;
            font-size: 1rem;
            outline: none;
        }
        
        .search-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 1rem 2rem;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
        }
        
        .search-btn:hover {
            background: #5a6fd8;
        }
        
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .section-title {
            text-align: center;
            font-size: 2rem;
            color: white;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .category-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .category-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .category-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .category-desc {
            color: #666;
            font-size: 0.9rem;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }
        
        .product-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .product-image {
            height: 200px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: #ccc;
        }
        
        .product-info {
            padding: 1.5rem;
        }
        
        .product-name {
            font-size: 1.1rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .product-price {
            font-size: 1.3rem;
            color: #e74c3c;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .product-desc {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .footer {
            background: rgba(0, 0, 0, 0.8);
            color: white;
            text-align: center;
            padding: 2rem;
            margin-top: 3rem;
        }
        
        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 1rem;
        }
        
        .footer-links a {
            color: white;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .footer-links a:hover {
            color: #667eea;
        }
        
        .flash-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .flash-message {
            background: #4CAF50;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 5px;
            margin-bottom: 0.5rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .flash-message.error {
            background: #f44336;
        }
        
        .flash-message.info {
            background: #2196F3;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .categories {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .products-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">{{ message }}</div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="{{ url_for('index') }}" class="logo">🛒 小飞飞科技商城</a>
            <nav>
                <ul class="nav-links">
                    <li><a href="{{ url_for('index') }}">首页</a></li>
                    <li><a href="{{ url_for('search') }}">商品搜索</a></li>
                    <li><a href="#categories">分类</a></li>
                    <li><a href="#products">热门商品</a></li>
                    <li><a href="#contact">联系我们</a></li>
                </ul>
            </nav>
            <div class="auth-buttons">
                <a href="{{ url_for('login') }}" class="btn btn-outline">登录</a>
                <a href="#register" class="btn btn-primary">注册</a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <h1 class="hero-title">小飞飞科技在线商城</h1>
        <p class="hero-subtitle">品质生活，从这里开始 | 正品保证，快速配送</p>
        
        <div class="search-container">
            <form action="{{ url_for('search') }}" method="POST" class="search-form">
                <input type="text" name="search_term" class="search-input" placeholder="搜索您想要的商品..." required>
                <button type="submit" class="search-btn">🔍 搜索</button>
            </form>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Categories Section -->
        <section id="categories">
            <h2 class="section-title">商品分类</h2>
            <div class="categories">
                {% for category in categories %}
                <div class="category-card">
                    <div class="category-icon">
                        {% if category[0] == 1 %}📱
                        {% elif category[0] == 2 %}👕
                        {% elif category[0] == 3 %}🏠
                        {% elif category[0] == 4 %}📚
                        {% elif category[0] == 5 %}⚽
                        {% else %}🛍️
                        {% endif %}
                    </div>
                    <div class="category-name">{{ category[1] }}</div>
                    <div class="category-desc">精选优质商品</div>
                </div>
                {% endfor %}
            </div>
        </section>

        <!-- Products Section -->
        <section id="products">
            <h2 class="section-title">热门商品</h2>
            <div class="products-grid">
                {% for product in products %}
                <div class="product-card">
                    <div class="product-image">🛍️</div>
                    <div class="product-info">
                        <div class="product-name">{{ product[1] }}</div>
                        <div class="product-price">¥{{ "%.2f"|format(product[2]) }}</div>
                        <div class="product-desc">{{ product[4][:50] }}{% if product[4]|length > 50 %}...{% endif %}</div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="#about">关于我们</a>
                <a href="#service">客户服务</a>
                <a href="#privacy">隐私政策</a>
                <a href="#terms">服务条款</a>
                <a href="#contact">联系我们</a>
            </div>
            <p>&copy; 2024 小飞飞科技在线商城. 版权所有 | 客服热线: 400-888-6666</p>
            <p>技术支持: <EMAIL> | 商务合作: <EMAIL></p>
        </div>
    </footer>

    <script>
        // 自动隐藏flash消息
        setTimeout(function() {
            const flashMessages = document.querySelector('.flash-messages');
            if (flashMessages) {
                flashMessages.style.opacity = '0';
                flashMessages.style.transition = 'opacity 0.5s';
                setTimeout(() => flashMessages.remove(), 500);
            }
        }, 3000);
        
        // 商品卡片点击效果
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
        
        // 分类卡片点击效果
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
