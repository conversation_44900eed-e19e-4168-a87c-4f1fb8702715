<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渗透测试智能化检测工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="url"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .dropdown-container {
            position: relative;
            width: 100%;
        }
        .dropdown-header {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
        }
        .dropdown-header:hover {
            border-color: #007bff;
        }
        .dropdown-arrow {
            transition: transform 0.3s ease;
        }
        .dropdown-arrow.open {
            transform: rotate(180deg);
        }
        .dropdown-content {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        .dropdown-content.show {
            display: block;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        .checkbox-item:last-child {
            border-bottom: none;
        }
        .checkbox-item input[type="checkbox"] {
            margin-right: 10px;
            width: auto;
        }
        .checkbox-item label {
            margin: 0;
            font-weight: normal;
            cursor: pointer;
            flex: 1;
            font-size: 14px;
        }
        .checkbox-item:hover {
            background: #f8f9fa;
        }

        .start-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
        }
        .start-btn:hover {
            background: #218838;
        }
        .start-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .stop-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }
        .stop-btn:hover {
            background: #c82333;
        }

        .clear-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }
        .clear-btn:hover {
            background: #5a6268;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .button-group button {
            flex: 1;
            margin-top: 0;
        }
        #result {
            width: 100%;
            height: 400px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            background: #f8f9fa;
            resize: vertical;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }
        .status.running {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.idle {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .time-display {
            margin: 10px 0;
            padding: 8px;
            background: #e9ecef;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            color: #495057;
        }

        .vulnerability-details {
            margin-top: 20px;
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .vulnerability-details h3 {
            color: #dc3545;
            margin-bottom: 20px;
            font-size: 18px;
        }

        .vulnerability-item {
            margin-bottom: 20px;
            padding: 15px;
            border-left: 4px solid #dc3545;
            background: #f8f9fa;
            border-radius: 0 5px 5px 0;
        }

        .vulnerability-item.suspected {
            border-left-color: #ffc107;
        }

        .vulnerability-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .vulnerability-type {
            font-weight: bold;
            color: #dc3545;
            font-size: 16px;
        }

        .vulnerability-type.suspected {
            color: #ffc107;
        }

        .vulnerability-time {
            font-size: 12px;
            color: #666;
        }

        .vulnerability-payload {
            background: #2d3748;
            color: #e2e8f0;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
            max-height: 100px;
            overflow-y: auto;
        }

        .vulnerability-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 10px;
        }

        .status-200 {
            background: #d4edda;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .vulnerability-description {
            margin-top: 10px;
            font-size: 14px;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>渗透测试智能化检测工具</h1>
        
        <div class="form-group">
            <label for="target-url">目标URL:</label>
            <input type="url" id="target-url" placeholder="请输入目标URL，例如：http://localhost/DVWA">
        </div>

        <div class="form-group">
            <label for="vuln-dropdown">漏洞检测类型 (OWASP Top 10):</label>
            <div class="dropdown-container">
                <div class="dropdown-header" onclick="toggleDropdown()">
                    <span id="selected-count">已选择 1 项</span>
                    <span class="dropdown-arrow">▼</span>
                </div>
                <div class="dropdown-content" id="dropdown-content">
                    <div class="checkbox-item">
                        <input type="checkbox" id="sql-injection" value="sql-injection" checked onchange="updateSelection()">
                        <label for="sql-injection">1. SQL注入</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="ssrf" value="ssrf" onchange="updateSelection()">
                        <label for="ssrf">2. SSRF服务端请求伪造</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="csrf" value="csrf" onchange="updateSelection()">
                        <label for="csrf">3. 跨站请求伪造(CSRF)</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="file-upload" value="file-upload" onchange="updateSelection()">
                        <label for="file-upload">4. 文件上传漏洞</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="command-injection" value="command-injection" onchange="updateSelection()">
                        <label for="command-injection">5. 命令注入</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="path-traversal" value="path-traversal" onchange="updateSelection()">
                        <label for="path-traversal">6. 路径遍历</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="xxe" value="xxe" onchange="updateSelection()">
                        <label for="xxe">7. XML外部实体注入(XXE)</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="broken-auth" value="broken-auth" onchange="updateSelection()">
                        <label for="broken-auth">8. 身份验证失效</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="sensitive-data" value="sensitive-data" onchange="updateSelection()">
                        <label for="sensitive-data">9. 敏感数据泄露</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="xxs" value="xxs" onchange="updateSelection()">
                        <label for="xxs">10. 跨站脚本攻击(XSS)</label>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <button class="start-btn" id="start-btn" onclick="startDetection()">🚀 开始检测</button>
            <div class="button-group" id="control-buttons" style="display: none;">
                <button class="stop-btn" id="stop-btn" onclick="stopDetection()">⏹️ 停止检测</button>
                <button class="clear-btn" id="clear-btn" onclick="clearResult()">🗑️ 清空结果</button>
            </div>
        </div>

        <div id="status" class="status idle">等待开始检测</div>
        <div id="time-display" class="time-display">耗时: 0分0秒</div>

        <div class="form-group">
            <label for="result">检测结果:</label>
            <textarea id="result" readonly placeholder="检测结果将在这里显示..."></textarea>
        </div>

        <!-- 漏洞详情显示区域 -->
        <div id="vulnerability-details" class="vulnerability-details" style="display: none;">
            <h3>🚨 检测到的漏洞详情</h3>
            <div id="vulnerability-list"></div>
        </div>
    </div>

    <script>
        let isRunning = false;
        let resultInterval;

        function toggleDropdown() {
            const dropdownContent = document.getElementById('dropdown-content');
            const arrow = document.querySelector('.dropdown-arrow');

            if (dropdownContent.classList.contains('show')) {
                dropdownContent.classList.remove('show');
                arrow.classList.remove('open');
            } else {
                dropdownContent.classList.add('show');
                arrow.classList.add('open');
            }
        }

        function updateSelection() {
            const checkboxes = document.querySelectorAll('.checkbox-item input[type="checkbox"]:checked');
            const count = checkboxes.length;
            const selectedCount = document.getElementById('selected-count');

            if (count === 0) {
                selectedCount.textContent = '请选择漏洞类型';
            } else if (count === 1) {
                selectedCount.textContent = `已选择 ${count} 项`;
            } else {
                selectedCount.textContent = `已选择 ${count} 项`;
            }
        }

        // 点击外部关闭下拉框
        document.addEventListener('click', function(event) {
            const dropdownContainer = document.querySelector('.dropdown-container');
            if (!dropdownContainer.contains(event.target)) {
                const dropdownContent = document.getElementById('dropdown-content');
                const arrow = document.querySelector('.dropdown-arrow');
                dropdownContent.classList.remove('show');
                arrow.classList.remove('open');
            }
        });

        function updateStatus(text, isRunning) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = text;
            statusDiv.className = isRunning ? 'status running' : 'status idle';
        }

        function startDetection() {
            const targetUrl = document.getElementById('target-url').value.trim();

            if (!targetUrl) {
                alert('请输入目标URL');
                return;
            }

            // 获取选中的漏洞类型
            const selectedVulns = [];
            const checkboxes = document.querySelectorAll('.checkbox-item input[type="checkbox"]:checked');
            checkboxes.forEach(checkbox => {
                selectedVulns.push(checkbox.value);
            });

            if (selectedVulns.length === 0) {
                alert('请至少选择一种漏洞检测类型');
                return;
            }

            if (isRunning) {
                alert('检测正在进行中');
                return;
            }

            // 发送检测请求
            fetch('/start_detection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    target_url: targetUrl,
                    vulnerability_types: selectedVulns
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    isRunning = true;
                    document.getElementById('start-btn').style.display = 'none';
                    document.getElementById('control-buttons').style.display = 'flex';
                    updateStatus('检测正在进行中...', true);
                    document.getElementById('result').value = '';

                    // 开始轮询结果 (每1秒更新一次，更及时)
                    resultInterval = setInterval(getResult, 1000);
                } else {
                    alert('启动失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('请求失败: ' + error);
            });
        }

        function stopDetection() {
            if (!isRunning) {
                alert('没有正在运行的检测');
                return;
            }

            if (confirm('确定要停止当前检测吗？')) {
                fetch('/stop_detection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        isRunning = false;
                        document.getElementById('start-btn').style.display = 'block';
                        document.getElementById('control-buttons').style.display = 'none';
                        updateStatus('检测已停止', false);
                        clearInterval(resultInterval);

                        // 获取最终结果
                        getResult();
                    } else {
                        alert('停止失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('停止请求失败: ' + error);
                });
            }
        }

        function clearResult() {
            if (isRunning) {
                alert('检测正在进行中，无法清空结果');
                return;
            }

            if (!confirm('确定要清空检测结果吗？')) {
                return;
            }

            fetch('/clear_result', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('result').value = '';
                    updateStatus('结果已清空', false);
                } else {
                    alert('清空失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('清空请求失败: ' + error);
            });
        }

        function getResult() {
            fetch('/get_result')
            .then(response => response.json())
            .then(data => {
                const resultTextarea = document.getElementById('result');
                resultTextarea.value = data.result;
                // 自动滚动到底部
                resultTextarea.scrollTop = resultTextarea.scrollHeight;

                // 更新耗时显示
                const timeDisplay = document.getElementById('time-display');
                if (data.is_running && data.current_time) {
                    timeDisplay.textContent = '耗时: ' + data.current_time;
                } else if (!data.is_running) {
                    timeDisplay.textContent = '耗时: 0分0秒';
                }

                // 显示漏洞详情
                if (data.vulnerabilities && data.vulnerabilities.length > 0) {
                    displayVulnerabilities(data.vulnerabilities);
                }

                if (!data.is_running && isRunning) {
                    // 检测完成
                    isRunning = false;
                    document.getElementById('start-btn').style.display = 'block';
                    document.getElementById('control-buttons').style.display = 'none';
                    updateStatus('检测完成', false);
                    clearInterval(resultInterval);
                }
            })
            .catch(error => {
                console.error('获取结果失败:', error);
            });
        }

        function displayVulnerabilities(vulnerabilities) {
            const detailsContainer = document.getElementById('vulnerability-details');
            const listContainer = document.getElementById('vulnerability-list');

            if (vulnerabilities.length === 0) {
                detailsContainer.style.display = 'none';
                return;
            }

            let html = '';
            vulnerabilities.forEach((vuln, index) => {
                const isSuspected = vuln.type === '疑似漏洞';
                const statusClass = vuln.status_code === '200' ? 'status-200' : 'status-error';

                html += `
                    <div class="vulnerability-item ${isSuspected ? 'suspected' : ''}">
                        <div class="vulnerability-header">
                            <span class="vulnerability-type ${isSuspected ? 'suspected' : ''}">
                                ${isSuspected ? '⚠️' : '🚨'} ${vuln.type}
                            </span>
                            <div>
                                <span class="vulnerability-status ${statusClass}">
                                    状态码: ${vuln.status_code}
                                </span>
                                <span class="vulnerability-time">${vuln.time}</span>
                            </div>
                        </div>
                        <div class="vulnerability-payload">
                            <strong>检测载荷:</strong><br>
                            ${vuln.payload}
                        </div>
                        ${vuln.description ? `
                            <div class="vulnerability-description">
                                <strong>漏洞描述:</strong> ${vuln.description}
                            </div>
                        ` : ''}
                    </div>
                `;
            });

            listContainer.innerHTML = html;
            detailsContainer.style.display = 'block';
        }
    </script>
</body>
</html>
