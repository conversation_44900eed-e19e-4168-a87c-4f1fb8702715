#!/usr/bin/env python3
"""
完美的AI驱动SSRF检测系统 - 按照用户要求完全重构
"""

import uuid
from collections import defaultdict
import random
from http import HTTPStatus
from openai import OpenAI
import requests
import subprocess
import os
import time
import re
import json
from urllib.parse import urlparse
import urllib3
import sys

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 全局配置
NMAP_PATH = r"D:\nmap\nmap.exe"
COMMAND_COUNT = 3  # 每类测试用例数量
SCAN_STRATEGY_COUNT = 3  # 使用的扫描策略数量
start = 30474
end = 31395
# start = 0
# end = 10000
# 硅基流动API
SILICONFLOW_API_KEY = "sk-zaidnpaavbsjycrhkacrvnytnojtlcvtxzrbrfswaolyoiyz"
SILICONFLOW_BASE_URL = "https://api.siliconflow.cn/v1"
MODEL_NAME = "deepseek-ai/DeepSeek-V3"

client = OpenAI(
    api_key=SILICONFLOW_API_KEY,
    base_url=SILICONFLOW_BASE_URL
)

def call_siliconflow_api(prompt, max_retries=COMMAND_COUNT):
    """调用硅基流动API (OpenAI兼容接口)"""
    for attempt in range(max_retries):
        try:
            response = client.chat.completions.create(
                model=MODEL_NAME,
                messages=[
                    {"role": "system", "content": "你是一个专业的网络安全专家，长期从事于渗透测试行业，致力于维护网络安全"},
                    {"role": "user", "content": prompt}
                ],
                temperature=1.2,
                max_tokens=2000,
                stream=False
            )
            return response.choices[0].message.content

        except Exception as e:
            print(f"API调用异常 (尝试 {attempt + 1}/{max_retries}): {e}")  #重试3次
            if attempt < max_retries - 1:
                time.sleep(2)

    return None

def get_nmap_strategies():
    """获取大模型生成的扫描策略"""
    prompt = f"""作为安全专家，请为网站端口扫描生成{SCAN_STRATEGY_COUNT}个最优nmap扫描策略（仅返回扫描命令参数，不要解释）。

要求：
1. 每个策略一行
2. 可以使用任何有效的nmap参数组合
3. 可以包含TCP扫描、服务识别、操作系统识别等
4. 不要包含nmap命令本身，只返回参数

示例输出格式：
-sS -T4 -p 80,443,3306,8080
"""

    response = call_siliconflow_api(prompt)
    if not response:
        return None

    strategies = [s.strip() for s in response.split('\n') if s.strip()]
    return strategies[:SCAN_STRATEGY_COUNT]

def analyze_with_ai(context, data, analysis_type):
    """通用AI分析函数"""
    prompt = f"""作为网络安全专家，请分析以下{analysis_type}数据并给出专业判断：

{context}
{data}

请按以下格式回答：
1. 发现的安全问题: [具体问题描述](请具体一些，根据获取的数据分析）
2. 风险等级: [高危/中危/低危/无风险] (高危=确认漏洞，中危=确认漏洞，低危=疑似漏洞，无风险=安全)
3. 建议措施: [简明建议]
4. 漏洞状态: [确认漏洞/疑似漏洞/安全] (必须与风险等级对应)

注意：风险等级和漏洞状态必须逻辑一致！不要做任何解释，以以上格式返回。"""

    return call_siliconflow_api(prompt)

def determine_vuln_status(analysis):
    """从AI分析结果中提取漏洞状态"""
    if not analysis:
        return 'unknown'

    # 优先匹配"漏洞状态:"
    vuln_match = re.search(r"漏洞状态:\s*(\S+)", analysis, re.IGNORECASE)
    if vuln_match:
        value = vuln_match.group(1)
        if "确认漏洞" in value:
            return "confirmed"
        elif "疑似漏洞" in value:
            return "suspected"
        elif "安全" in value:
            return "safe"

    # 其次匹配"风险等级:"
    risk_match = re.search(r"风险等级:\s*(\S+)", analysis, re.IGNORECASE)
    if risk_match:
        value = risk_match.group(1)
        if "高危" in value:
            return "confirmed"
        elif "中危" in value:
            return "suspected"
        elif "低危" in value or "无风险" in value:
            return "safe"

    return 'unknown'

def parse_nmap_ports(nmap_output):
    """解析Nmap输出，提取开放端口"""
    open_ports = []
    port_pattern = re.compile(r'(\d+)/(tcp|udp)\s+open\s+(\S+)')
    for line in nmap_output.split('\n'):
        match = port_pattern.search(line)
        if match:
            port, protocol, service = match.groups()
            open_ports.append((port, protocol, service.lower()))  # 服务名称转为小写
    return open_ports

def select_critical_ports(open_ports, max_ports=5):
    """选择最重要的5个端口"""
    if not open_ports:
        return []

    # 将所有开放端口发送给大模型进行评估
    prompt = f"""作为网络安全专家，请根据以下开放端口列表，评估并选择5个最重要的端口用于进一步的安全测试。
    开放端口列表：{open_ports}
    
    请按重要性排序返回这5个端口，并说明每个端口的重要原因。重要性评估应考虑：
    1. 端口对应的服务类型
    2. 服务的潜在安全风险
    3. 常见漏洞利用可能性
    4. 对系统整体安全的影响
    
    返回格式为JSON对象，包含端口信息和重要性原因：
    {{
        "selected_ports": [
            {{
                "port": "端口号",
                "protocol": "协议类型",
                "service": "服务名称",
                "reason": "重要性原因"
            }}
        ]
    }}"""
    # 调用大模型获取最重要的5个端口
    response = call_siliconflow_api(prompt)
    if not response:
        return []

    try:
        # 解析大模型响应
        json_str = re.search(r'\{.*\}', response, re.DOTALL)
        if not json_str:
            return []
        
        result = json.loads(json_str.group(0))

        # 提取选择的端口信息
        selected_ports = []
        for port_info in result.get('selected_ports', [])[:5]:
            selected_ports.append((
                port_info['port'],
                port_info['protocol'],
                port_info['service']
            ))

        return selected_ports
    except:
        return []

def generate_tests_for_port(target_server, port_info, existing_tests=None):
    """为单个端口生成2个SSRF测试载荷"""
    if existing_tests is None:
        existing_tests = set()

    port, protocol, service = port_info
    prompt = f"""作为安全专家，请为端口{port}/{protocol}({service})生成2个SSRF攻击载荷。

目标服务器：{target_server}
端口：{port}/{protocol} - 服务: {service}

要求：
1. 生成2个完整的SSRF攻击URL
2. 使用常见参数名：url
3. 针对端口{port}的{service}服务设计攻击载荷
4. 支持多种协议：http、file等
5. 载荷格式：{target_server}?参数名=协议://目标

返回JSON：
{{
    "payloads": [
        "{target_server}?url=http://127.0.0.1:{port}"
    ]
}}"""

    response = call_siliconflow_api(prompt)
    if not response:
        return [], existing_tests

    try:
        json_str = re.search(r'\{.*\}', response, re.DOTALL)
        if json_str:
            result = json.loads(json_str.group(0))
            payloads = result.get('payloads', [])

            # 过滤重复载荷
            filtered_payloads = []
            for payload in payloads:
                if payload not in existing_tests:
                    filtered_payloads.append(payload)
                    existing_tests.add(payload)

            return filtered_payloads, existing_tests
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")

    return [], existing_tests

def generate_general_ssrf_tests(target_server, existing_tests=None):
    """生成20个通用SSRF测试载荷"""
    if existing_tests is None:
        existing_tests = set()

    prompt = f"""作为安全专家，请为目标服务器生成20个通用SSRF攻击载荷。

目标服务器：{target_server}

要求：
1. 生成20个不同的SSRF攻击载荷
2. 使用常见参数名：url
3. 包含多种协议：file、http
4. 包含windows系统盘文件读取、内网探测、数据库探测等
5. 载荷格式：{target_server}?参数名=协议://目标路径

示例格式：
{target_server}?url=file:///C:/Windows/System32/drivers/etc/hosts


返回JSON：
{{
    "payloads": [
        "完整URL1",
        "完整URL2",
        "..."
    ]
}}"""

    response = call_siliconflow_api(prompt)
    if not response:
        return [], existing_tests

    try:
        json_str = re.search(r'\{.*\}', response, re.DOTALL)
        if json_str:
            result = json.loads(json_str.group(0))
            payloads = result.get('payloads', [])

            # 过滤重复载荷
            filtered_payloads = []
            for payload in payloads:
                if payload not in existing_tests:
                    filtered_payloads.append(payload)
                    existing_tests.add(payload)

            return filtered_payloads, existing_tests
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")

    return [], existing_tests

def run_nmap_scan(target, scan_args):
    """执行nmap扫描并返回结果"""
    parsed = urlparse(target)
    target_host = parsed.netloc.split(':')[0] if parsed.netloc else target
    cmd = [NMAP_PATH] + scan_args.split() + [target_host]
    print(f"执行nmap扫描: {' '.join(cmd)}")

    try:
        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=300
        )

        if result.returncode != 0:
            print(f"nmap扫描错误: {result.stderr}")
            return None

        return {
            'raw_output': result.stdout,
            'target': target_host,
            'command': ' '.join(cmd)
        }
    except Exception as e:
        print(f"nmap执行失败: {e}")
        return None

def execute_tests_for_port(port_info, test_stats, existing_tests, target_server):
    """为单个端口执行测试"""
    port, protocol, service = port_info
    print(f"\n=== 为端口 {port}/{protocol}({service}) 生成测试 ===")

    payloads, existing_tests = generate_tests_for_port(target_server, port_info, existing_tests)
    if not payloads:
        print(f"⚠️ 未能为端口 {port}/{protocol} 生成测试")
        return existing_tests

    test_type = f"端口{port}/{protocol}-{service}"
    print(f"\n■ 测试类型: {test_type}")
    test_stats['vulnerabilities'][test_type] = []

    for payload in payloads:
        try:
            test_stats['executed_tests'] += 1
            print(f"\n测试用例: {payload}")
            response = send_request(payload)
            if not response:
                continue

            ai_analysis = analyze_with_ai(
                context=f"端口 {port}/{protocol}({service}) 测试结果分析",
                data=f"状态码: {response.status_code}\n响应内容: {response.text[start:end]}",
                analysis_type="SSRF攻击"
            )

            vuln_status = determine_vuln_status(ai_analysis)
            test_stats['vulnerabilities'][test_type].append({
                'test_case': payload,
                'status_code': response.status_code,
                'response': response.text[start:end] or "[空响应]",
                'vulnerability_status': vuln_status,
                'analysis': ai_analysis or "AI分析失败"
            })

        except Exception as e:
            print(f"测试执行出错: {e}")

    return existing_tests

def send_request(url, headers=None, allow_redirects=False):
    """发送HTTP请求"""
    try:
        response = requests.get(
            url,
            headers=headers,
            verify=False,
            timeout=60,
            allow_redirects=allow_redirects
        )
        print(f"[+] 请求成功: {url}")
        print(f"状态码: {response.status_code}")
        print(f"响应片段: {response.text[start:end]}")
        return response
    except Exception as e:
        print(f"[-] 请求失败: {e}")
        return None

def generate_report(test_stats):
    """生成综合漏洞报告"""
    print("\n" + "=" * 50)
    print("=== 综合安全检测报告 ===")
    print("=" * 50 + "\n")

    print(f"📊 执行测试总数: {test_stats['executed_tests']}")

    stats = defaultdict(int)
    for vulns in test_stats['vulnerabilities'].values():
        for v in vulns:
            status = v.get('vulnerability_status', 'unknown')
            if status not in ['confirmed', 'suspected', 'safe']:
                status = 'unknown'
            stats[status] += 1

    print(f"✅ 确认漏洞: {stats['confirmed']} | ⚠️ 疑似漏洞: {stats['suspected']} | 🔒 安全: {stats['safe']} | ❓ 未知: {stats.get('unknown', 0)}")

    print("\n🔍 测试结果详情:")
    for test_type, vulns in test_stats['vulnerabilities'].items():
        if not vulns:
            continue
        print(f"\n■ {test_type.upper()} 结果汇总:")
        for i, v in enumerate(vulns, 1):
            status_map = {
                'confirmed': '✅ 确认漏洞',
                'suspected': '⚠️ 疑似漏洞',
                'safe': '🔒 安全',
                'unknown': '❓ 未知'
            }
            status = status_map.get(v['vulnerability_status'], '❓ 未知')
            print(f"\n[{i}] {status}")
            print(f"   测试用例: {v['test_case']}")
            print(f"   状态码: {v['status_code']}")
            print("\n   AI分析结果:")
            for line in str(v['analysis']).split('\n'):
                print(f"   {line}")
            print("-" * 60)

def main():
    if len(sys.argv) != 2:
        print("用法: python perfect_ssrf_detector.py <目标URL>")
        print("示例: python perfect_ssrf_detector.py http://localhost:5002")
        return

    target_server = sys.argv[1]

    print("=== 基于大模型的SSRF漏洞检测 ===")
    print(f"目标服务器: {target_server}")
    print(f"nmap路径: {NMAP_PATH}")

    if not os.path.exists(NMAP_PATH):
        print(f"⚠️ 错误: 未在{NMAP_PATH}找到nmap，程序终止")
        return

    test_stats = {
        'executed_tests': 0,
        'vulnerabilities': defaultdict(list)
    }

    # 获取大模型生成的扫描策略
    print("\n=== 获取扫描策略 ===")
    strategies = get_nmap_strategies()
    if not strategies:
        print("⚠️ 未能获取扫描策略，但继续检测...")
        strategies = []

    # 打印扫描策略
    print("\n=== 生成的扫描策略 ===")
    for i, strategy in enumerate(strategies, 1):
        print(f"策略{i}: nmap {strategy}")

    # 执行多个扫描策略并合并结果
    all_open_ports = set()
    for strategy in strategies:
        print(f"\n=== 执行扫描策略: nmap {strategy} ===")
        scan_result = run_nmap_scan(target_server, strategy)
        if not scan_result:
            continue

        open_ports = parse_nmap_ports(scan_result['raw_output'])
        for port_info in open_ports:
            all_open_ports.add(port_info)

    # 打印所有开放端口
    print("\n=== 发现开放端口 ===")
    if all_open_ports:
        for port, protocol, service in all_open_ports:
            print(f"端口 {port}/{protocol} - 服务: {service}")

        # 选择关键端口
        critical_ports = select_critical_ports(list(all_open_ports))
        print("\n=== 选择的关键端口 ===")
        for port, protocol, service in critical_ports:
            print(f"端口 {port}/{protocol} - 服务: {service}")
    else:
        print("未发现开放端口，但继续进行SSRF检测...")
        critical_ports = []

    # 为每个关键端口生成并执行测试（每个端口2个策略）
    existing_tests = set()
    for port_info in critical_ports:
        existing_tests = execute_tests_for_port(port_info, test_stats, existing_tests, target_server)

    # 生成20个通用SSRF测试
    print("\n=== 生成SSRF测试 ===")
    general_payloads, existing_tests = generate_general_ssrf_tests(target_server, existing_tests)

    if general_payloads:
        test_type = "SSRF测试"
        print(f"\n■ 测试类型: {test_type}")
        test_stats['vulnerabilities'][test_type] = []

        for payload in general_payloads:
            try:
                test_stats['executed_tests'] += 1
                print(f"\n测试用例: {payload}")
                response = send_request(payload)
                if not response:
                    continue

                ai_analysis = analyze_with_ai(
                    context=f"SSRF测试结果分析",
                    data=f"测试载荷: {payload}\n状态码: {response.status_code}\n响应内容: {response.text[start:end]}",
                    analysis_type="SSRF攻击"
                )

                vuln_status = determine_vuln_status(ai_analysis)
                test_stats['vulnerabilities'][test_type].append({
                    'test_case': payload,
                    'status_code': response.status_code,
                    'response': response.text[start:end] or "[空响应]",
                    'vulnerability_status': vuln_status,
                    'analysis': ai_analysis or "AI分析失败"
                })

            except Exception as e:
                print(f"测试执行出错: {e}")
    else:
        print("⚠️ 未能生成通用SSRF测试，但检测继续...")

    # 生成报告
    generate_report(test_stats)

    print("\n检测完成！建议：")
    print("1. 对'确认漏洞'项立即进行修复")
    print("2. 对'疑似漏洞'项进行人工验证")

if __name__ == "__main__":
    main()
