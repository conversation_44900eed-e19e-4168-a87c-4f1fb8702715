<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE nmaprun>
<?xml-stylesheet href="file:///D:/nmap/nmap.xsl" type="text/xsl"?>
<!-- Nmap 7.92 scan initiated Tue Aug  5 11:30:30 2025 as: D:\\nmap\\nmap.exe -sS -A -T4 -p1-65535 -v -&#45;open -oA full_scan localhost -->
<nmaprun scanner="nmap" args="D:\\nmap\\nmap.exe -sS -A -T4 -p1-65535 -v -&#45;open -oA full_scan localhost" start="1754364630" startstr="Tue Aug  5 11:30:30 2025" version="7.92" xmloutputversion="1.05">
<scaninfo type="syn" protocol="tcp" numservices="65535" services="1-65535"/>
<verbose level="1"/>
<debugging level="0"/>
<taskbegin task="NSE" time="1754364631"/>
<taskend task="NSE" time="1754364631"/>
<taskbegin task="NSE" time="1754364631"/>
<taskend task="NSE" time="1754364631"/>
<taskbegin task="NSE" time="1754364631"/>
<taskend task="NSE" time="1754364631"/>
<taskbegin task="SYN Stealth Scan" time="1754364634"/>
<taskend task="SYN Stealth Scan" time="1754364641" extrainfo="65535 total ports"/>
<taskbegin task="Service scan" time="1754364641"/>
<taskprogress task="Service scan" time="1754364672" percent="45.45" remaining="38" etc="1754364709"/>
<taskprogress task="Service scan" time="1754364720" percent="68.18" remaining="37" etc="1754364757"/>
<taskend task="Service scan" time="1754364809" extrainfo="44 services on 1 host"/>
<taskbegin task="NSE" time="1754364819"/>
<taskend task="NSE" time="1754364894"/>
<taskbegin task="NSE" time="1754364894"/>
<taskend task="NSE" time="1754364925"/>
<taskbegin task="NSE" time="1754364925"/>
<taskend task="NSE" time="1754364925"/>
<host starttime="1754364634" endtime="1754364925"><status state="up" reason="localhost-response" reason_ttl="0"/>
<address addr="127.0.0.1" addrtype="ipv4"/>
<hostnames>
<hostname name="localhost" type="user"/>
<hostname name="kubernetes.docker.internal" type="PTR"/>
</hostnames>
<ports><extraports state="closed" count="65490">
<extrareasons reason="reset" count="65490" proto="tcp" ports="1-79,81-134,136,138-444,446-901,903-911,913-2178,2180-2868,2870-3305,3307-3503,3505-3517,3519-3522,3524-3555,3557-4300,4302-4309,4311-4708,4710-4999,5001-5039,5041-5282,5285-5353,5355-6187,6190-7896,7898-8085,8087-9096,9098-9209,9211-9999,10001-11199,11201-11433,11435-16421,16423-20713,20715-28316,28318-33330,33332-36509,36511-37509,37511-49663,49669-49728,49730-50914,50916-53165,53167-63341,63343-65535"/>
</extraports>
<extraports state="filtered" count="1">
<extrareasons reason="no-response" count="1" proto="tcp" ports="137"/>
</extraports>
<port protocol="tcp" portid="80"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="http" product="Apache httpd" version="2.4.39" extrainfo="(Win64) OpenSSL/1.1.1b mod_fcgid/2.3.9a mod_log_rotate/1.02" method="probed" conf="10"><cpe>cpe:/a:apache:http_server:2.4.39</cpe></service><script id="http-server-header" output="Apache/2.4.39 (Win64) OpenSSL/1.1.1b mod_fcgid/2.3.9a mod_log_rotate/1.02"><elem>Apache/2.4.39 (Win64) OpenSSL/1.1.1b mod_fcgid/2.3.9a mod_log_rotate/1.02</elem>
</script><script id="http-title" output="\xE7\xAB\x99\xE7\x82\xB9\xE5\x88\x9B\xE5\xBB\xBA\xE6\x88\x90\xE5\x8A\x9F-phpstudy for windows"><elem key="title">\xE7\xAB\x99\xE7\x82\xB9\xE5\x88\x9B\xE5\xBB\xBA\xE6\x88\x90\xE5\x8A\x9F-phpstudy for windows</elem>
</script><script id="http-methods" output="&#xa;  Supported Methods: HEAD GET POST OPTIONS TRACE&#xa;  Potentially risky methods: TRACE"><table key="Supported Methods">
<elem>HEAD</elem>
<elem>GET</elem>
<elem>POST</elem>
<elem>OPTIONS</elem>
<elem>TRACE</elem>
</table>
<table key="Potentially risky methods">
<elem>TRACE</elem>
</table>
</script></port>
<port protocol="tcp" portid="135"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="msrpc" product="Microsoft Windows RPC" ostype="Windows" method="probed" conf="10"><cpe>cpe:/o:microsoft:windows</cpe></service></port>
<port protocol="tcp" portid="445"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="microsoft-ds" method="table" conf="3"/></port>
<port protocol="tcp" portid="902"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="vmware-auth" product="VMware Authentication Daemon" version="1.10" extrainfo="Uses VNC, SOAP" tunnel="ssl" method="probed" conf="10"/></port>
<port protocol="tcp" portid="912"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="vmware-auth" product="VMware Authentication Daemon" version="1.0" extrainfo="Uses VNC, SOAP" method="probed" conf="10"/></port>
<port protocol="tcp" portid="2179"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="vmrdp" method="table" conf="3"/></port>
<port protocol="tcp" portid="2869"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="http" product="Microsoft HTTPAPI httpd" version="2.0" extrainfo="SSDP/UPnP" ostype="Windows" method="probed" conf="10"><cpe>cpe:/o:microsoft:windows</cpe></service></port>
<port protocol="tcp" portid="3306"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="mysql" product="MySQL" version="5.7.26" method="probed" conf="10"><cpe>cpe:/a:mysql:mysql:5.7.26</cpe></service><script id="mysql-info" output="&#xa;  Protocol: 10&#xa;  Version: 5.7.26&#xa;  Thread ID: 4&#xa;  Capabilities flags: 63487&#xa;  Some Capabilities: InteractiveClient, SupportsLoadDataLocal, Speaks41ProtocolOld, LongPassword, ConnectWithDatabase, Support41Auth, SupportsCompression, SupportsTransactions, DontAllowDatabaseTableColumn, IgnoreSigpipes, IgnoreSpaceBeforeParenthesis, Speaks41ProtocolNew, ODBCClient, FoundRows, LongColumnFlag, SupportsMultipleStatments, SupportsAuthPlugins, SupportsMultipleResults&#xa;  Status: Autocommit&#xa;  Salt: &#xd;GNX]8k\x19`,b:&#xa;Y\x14Wh\x02K;&#xa;  Auth Plugin Name: mysql_native_password"><elem key="Protocol">10</elem>
<elem key="Version">5.7.26</elem>
<elem key="Thread ID">4</elem>
<elem key="Capabilities flags">63487</elem>
<table key="Some Capabilities">
<elem>InteractiveClient</elem>
<elem>SupportsLoadDataLocal</elem>
<elem>Speaks41ProtocolOld</elem>
<elem>LongPassword</elem>
<elem>ConnectWithDatabase</elem>
<elem>Support41Auth</elem>
<elem>SupportsCompression</elem>
<elem>SupportsTransactions</elem>
<elem>DontAllowDatabaseTableColumn</elem>
<elem>IgnoreSigpipes</elem>
<elem>IgnoreSpaceBeforeParenthesis</elem>
<elem>Speaks41ProtocolNew</elem>
<elem>ODBCClient</elem>
<elem>FoundRows</elem>
<elem>LongColumnFlag</elem>
<elem>SupportsMultipleStatments</elem>
<elem>SupportsAuthPlugins</elem>
<elem>SupportsMultipleResults</elem>
</table>
<elem key="Status">Autocommit</elem>
<elem key="Salt">&#xd;GNX]8k\x19`,b:&#xa;Y\x14Wh\x02K;</elem>
<elem key="Auth Plugin Name">mysql_native_password</elem>
</script></port>
<port protocol="tcp" portid="3504"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="http" product="Octoshape P2P streaming web service" method="probed" conf="10"/><script id="http-title" output="Site doesn&apos;t have a title."></script></port>
<port protocol="tcp" portid="3518"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="artifact-msg" servicefp="SF-Port3518-TCP:V=7.92%I=7%D=8/5%Time=68917AEC%P=i686-pc-windows-windows%r(GetRequest,BE,&quot;HTTP/1\.0\x20404\x20Not\x20Found\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nVary:\x20Origin\r\nX-Content-Type-Options:\x20nosniff\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\nContent-Length:\x2019\r\n\r\n404\x20page\x20not\x20found\n&quot;)%r(HTTPOptions,BE,&quot;HTTP/1\.0\x20404\x20Not\x20Found\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nVary:\x20Origin\r\nX-Content-Type-Options:\x20nosniff\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\nContent-Length:\x2019\r\n\r\n404\x20page\x20not\x20found\n&quot;)%r(RTSPRequest,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(SSLSessionReq,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(TerminalServerCookie,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(TLSSessionReq,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(Kerberos,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(FourOhFourRequest,BE,&quot;HTTP/1\.0\x20404\x20Not\x20Found\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nVary:\x20Origin\r\nX-Content-Type-Options:\x20nosniff\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:31:25\x20GMT\r\nContent-Length:\x2019\r\n\r\n404\x20page\x20not\x20found\n&quot;)%r(LDAPSearchReq,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(SIPOptions,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;);" method="table" conf="3"/><script id="fingerprint-strings" output="&#xa;  FourOhFourRequest: &#xa;    HTTP/1.0 404 Not Found&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Vary: Origin&#xa;    X-Content-Type-Options: nosniff&#xa;    Date: Tue, 05 Aug 2025 03:31:25 GMT&#xa;    Content-Length: 19&#xa;    page not found&#xa;  GetRequest, HTTPOptions: &#xa;    HTTP/1.0 404 Not Found&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Vary: Origin&#xa;    X-Content-Type-Options: nosniff&#xa;    Date: Tue, 05 Aug 2025 03:30:52 GMT&#xa;    Content-Length: 19&#xa;    page not found&#xa;  Kerberos, LDAPSearchReq, RTSPRequest, SIPOptions, SSLSessionReq, TLSSessionReq, TerminalServerCookie: &#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Connection: close&#xa;    Request"><elem key="FourOhFourRequest">&#xa;    HTTP/1.0 404 Not Found&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Vary: Origin&#xa;    X-Content-Type-Options: nosniff&#xa;    Date: Tue, 05 Aug 2025 03:31:25 GMT&#xa;    Content-Length: 19&#xa;    page not found</elem>
<elem key="GetRequest, HTTPOptions">&#xa;    HTTP/1.0 404 Not Found&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Vary: Origin&#xa;    X-Content-Type-Options: nosniff&#xa;    Date: Tue, 05 Aug 2025 03:30:52 GMT&#xa;    Content-Length: 19&#xa;    page not found</elem>
<elem key="Kerberos, LDAPSearchReq, RTSPRequest, SIPOptions, SSLSessionReq, TLSSessionReq, TerminalServerCookie">&#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Connection: close&#xa;    Request</elem>
</script></port>
<port protocol="tcp" portid="3523"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="http" product="Golang net/http server" extrainfo="Go-IPFS json-rpc or InfluxDB API" method="probed" conf="10"><cpe>cpe:/a:protocol_labs:go-ipfs</cpe></service><script id="http-methods" output="&#xa;  Supported Methods: GET HEAD POST OPTIONS"><table key="Supported Methods">
<elem>GET</elem>
<elem>HEAD</elem>
<elem>POST</elem>
<elem>OPTIONS</elem>
</table>
</script><script id="http-favicon" output="Unknown favicon MD5: 575ADFEA2D8D1528A37C7863FF2F10BD"/><script id="http-title" output="Windsurf Chat"><elem key="title">Windsurf Chat</elem>
</script></port>
<port protocol="tcp" portid="3556"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="sky-transport" servicefp="SF-Port3556-TCP:V=7.92%I=7%D=8/5%Time=68917AE7%P=i686-pc-windows-windows%r(NULL,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(GenericLines,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(GetRequest,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(HTTPOptions,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(RTSPRequest,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(RPCCheck,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(DNSVersionBindReqTCP,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(DNSStatusRequestTCP,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(Help,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(SSLSessionReq,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(TerminalServerCookie,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(TLSSessionReq,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(Kerberos,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(SMBProgNeg,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(X11Probe,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;)%r(FourOhFourRequest,2E,&quot;\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01&quot;);" method="table" conf="3"/></port>
<port protocol="tcp" portid="4301"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="d-data" tunnel="ssl" method="table" conf="3"/><script id="ssl-cert" output="Subject: commonName=localhost.ptlogin2.qq.com/organizationName=Shenzhen Tencent Computer Systems Company Limited/stateOrProvinceName=Guangdong Province/countryName=CN&#xa;Subject Alternative Name: DNS:localhost.ptlogin2.qq.com, DNS:localhost.ptlogin2.tencent.com, DNS:localhost.ptlogin2.tenpay.com, DNS:localhost.ptlogin2.weiyun.com&#xa;Issuer: commonName=DigiCert Secure Site OV G2 TLS CN RSA4096 SHA256 2022 CA1/organizationName=DigiCert, Inc./countryName=US&#xa;Public Key type: rsa&#xa;Public Key bits: 2048&#xa;Signature Algorithm: sha256WithRSAEncryption&#xa;Not valid before: 2025-05-16T00:00:00&#xa;Not valid after:  2026-06-16T23:59:59&#xa;MD5:   431b 3ac6 15fc 3330 b0a6 9bc6 485e 4a5a&#xa;SHA-1: b87f fac8 3152 2e22 2c12 249b f5d7 bd3e 1678 f0bd"><table key="subject">
<elem key="commonName">localhost.ptlogin2.qq.com</elem>
<elem key="countryName">CN</elem>
<elem key="localityName">Shenzhen</elem>
<elem key="organizationName">Shenzhen Tencent Computer Systems Company Limited</elem>
<elem key="stateOrProvinceName">Guangdong Province</elem>
</table>
<table key="issuer">
<elem key="commonName">DigiCert Secure Site OV G2 TLS CN RSA4096 SHA256 2022 CA1</elem>
<elem key="countryName">US</elem>
<elem key="organizationName">DigiCert, Inc.</elem>
</table>
<table key="pubkey">
<elem key="type">rsa</elem>
<elem key="bits">2048</elem>
<elem key="modulus">C5549FAE83A6A76CF6960287055A3E25DE09E0B9B5D48D7B00898A5B012728051431581FA80206E8BEE47F63D07699E4E6E87DEEF4E5018906A7990890C42C3621DFE940C8FAB1FBCE7C78CBF2D8AB9CA7545C3C21B8A8302145A3A845A7DAC660C589E990DAF678C2A27DAB46719E6DE24F9A2D64680A7A19D007219904A6987211460344ECCF48685F5F5B7D3EC5B2C8769C5B26F3F32297DB7EEB97E906AABE6292A977F025357236CABC0197BB29D7C29F07B68F1C52EA20ECCCFFCEE4FA31145DC51282721136287F735D374C3FA9837E15A072476405B2ECF384F74383B3D5DC09E5113F58490DDFD70BA9CA27010408B72FEDE579ADD2F5CECAC16E0F</elem>
<elem key="exponent">65537</elem>
</table>
<table key="extensions">
<table>
<elem key="name">X509v3 Authority Key Identifier</elem>
<elem key="value">keyid:2B:23:16:81:1B:47:89:8A:90:7A:EC:E8:32:D4:6C:8E:72:F9:CE:25&#xa;</elem>
</table>
<table>
<elem key="name">X509v3 Subject Key Identifier</elem>
<elem key="value">9F:6A:34:6D:89:24:28:C7:93:7D:8E:EB:EF:22:86:B8:7B:21:12:AA</elem>
</table>
<table>
<elem key="name">X509v3 Subject Alternative Name</elem>
<elem key="value">DNS:localhost.ptlogin2.qq.com, DNS:localhost.ptlogin2.tencent.com, DNS:localhost.ptlogin2.tenpay.com, DNS:localhost.ptlogin2.weiyun.com</elem>
</table>
<table>
<elem key="name">X509v3 Certificate Policies</elem>
<elem key="value">Policy: **********.2.2&#xa;  CPS: http://www.digicert.com/CPS&#xa;</elem>
</table>
<table>
<elem key="name">X509v3 Key Usage</elem>
<elem key="value">Digital Signature, Key Encipherment</elem>
<elem key="critical">true</elem>
</table>
<table>
<elem key="name">X509v3 Extended Key Usage</elem>
<elem key="value">TLS Web Server Authentication, TLS Web Client Authentication</elem>
</table>
<table>
<elem key="name">X509v3 CRL Distribution Points</elem>
<elem key="value">&#xa;Full Name:&#xa;  URI:http://crl.digicert.cn/DigiCertSecureSiteOVG2TLSCNRSA4096SHA2562022CA1.crl&#xa;</elem>
</table>
<table>
<elem key="name">Authority Information Access</elem>
<elem key="value">OCSP - URI:http://ocsp.digicert.cn&#xa;CA Issuers - URI:http://cacerts.digicert.cn/DigiCertSecureSiteOVG2TLSCNRSA4096SHA2562022CA1.crt&#xa;</elem>
</table>
<table>
<elem key="name">X509v3 Basic Constraints</elem>
<elem key="value">CA:FALSE</elem>
<elem key="critical">true</elem>
</table>
<table>
<elem key="name">CT Precertificate SCTs</elem>
<elem key="value">Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : 0E:57:94:BC:F3:AE:A9:3E:33:1B:2C:99:07:B3:F7:90:&#xa;                DF:9B:C2:3D:71:32:25:DD:21:A9:25:AC:61:C5:4E:21&#xa;    Timestamp : May 16 07:43:47.041 2025 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:45:02:20:60:3D:8E:39:97:FC:AD:2C:20:AE:48:B0:&#xa;                BB:52:B6:5C:5C:21:48:92:82:61:38:BD:10:F0:B1:B9:&#xa;                40:57:A3:17:02:21:00:F7:66:94:04:BD:B6:09:A1:BC:&#xa;                BE:3E:6C:3A:82:52:2A:B7:B4:28:1F:02:CA:F6:7E:B3:&#xa;                99:F9:DC:FA:C7:70:56&#xa;Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : 64:11:C4:6C:A4:12:EC:A7:89:1C:A2:02:2E:00:BC:AB:&#xa;                4F:28:07:D4:1E:35:27:AB:EA:FE:D5:03:C9:7D:CD:F0&#xa;    Timestamp : May 16 07:43:47.087 2025 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:44:02:1F:4A:31:F4:70:4C:7E:C0:3D:C8:96:5D:80:&#xa;                4C:A9:AA:1C:D0:9E:C6:4D:3D:3E:0C:44:68:CC:56:B8:&#xa;                87:64:7B:02:21:00:CD:CD:0A:AC:7F:78:E2:56:E2:62:&#xa;                BF:DC:5D:A6:61:E2:FA:7C:AE:3C:2F:05:BB:05:A7:AE:&#xa;                09:69:C5:D5:72:E9&#xa;Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : 49:9C:9B:69:DE:1D:7C:EC:FC:36:DE:CD:87:64:A6:B8:&#xa;                5B:AF:0A:87:80:19:D1:55:52:FB:E9:EB:29:DD:F8:C3&#xa;    Timestamp : May 16 07:43:47.100 2025 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:46:02:21:00:BC:2E:96:37:90:B9:B1:E7:C3:21:B9:&#xa;                47:2F:DC:DD:43:E4:6A:E7:E9:0F:BE:BE:4F:AA:74:0F:&#xa;                99:64:42:CD:32:02:21:00:FF:3A:8B:EC:3B:35:BF:F8:&#xa;                F1:04:84:DC:7A:2E:D8:69:D3:02:DB:35:68:17:14:F7:&#xa;                C2:40:04:EA:02:E6:8F:B9</elem>
</table>
</table>
<elem key="sig_algo">sha256WithRSAEncryption</elem>
<table key="validity">
<elem key="notBefore">2025-05-16T00:00:00</elem>
<elem key="notAfter">2026-06-16T23:59:59</elem>
</table>
<elem key="md5">431b3ac615fc3330b0a69bc6485e4a5a</elem>
<elem key="sha1">b87ffac831522e222c12249bf5d7bd3e1678f0bd</elem>
<elem key="pem">-&#45;&#45;&#45;&#45;BEGIN CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;MIIITTCCBjWgAwIBAgIQBsaOEw+pe7gmor+3G0F51jANBgkqhkiG9w0BAQsFADBq&#xa;MQswCQYDVQQGEwJVUzEXMBUGA1UEChMORGlnaUNlcnQsIEluYy4xQjBABgNVBAMT&#xa;OURpZ2lDZXJ0IFNlY3VyZSBTaXRlIE9WIEcyIFRMUyBDTiBSU0E0MDk2IFNIQTI1&#xa;NiAyMDIyIENBMTAeFw0yNTA1MTYwMDAwMDBaFw0yNjA2MTYyMzU5NTlaMIGdMQsw&#xa;CQYDVQQGEwJDTjEbMBkGA1UECBMSR3Vhbmdkb25nIFByb3ZpbmNlMREwDwYDVQQH&#xa;EwhTaGVuemhlbjE6MDgGA1UEChMxU2hlbnpoZW4gVGVuY2VudCBDb21wdXRlciBT&#xa;eXN0ZW1zIENvbXBhbnkgTGltaXRlZDEiMCAGA1UEAxMZbG9jYWxob3N0LnB0bG9n&#xa;aW4yLnFxLmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAMVUn66D&#xa;pqds9pYChwVaPiXeCeC5tdSNewCJilsBJygFFDFYH6gCBui+5H9j0HaZ5Obofe70&#xa;5QGJBqeZCJDELDYh3+lAyPqx+858eMvy2Kucp1RcPCG4qDAhRaOoRafaxmDFiemQ&#xa;2vZ4wqJ9q0Zxnm3iT5otZGgKehnQByGZBKaYchFGA0Tsz0hoX19bfT7Fssh2nFsm&#xa;8/Mil9t+65fpBqq+YpKpd/AlNXI2yrwBl7sp18KfB7aPHFLqIOzM/87k+jEUXcUS&#xa;gnIRNih/c103TD+pg34VoHJHZAWy7POE90ODs9XcCeURP1hJDd/XC6nKJwEECLcv&#xa;7eV5rdL1zsrBbg8CAwEAAaOCA7kwggO1MB8GA1UdIwQYMBaAFCsjFoEbR4mKkHrs&#xa;6DLUbI5y+c4lMB0GA1UdDgQWBBSfajRtiSQox5N9juvvIoa4eyESqjCBggYDVR0R&#xa;BHsweYIZbG9jYWxob3N0LnB0bG9naW4yLnFxLmNvbYIebG9jYWxob3N0LnB0bG9n&#xa;aW4yLnRlbmNlbnQuY29tgh1sb2NhbGhvc3QucHRsb2dpbjIudGVucGF5LmNvbYId&#xa;bG9jYWxob3N0LnB0bG9naW4yLndlaXl1bi5jb20wPgYDVR0gBDcwNTAzBgZngQwB&#xa;AgIwKTAnBggrBgEFBQcCARYbaHR0cDovL3d3dy5kaWdpY2VydC5jb20vQ1BTMA4G&#xa;A1UdDwEB/wQEAwIFoDAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUHAwIwWwYD&#xa;VR0fBFQwUjBQoE6gTIZKaHR0cDovL2NybC5kaWdpY2VydC5jbi9EaWdpQ2VydFNl&#xa;Y3VyZVNpdGVPVkcyVExTQ05SU0E0MDk2U0hBMjU2MjAyMkNBMS5jcmwwgZEGCCsG&#xa;AQUFBwEBBIGEMIGBMCMGCCsGAQUFBzABhhdodHRwOi8vb2NzcC5kaWdpY2VydC5j&#xa;bjBaBggrBgEFBQcwAoZOaHR0cDovL2NhY2VydHMuZGlnaWNlcnQuY24vRGlnaUNl&#xa;cnRTZWN1cmVTaXRlT1ZHMlRMU0NOUlNBNDA5NlNIQTI1NjIwMjJDQTEuY3J0MAwG&#xa;A1UdEwEB/wQCMAAwggF+BgorBgEEAdZ5AgQCBIIBbgSCAWoBaAB2AA5XlLzzrqk+&#xa;MxssmQez95Dfm8I9cTIl3SGpJaxhxU4hAAABltgMm2EAAAQDAEcwRQIgYD2OOZf8&#xa;rSwgrkiwu1K2XFwhSJKCYTi9EPCxuUBXoxcCIQD3ZpQEvbYJoby+Pmw6glIqt7Qo&#xa;HwLK9n6zmfnc+sdwVgB1AGQRxGykEuyniRyiAi4AvKtPKAfUHjUnq+r+1QPJfc3w&#xa;AAABltgMm48AAAQDAEYwRAIfSjH0cEx+wD3Ill2ATKmqHNCexk09PgxEaMxWuIdk&#xa;ewIhAM3NCqx/eOJW4mK/3F2mYeL6fK48LwW7BaeuCWnF1XLpAHcASZybad4dfOz8&#xa;Nt7Nh2SmuFuvCoeAGdFVUvvp6ynd+MMAAAGW2AybnAAABAMASDBGAiEAvC6WN5C5&#xa;sefDIblHL9zdQ+Rq5+kPvr5PqnQPmWRCzTICIQD/OovsOzW/+PEEhNx6Lthp0wLb&#xa;NWgXFPfCQATqAuaPuTANBgkqhkiG9w0BAQsFAAOCAgEAjSJFXm9gcJ+7wq0+66Wn&#xa;V6BKuMN6Cygi0YlK/t7i+pOcIu7mtqO/EZo5E2BRYqdiYRprVEZLe78j4hSjL7UV&#xa;U0QCqUlhBumhIYzfkir0d6ErC+H1PTBhKA0KIk+JZUm5unTfCr14fJZ/uSjB/d16&#xa;S5EQxNa3FAv2hpXLsMZCFkxQz8m9RNji0TeTZ85wy5Y0EChzSObVPjgHd6tTubJY&#xa;J/4kKsI4bNdhkP0g3LyO4yEIrf4U2DKRV33GGflOVSX3dYp+XxUF5fgzVuw55xcf&#xa;hlnOaiUmkr0JBmHOX0lOwf5Lp4s/l2KleFLxSszXcu9a9wKyeOcz9G2mwMn02jb8&#xa;S10BBwQyFfGu3eKGT+NyJJs3+ZrKeFIV/zmgr9bDZOx6kiKScM8AfoMXuhtEHqgD&#xa;yHgVuUGEEI32MCBOpBiBFER0++CUR5tdW7+bw163gBFb083Ddvc4QnnKWX9GQ+Bo&#xa;WG+UsVHva2LF1J4cE0ufdAIuQ2i5CBGzo80MUnil4o+yMpzIdfFD3C+T16cy6H2N&#xa;gGF3CIcfNLgy2acontUsLCaM182No3+4deelacqqqQJ7FM5SBil9wGCF2JNoXUs4&#xa;/cd2f6FXb6Er+Vo4FfhoI1ieLp+MqkIG+Jd7rZN3fDCilo9H9Lxnpt65mbP/aIES&#xa;SG5Z6MBpAvhMggOgeubO6zQ=&#xa;-&#45;&#45;&#45;&#45;END CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;</elem>
</script></port>
<port protocol="tcp" portid="4310"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="mirrtex" servicefp="SF-Port4310-TCP:V=7.92%I=7%D=8/5%Time=68917AEC%P=i686-pc-windows-windows%r(GetRequest,14E,&quot;HTTP/1\.1\x20200\x20\r\nContent-Length:\x20296\r\n\r\nSE\^\x12\x86R\?\xe7\xed\x0b\x8d\x9a\xd7\x97N\xea\0\0\x01\t\0\0\x01\x10\xea\xd7\x91\xd6\xb59\x9c\xaf\xe8\xb7KJ\xe8\x12f1\x7f\x03Xe\xa4\xb7\*S\xfb\x17\xff\xd67\xb1Lj\&quot;\x20U\x0f\x024~\xf0_\x7fB\xd7\x95\x9c\x1e\$\x81m\x03\xf4xZ\x93\x99\xf0\x84\]\xa2\x80Z\x20\x120\x93!/\x89\xf7\xc0X\xe5\x0b\xb3/\xca\x1d\x0b-\xc4\xde\xf9\xf0\xc1\xa40`l\x85@\x9a:\x0b\x0e\xd4T\xd3\xc3y}5H\xcf\*\xce\[&gt;\n\xa1\x85=\x02\xc8\xf4OMq\xb8\x13R}\x18\t\r\x07\x95\xb1%\xd8\xfd\xb3Bar2\xd2L\\\xcf\*eI\xcfQ&amp;I;\&quot;@\tC-\xb7s\xce\xd1K\xed!\x7f\x06\x07\xbb\x9c\x81\xb6\x08\t\xb8s\x1f\x1f\*\xea_\x7f\xfeD\xa7\x86\x90\xcf\xbb\xacR\xff\xd5bZ\x16\x87\xc4\$\xe3\x0fo\xdd\x01/\xae\xd1\xff`n\x9a\x04\xe8\xd1\x15\xbf\x8a\x1f\x92\xbc\xab\xab\xea\x08O\x85\xc9\xc4\x91x\x7f\x9c\x19dS\x98\x15\x89\xa1\x12J\x7fh\xee\x8b2\x7f\x98\xbe\xcbM\x19\x07\x8e\xad\x03-~\xe4\*%\xbeZ\x89=\x08\$\xc0,h\x1e\x021}\x90\xb2\x19&quot;)%r(HTTPOptions,14E,&quot;HTTP/1\.1\x20200\x20\r\nContent-Length:\x20296\r\n\r\n\xb6&gt;\x82\xc3\x1b\t\xbc\xe1#\[\x9b\xd6\x90\x08R`\0\0\x01\t\0\0\x01\x10XV\xc2:7\xc86b\x88r\xb1W\xdcn\xb6\xff\xc7\xbf\x14f2\xefU\xf0\xf1\x9a\xff0\|\xb4\x10`\xfaxVX\x93\x1c\x80\xac\xf1\xbd\x08w\x20\]\ngN\xd9\.\x97KU7\x1b\&quot;9q\x1e\x9d\xfd\xae\xf3C\xf2~u\x85\xe9\x17U\x1d\x98\xd3\xe5\xf8I1s\xb6i\x1d\x1a\xb9\x892\x9d\xf9\\S\x8ad\xc8\$\|\xa6\xf8\xfa\x9fA\x81;\x12\xb0\|gHr\xcc\xff{\?\r\xf7&gt;\xe97\x9f\x0fv\?e\x9c\x02t\xcc6\x1d\xf8BsN\xb9\x86\x1a\^\xcc\xb1U8a\xba\xa5\|\xa1\xcf\xe6h\xeb\xabK\xf0\xfb\x9d\x7f\x9b\xcd\xb2\.\x20\xa3Q\xd0\x13~\xd1\xb9\xe1~\xbc\x9d\xca\xb4&lt;\x8c\x03\.\x89\*\x16\0\)yc\x9cV\x0c\xe5\xf9\xf1\x14\xb5\xfd\x1c\x9cw\xf8\xc2\x06\xa9\xcb`\x94\x83lE\x13vw\xb3\x1b\x0ef\x19\xc6\x9d\x83\x14\x89\x9a\|\xa5-\xac\./\xb1\x13\xe9\xb3\x0e&amp;P7\xa3\xee\xc3\x01X\xc0\x99\x9b\x14\xf7\xd3\xcd\x14\x08\x9e%\x03\xa7\x91\xb9%\xfe5\x81u_\x08\]\x1e\xe9\xe3\x01\xe6\xaa\xc8\xc9u&quot;)%r(FourOhFourRequest,14E,&quot;HTTP/1\.1\x20200\x20\r\nContent-Length:\x20296\r\n\r\n\^O\xb9\x1f\xe0y\xcb\x08\xa3\xd0\)\xder\+1\xb4\0\0\x01\t\0\0\x01\x10\x03\x0e\xea\x9b\[\x95i\x9fss\xd0\xa5\x93!D\x15\xfd:\x1de\xe3\xc5\x8e\xe8`JB\xf7\xef~4l\xc2\x01\x1f\.\xafp\xf2Z\xef\x90V\xb14\0P\x84\x02X\xa9P\xe9\x18\.\x97X\xec\xecd}\xbfKH\^\xb5\xd32MS\xf6p\xcd\xf4\x1az\?#M&gt;\x90\xee\xba\x17O\xdbk\x0bw\xa3\x97\x8e\xe6\x89,6\xbanD\xc8\xab\x93\xc2OZ\x1aD\x01M\|v\x8eX\x11&apos;\x85\x8dA\xe0&amp;\xae\x0fk\x03y\x9e\x850\xb3\xf0\xcd\x17\+@\xa9\xee8\x8e\?\x9az\x8f&apos;PD\xb3w\xb7\x0e\xd1F7jh\xea\xf1\x8c%l\x1ehE\x8b\xa5\x109n\xf8\xf3\x14\xae\x10\x10\xb5F\x87\xc5\x13\x88\x07\xd5\xf3\x81n9\x1f\xceo\xf6\x15\x0b\xec8s\&quot;\xed\t\xc7\+\x85\x17\x076&amp;\xd1OR\x1e,\+Q\x08\xd19\x1em\xa2\x14\xe6\&quot;\xd2\xff:\x16Q\x08\xbe\xc4\$\xdd\x9cu\xcb\+\x9eB\x17\xc5\xfd\x1cF\xa6g\xfdd\xabcWJy\x8d\xf2\xeb6\x8c\x88\xa3\x03\xf3\xbcO\xa4\x04\xccao&amp;\x05,\x87\xcb\xa1&quot;);" method="table" conf="3"/><script id="fingerprint-strings" output="&#xa;  FourOhFourRequest: &#xa;    HTTP/1.1 200 &#xa;    Content-Length: 296&#xa;    z?#M&gt;&#xa;    F7jh&#xa;    cWJy&#xa;  GetRequest: &#xa;    HTTP/1.1 200 &#xa;    Content-Length: 296&#xa;    y}5H&#xa;    Bar2&#xa;    L\xcf*eI&#xa;    Q&amp;I;&quot;@ C-&#xa;  HTTPOptions: &#xa;    HTTP/1.1 200 &#xa;    Content-Length: 296&#xa;    |gHr"><elem key="FourOhFourRequest">&#xa;    HTTP/1.1 200 &#xa;    Content-Length: 296&#xa;    z?#M&gt;&#xa;    F7jh&#xa;    cWJy</elem>
<elem key="GetRequest">&#xa;    HTTP/1.1 200 &#xa;    Content-Length: 296&#xa;    y}5H&#xa;    Bar2&#xa;    L\xcf*eI&#xa;    Q&amp;I;&quot;@ C-</elem>
<elem key="HTTPOptions">&#xa;    HTTP/1.1 200 &#xa;    Content-Length: 296&#xa;    |gHr</elem>
</script></port>
<port protocol="tcp" portid="4709"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="unknown" servicefp="SF-Port4709-TCP:V=7.92%T=SSL%I=7%D=8/5%Time=68917AF2%P=i686-pc-windows-windows%r(GenericLines,30,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Encoding:\x20\r\n\r\n&quot;)%r(GetRequest,BE,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Encoding:\x20\r\nContent-Length:\x2090\r\nContent-Type:\x20application/json\r\n\r\n{\&quot;api_ver\&quot;:\&quot;1\.1\&quot;,\&quot;call_status\&quot;:\&quot;error\&quot;,\&quot;error_code\&quot;:5,\&quot;error_msg\&quot;:\&quot;sign\x20lost\&quot;,\&quot;result\&quot;:{}}&quot;)%r(HTTPOptions,127,&quot;HTTP/1\.1\x20200\x20OK\r\nAccess-Control-Allow-Credentials:\x20true\r\nAccess-Control-Allow-Headers:\x20x-pop-token\r\nAccess-Control-Allow-Origin:\x20\r\nContent-Encoding:\x20\r\nContent-Length:\x2090\r\nContent-Type:\x20application/json\r\n\r\n{\&quot;api_ver\&quot;:\&quot;1\.1\&quot;,\&quot;call_status\&quot;:\&quot;error\&quot;,\&quot;error_code\&quot;:5,\&quot;error_msg\&quot;:\&quot;sign\x20lost\&quot;,\&quot;result\&quot;:{}}&quot;)%r(RTSPRequest,30,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Encoding:\x20\r\n\r\n&quot;)%r(FourOhFourRequest,BE,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Encoding:\x20\r\nContent-Length:\x2090\r\nContent-Type:\x20application/json\r\n\r\n{\&quot;api_ver\&quot;:\&quot;1\.1\&quot;,\&quot;call_status\&quot;:\&quot;error\&quot;,\&quot;error_code\&quot;:5,\&quot;error_msg\&quot;:\&quot;sign\x20lost\&quot;,\&quot;result\&quot;:{}}&quot;)%r(SIPOptions,30,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Encoding:\x20\r\n\r\n&quot;);" tunnel="ssl" method="table" conf="3"/><script id="fingerprint-strings" output="&#xa;  FourOhFourRequest, GetRequest: &#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Encoding: &#xa;    Content-Length: 90&#xa;    Content-Type: application/json&#xa;    {&quot;api_ver&quot;:&quot;1.1&quot;,&quot;call_status&quot;:&quot;error&quot;,&quot;error_code&quot;:5,&quot;error_msg&quot;:&quot;sign lost&quot;,&quot;result&quot;:{}}&#xa;  GenericLines, RTSPRequest, SIPOptions: &#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Encoding:&#xa;  HTTPOptions: &#xa;    HTTP/1.1 200 OK&#xa;    Access-Control-Allow-Credentials: true&#xa;    Access-Control-Allow-Headers: x-pop-token&#xa;    Access-Control-Allow-Origin: &#xa;    Content-Encoding: &#xa;    Content-Length: 90&#xa;    Content-Type: application/json&#xa;    {&quot;api_ver&quot;:&quot;1.1&quot;,&quot;call_status&quot;:&quot;error&quot;,&quot;error_code&quot;:5,&quot;error_msg&quot;:&quot;sign lost&quot;,&quot;result&quot;:{}}"><elem key="FourOhFourRequest, GetRequest">&#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Encoding: &#xa;    Content-Length: 90&#xa;    Content-Type: application/json&#xa;    {&quot;api_ver&quot;:&quot;1.1&quot;,&quot;call_status&quot;:&quot;error&quot;,&quot;error_code&quot;:5,&quot;error_msg&quot;:&quot;sign lost&quot;,&quot;result&quot;:{}}</elem>
<elem key="GenericLines, RTSPRequest, SIPOptions">&#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Encoding:</elem>
<elem key="HTTPOptions">&#xa;    HTTP/1.1 200 OK&#xa;    Access-Control-Allow-Credentials: true&#xa;    Access-Control-Allow-Headers: x-pop-token&#xa;    Access-Control-Allow-Origin: &#xa;    Content-Encoding: &#xa;    Content-Length: 90&#xa;    Content-Type: application/json&#xa;    {&quot;api_ver&quot;:&quot;1.1&quot;,&quot;call_status&quot;:&quot;error&quot;,&quot;error_code&quot;:5,&quot;error_msg&quot;:&quot;sign lost&quot;,&quot;result&quot;:{}}</elem>
</script><script id="ssl-date" output="TLS randomness does not represent time"></script><script id="ssl-cert" output="Subject: commonName=localhost.wbridge.wps.cn&#xa;Subject Alternative Name: DNS:localhost.wbridge.wps.cn&#xa;Issuer: commonName=RapidSSL TLS RSA CA G1/organizationName=DigiCert Inc/countryName=US&#xa;Public Key type: rsa&#xa;Public Key bits: 2048&#xa;Signature Algorithm: sha256WithRSAEncryption&#xa;Not valid before: 2024-12-04T00:00:00&#xa;Not valid after:  2025-12-04T23:59:59&#xa;MD5:   85d4 d80e fc59 b31b db8c 8268 8baf 89b7&#xa;SHA-1: 2752 82b7 662a bbaa 92ab 2a37 4f61 6576 dd35 7f53"><table key="subject">
<elem key="commonName">localhost.wbridge.wps.cn</elem>
</table>
<table key="issuer">
<elem key="commonName">RapidSSL TLS RSA CA G1</elem>
<elem key="countryName">US</elem>
<elem key="organizationName">DigiCert Inc</elem>
<elem key="organizationalUnitName">www.digicert.com</elem>
</table>
<table key="pubkey">
<elem key="type">rsa</elem>
<elem key="bits">2048</elem>
<elem key="modulus">CC7748F707F0FF6C3B01F0C6104FB5732A66CBF1DF35AD49483C4B4B3EC325925D6342B14676E3BC3F1C4BAF969FD9C05950282C6FD5D139BF4C36C9D47FFB05FDC2121E3D9B8E5C043A071DAB37550BD7AEBE05268C1CBFEE36B0062FE746D433CB12FB6785608C8ED356A63D5287F3615BD5553A88FC6A1A1BD3E7D5A1C1FDA90AE9045F767B4C6284CF2FB5770FA7E1B924E08E1D95394E93046DBA9BD586401A3D3A9B56136D164A57E6401F664552365B487DC7A94F4BCE246FD86EB9BC376D14DDF1ACADAF2DA5535E9441387D08857147BCDC8B91DF161984166F85BAF5360100B4D7099305E6624C32FC9CFDDE60EB2E6A04611866B072901162470B</elem>
<elem key="exponent">65537</elem>
</table>
<table key="extensions">
<table>
<elem key="name">X509v3 Authority Key Identifier</elem>
<elem key="value">keyid:0C:DB:6C:82:49:0F:4A:67:0A:B8:14:EE:7A:C4:48:52:88:EB:56:38&#xa;</elem>
</table>
<table>
<elem key="name">X509v3 Subject Key Identifier</elem>
<elem key="value">DF:53:80:07:4E:B1:FC:3D:4C:95:7B:FB:51:88:CE:95:F9:B0:06:8E</elem>
</table>
<table>
<elem key="name">X509v3 Subject Alternative Name</elem>
<elem key="value">DNS:localhost.wbridge.wps.cn</elem>
</table>
<table>
<elem key="name">X509v3 Certificate Policies</elem>
<elem key="value">Policy: **********.2.1&#xa;  CPS: http://www.digicert.com/CPS&#xa;</elem>
</table>
<table>
<elem key="name">X509v3 Key Usage</elem>
<elem key="value">Digital Signature, Key Encipherment</elem>
<elem key="critical">true</elem>
</table>
<table>
<elem key="name">X509v3 Extended Key Usage</elem>
<elem key="value">TLS Web Server Authentication, TLS Web Client Authentication</elem>
</table>
<table>
<elem key="name">X509v3 CRL Distribution Points</elem>
<elem key="value">&#xa;Full Name:&#xa;  URI:http://cdp.rapidssl.com/RapidSSLTLSRSACAG1.crl&#xa;</elem>
</table>
<table>
<elem key="name">Authority Information Access</elem>
<elem key="value">OCSP - URI:http://status.rapidssl.com&#xa;CA Issuers - URI:http://cacerts.rapidssl.com/RapidSSLTLSRSACAG1.crt&#xa;</elem>
</table>
<table>
<elem key="name">X509v3 Basic Constraints</elem>
<elem key="value">CA:FALSE</elem>
<elem key="critical">true</elem>
</table>
<table>
<elem key="name">CT Precertificate SCTs</elem>
<elem key="value">Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : 12:F1:4E:34:BD:53:72:4C:84:06:19:C3:8F:3F:7A:13:&#xa;                F8:E7:B5:62:87:88:9C:6D:30:05:84:EB:E5:86:26:3A&#xa;    Timestamp : Dec  4 02:30:10.365 2024 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:45:02:21:00:8D:A6:EA:73:D1:59:77:2B:49:FD:C5:&#xa;                C9:36:19:68:D1:F7:F9:AB:04:F6:9E:9B:FA:BE:EB:5D:&#xa;                D6:DC:2B:9C:11:02:20:3F:DC:FC:F5:EB:5B:2C:07:80:&#xa;                83:5C:3D:7F:51:01:DC:EA:87:72:85:95:CA:0F:FA:16:&#xa;                0D:A8:A9:CB:96:06:DD&#xa;Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : ED:3C:4B:D6:E8:06:C2:A4:A2:00:57:DB:CB:24:E2:38:&#xa;                01:DF:51:2F:ED:C4:86:C5:70:0F:20:DD:B7:3E:3F:E0&#xa;    Timestamp : Dec  4 02:30:10.394 2024 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:46:02:21:00:91:95:B1:66:A8:DF:D3:4A:CC:3E:16:&#xa;                B7:EE:32:03:FC:A7:54:9A:9B:DF:90:C9:05:1A:64:8A:&#xa;                38:3C:98:2B:23:02:21:00:D0:34:6A:6D:FF:1A:B2:64:&#xa;                91:D6:4D:55:D2:7B:68:61:D8:2E:65:B8:15:23:26:A2:&#xa;                60:21:E1:3A:AD:65:71:09&#xa;Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : E6:D2:31:63:40:77:8C:C1:10:41:06:D7:71:B9:CE:C1:&#xa;                D2:40:F6:96:84:86:FB:BA:87:32:1D:FD:1E:37:8E:50&#xa;    Timestamp : Dec  4 02:30:10.411 2024 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:45:02:20:5C:58:D3:81:AE:4F:B0:60:FC:5C:E3:2E:&#xa;                2A:9E:AB:D1:68:EA:55:61:39:EC:D4:A0:04:D0:36:DD:&#xa;                E1:C4:D0:6B:02:21:00:85:78:BE:68:18:64:3F:D9:9F:&#xa;                D0:F4:C5:43:EF:F7:2B:F5:C5:33:8B:DA:6C:07:28:1B:&#xa;                43:E2:C1:15:EB:0D:16</elem>
</table>
</table>
<elem key="sig_algo">sha256WithRSAEncryption</elem>
<table key="validity">
<elem key="notBefore">2024-12-04T00:00:00</elem>
<elem key="notAfter">2025-12-04T23:59:59</elem>
</table>
<elem key="md5">85d4d80efc59b31bdb8c82688baf89b7</elem>
<elem key="sha1">275282b7662abbaa92ab2a374f616576dd357f53</elem>
<elem key="pem">-&#45;&#45;&#45;&#45;BEGIN CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;MIIGMTCCBRmgAwIBAgIQD9IksACaUQ5vs5UpxSQOwzANBgkqhkiG9w0BAQsFADBg&#xa;MQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3&#xa;d3cuZGlnaWNlcnQuY29tMR8wHQYDVQQDExZSYXBpZFNTTCBUTFMgUlNBIENBIEcx&#xa;MB4XDTI0MTIwNDAwMDAwMFoXDTI1MTIwNDIzNTk1OVowIzEhMB8GA1UEAxMYbG9j&#xa;YWxob3N0LndicmlkZ2Uud3BzLmNuMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB&#xa;CgKCAQEAzHdI9wfw/2w7AfDGEE+1cypmy/HfNa1JSDxLSz7DJZJdY0KxRnbjvD8c&#xa;S6+Wn9nAWVAoLG/V0Tm/TDbJ1H/7Bf3CEh49m45cBDoHHas3VQvXrr4FJowcv+42&#xa;sAYv50bUM8sS+2eFYIyO01amPVKH82Fb1VU6iPxqGhvT59Whwf2pCukEX3Z7TGKE&#xa;zy+1dw+n4bkk4I4dlTlOkwRtupvVhkAaPTqbVhNtFkpX5kAfZkVSNltIfcepT0vO&#xa;JG/Ybrm8N20U3fGsra8tpVNelEE4fQiFcUe83IuR3xYZhBZvhbr1NgEAtNcJkwXm&#xa;Ykwy/Jz93mDrLmoEYRhmsHKQEWJHCwIDAQABo4IDIjCCAx4wHwYDVR0jBBgwFoAU&#xa;DNtsgkkPSmcKuBTuesRIUojrVjgwHQYDVR0OBBYEFN9TgAdOsfw9TJV7+1GIzpX5&#xa;sAaOMCMGA1UdEQQcMBqCGGxvY2FsaG9zdC53YnJpZGdlLndwcy5jbjA+BgNVHSAE&#xa;NzA1MDMGBmeBDAECATApMCcGCCsGAQUFBwIBFhtodHRwOi8vd3d3LmRpZ2ljZXJ0&#xa;LmNvbS9DUFMwDgYDVR0PAQH/BAQDAgWgMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggr&#xa;BgEFBQcDAjA/BgNVHR8EODA2MDSgMqAwhi5odHRwOi8vY2RwLnJhcGlkc3NsLmNv&#xa;bS9SYXBpZFNTTFRMU1JTQUNBRzEuY3JsMHYGCCsGAQUFBwEBBGowaDAmBggrBgEF&#xa;BQcwAYYaaHR0cDovL3N0YXR1cy5yYXBpZHNzbC5jb20wPgYIKwYBBQUHMAKGMmh0&#xa;dHA6Ly9jYWNlcnRzLnJhcGlkc3NsLmNvbS9SYXBpZFNTTFRMU1JTQUNBRzEuY3J0&#xa;MAwGA1UdEwEB/wQCMAAwggF/BgorBgEEAdZ5AgQCBIIBbwSCAWsBaQB2ABLxTjS9&#xa;U3JMhAYZw48/ehP457Vih4icbTAFhOvlhiY6AAABk4+A6L0AAAQDAEcwRQIhAI2m&#xa;6nPRWXcrSf3FyTYZaNH3+asE9p6b+r7rXdbcK5wRAiA/3Pz161ssB4CDXD1/UQHc&#xa;6odyhZXKD/oWDaipy5YG3QB3AO08S9boBsKkogBX28sk4jgB31Ev7cSGxXAPIN23&#xa;Pj/gAAABk4+A6NoAAAQDAEgwRgIhAJGVsWao39NKzD4Wt+4yA/ynVJqb35DJBRpk&#xa;ijg8mCsjAiEA0DRqbf8asmSR1k1V0ntoYdguZbgVIyaiYCHhOq1lcQkAdgDm0jFj&#xa;QHeMwRBBBtdxuc7B0kD2loSG+7qHMh39HjeOUAAAAZOPgOjrAAAEAwBHMEUCIFxY&#xa;04GuT7Bg/FzjLiqeq9Fo6lVhOezUoATQNt3hxNBrAiEAhXi+aBhkP9mf0PTFQ+/3&#xa;K/XFM4vabAcoG0PiwRXrDRYwDQYJKoZIhvcNAQELBQADggEBACr+CR+Ndcq8CXUG&#xa;05xB0zbElyHwq46/SB7AFiUZA43JfJNOGPmX4N1X3GevuwowqDE3I7K8s+b6TQVH&#xa;WlGM+zItfp4ekxZ1zWPRfc8/Lp6bH0jtPDH1eWFsFHqZPRPxOwHOmwnroOPmtXck&#xa;wXIOMjl+LleGFS5Pc+etoirbxooQvlq/wedvDrBWUP3UdaTOUSAidUSzfpIc+ScA&#xa;gLI5KIwgLAM8dZzkpplu6cOfxkXjp87Npf8vwgFL65Rfu8YlDvyBT3DchHGjDW1t&#xa;TkJF5yXXJM6zqO/coUMH/mDowIEj3GvwiX+EOYJl66GYkrRD4+a3YNSHIVzAMlOS&#xa;b26DATc=&#xa;-&#45;&#45;&#45;&#45;END CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;</elem>
</script></port>
<port protocol="tcp" portid="5000"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="upnp" servicefp="SF-Port5000-TCP:V=7.92%I=7%D=8/5%Time=68917AE7%P=i686-pc-windows-windows%r(GetRequest,5CEB,&quot;HTTP/1\.1\x20200\x20OK\r\nServer:\x20Werkzeug/3\.1\.3\x20Python/3\.12\.1\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:47\x20GMT\r\nContent-Type:\x20text/html;\x20charset=utf-8\r\nContent-Length:\x2023462\r\nAccess-Control-Allow-Origin:\x20\*\r\nAccess-Control-Allow-Headers:\x20Content-Type,Authorization\r\nAccess-Control-Allow-Methods:\x20GET,PUT,POST,DELETE,OPTIONS\r\nConnection:\x20close\r\n\r\n&lt;!DOCTYPE\x20html&gt;\n&lt;html\x20lang=\&quot;zh-CN\&quot;&gt;\n&lt;head&gt;\n\x20\x20\x20\x20&lt;meta\x20charset=\&quot;UTF-8\&quot;&gt;\n\x20\x20\x20\x20&lt;meta\x20name=\&quot;viewport\&quot;\x20content=\&quot;width=device-width,\x20initial-scale=1\.0\&quot;&gt;\n\x20\x20\x20\x20&lt;title&gt;\xe6\xb8\x97\xe9\x80\x8f\xe6\xb5\x8b\xe8\xaf\x95\xe6\x99\xba\xe8\x83\xbd\xe5\x8c\x96\xe6\xa3\x80\xe6\xb5\x8b\xe5\xb7\xa5\xe5\x85\xb7&lt;/title&gt;\n\x20\x20\x20\x20&lt;style&gt;\n\x20\x20\x20\x20\x20\x20\x20\x20body\x20{\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-family:\x20Arial,\x20sans-serif;\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20max-width:\x20800px;\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin:\x2050px\x20auto;\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x2020px;\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20#f5f5f5;\n\x20\x20\x20\x20\x20\x20\x20\x20}\n\x20\x20\x20\x20\x20\x20\x20\x20\.container\x20{\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background:\x20white;\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x2030px;\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x2010px;\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20box-shadow:\x200\x202px\x2010px\x20rgba\(0,0,0,0\.1\);\n\x20\x20\x20\x20\x20&quot;)%r(RTSPRequest,16C,&quot;&lt;!DOCTYPE\x20HTML&gt;\n&lt;html\x20lang=\&quot;en\&quot;&gt;\n\x20\x20\x20\x20&lt;head&gt;\n\x20\x20\x20\x20\x20\x20\x20\x20&lt;meta\x20charset=\&quot;utf-8\&quot;&gt;\n\x20\x20\x20\x20\x20\x20\x20\x20&lt;title&gt;Error\x20response&lt;/title&gt;\n\x20\x20\x20\x20&lt;/head&gt;\n\x20\x20\x20\x20&lt;body&gt;\n\x20\x20\x20\x20\x20\x20\x20\x20&lt;h1&gt;Error\x20response&lt;/h1&gt;\n\x20\x20\x20\x20\x20\x20\x20\x20&lt;p&gt;Error\x20code:\x20400&lt;/p&gt;\n\x20\x20\x20\x20\x20\x20\x20\x20&lt;p&gt;Message:\x20Bad\x20request\x20version\x20\(&apos;RTSP/1\.0&apos;\)\.&lt;/p&gt;\n\x20\x20\x20\x20\x20\x20\x20\x20&lt;p&gt;Error\x20code\x20explanation:\x20400\x20-\x20Bad\x20request\x20syntax\x20or\x20unsupported\x20method\.&lt;/p&gt;\n\x20\x20\x20\x20&lt;/body&gt;\n&lt;/html&gt;\n&quot;);" method="table" conf="3"/><script id="fingerprint-strings" output="&#xa;  GetRequest: &#xa;    HTTP/1.1 200 OK&#xa;    Server: Werkzeug/3.1.3 Python/3.12.1&#xa;    Date: Tue, 05 Aug 2025 03:30:47 GMT&#xa;    Content-Type: text/html; charset=utf-8&#xa;    Content-Length: 23462&#xa;    Access-Control-Allow-Origin: *&#xa;    Access-Control-Allow-Headers: Content-Type,Authorization&#xa;    Access-Control-Allow-Methods: GET,PUT,POST,DELETE,OPTIONS&#xa;    Connection: close&#xa;    &lt;!DOCTYPE html&gt;&#xa;    &lt;html lang=&quot;zh-CN&quot;&gt;&#xa;    &lt;head&gt;&#xa;    &lt;meta charset=&quot;UTF-8&quot;&gt;&#xa;    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;&#xa;    &lt;title&gt;&#xa;    &lt;/title&gt;&#xa;    &lt;style&gt;&#xa;    body {&#xa;    font-family: Arial, sans-serif;&#xa;    max-width: 800px;&#xa;    margin: 50px auto;&#xa;    padding: 20px;&#xa;    background-color: #f5f5f5;&#xa;    .container {&#xa;    background: white;&#xa;    padding: 30px;&#xa;    border-radius: 10px;&#xa;    box-shadow: 0 2px 10px rgba(0,0,0,0.1);&#xa;  RTSPRequest: &#xa;    &lt;!DOCTYPE HTML&gt;&#xa;    &lt;html lang=&quot;en&quot;&gt;&#xa;    &lt;head&gt;&#xa;    &lt;meta charset=&quot;utf-8&quot;&gt;&#xa;    &lt;title&gt;Error response&lt;/title&gt;&#xa;    &lt;/head&gt;&#xa;    &lt;body&gt;&#xa;    &lt;h1&gt;Error response&lt;/h1&gt;&#xa;    &lt;p&gt;Error code: 400&lt;/p&gt;&#xa;    &lt;p&gt;Message: Bad request version (&apos;RTSP/1.0&apos;).&lt;/p&gt;&#xa;    &lt;p&gt;Error code explanation: 400 - Bad request syntax or unsupported method.&lt;/p&gt;&#xa;    &lt;/body&gt;&#xa;    &lt;/html&gt;"><elem key="GetRequest">&#xa;    HTTP/1.1 200 OK&#xa;    Server: Werkzeug/3.1.3 Python/3.12.1&#xa;    Date: Tue, 05 Aug 2025 03:30:47 GMT&#xa;    Content-Type: text/html; charset=utf-8&#xa;    Content-Length: 23462&#xa;    Access-Control-Allow-Origin: *&#xa;    Access-Control-Allow-Headers: Content-Type,Authorization&#xa;    Access-Control-Allow-Methods: GET,PUT,POST,DELETE,OPTIONS&#xa;    Connection: close&#xa;    &lt;!DOCTYPE html&gt;&#xa;    &lt;html lang=&quot;zh-CN&quot;&gt;&#xa;    &lt;head&gt;&#xa;    &lt;meta charset=&quot;UTF-8&quot;&gt;&#xa;    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;&#xa;    &lt;title&gt;&#xa;    &lt;/title&gt;&#xa;    &lt;style&gt;&#xa;    body {&#xa;    font-family: Arial, sans-serif;&#xa;    max-width: 800px;&#xa;    margin: 50px auto;&#xa;    padding: 20px;&#xa;    background-color: #f5f5f5;&#xa;    .container {&#xa;    background: white;&#xa;    padding: 30px;&#xa;    border-radius: 10px;&#xa;    box-shadow: 0 2px 10px rgba(0,0,0,0.1);</elem>
<elem key="RTSPRequest">&#xa;    &lt;!DOCTYPE HTML&gt;&#xa;    &lt;html lang=&quot;en&quot;&gt;&#xa;    &lt;head&gt;&#xa;    &lt;meta charset=&quot;utf-8&quot;&gt;&#xa;    &lt;title&gt;Error response&lt;/title&gt;&#xa;    &lt;/head&gt;&#xa;    &lt;body&gt;&#xa;    &lt;h1&gt;Error response&lt;/h1&gt;&#xa;    &lt;p&gt;Error code: 400&lt;/p&gt;&#xa;    &lt;p&gt;Message: Bad request version (&apos;RTSP/1.0&apos;).&lt;/p&gt;&#xa;    &lt;p&gt;Error code explanation: 400 - Bad request syntax or unsupported method.&lt;/p&gt;&#xa;    &lt;/body&gt;&#xa;    &lt;/html&gt;</elem>
</script></port>
<port protocol="tcp" portid="5040"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="unknown" method="table" conf="3"/></port>
<port protocol="tcp" portid="5283"><state state="open" reason="syn-ack" reason_ttl="128"/></port>
<port protocol="tcp" portid="5284"><state state="open" reason="syn-ack" reason_ttl="128"/></port>
<port protocol="tcp" portid="5354"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="mdnsresponder" method="table" conf="3"/></port>
<port protocol="tcp" portid="6188"><state state="open" reason="syn-ack" reason_ttl="128"/></port>
<port protocol="tcp" portid="6189"><state state="open" reason="syn-ack" reason_ttl="128"/></port>
<port protocol="tcp" portid="7897"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="unknown" servicefp="SF-Port7897-TCP:V=7.92%I=7%D=8/5%Time=68917AE7%P=i686-pc-windows-windows%r(GetRequest,42,&quot;HTTP/1\.0\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\nContent-Length:\x200\r\n\r\n&quot;)%r(HTTPOptions,42,&quot;HTTP/1\.0\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\nContent-Length:\x200\r\n\r\n&quot;)%r(FourOhFourRequest,42,&quot;HTTP/1\.0\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\nContent-Length:\x200\r\n\r\n&quot;);" method="table" conf="3"/><script id="fingerprint-strings" output="&#xa;  FourOhFourRequest, GetRequest, HTTPOptions: &#xa;    HTTP/1.0 400 Bad Request&#xa;    Connection: close&#xa;    Content-Length: 0"><elem key="FourOhFourRequest, GetRequest, HTTPOptions">&#xa;    HTTP/1.0 400 Bad Request&#xa;    Connection: close&#xa;    Content-Length: 0</elem>
</script></port>
<port protocol="tcp" portid="8086"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="http" product="Apache httpd" version="2.4.39" extrainfo="(Win64) OpenSSL/1.1.1b mod_fcgid/2.3.9a mod_log_rotate/1.02" method="probed" conf="10"><cpe>cpe:/a:apache:http_server:2.4.39</cpe></service><script id="http-server-header" output="Apache/2.4.39 (Win64) OpenSSL/1.1.1b mod_fcgid/2.3.9a mod_log_rotate/1.02"><elem>Apache/2.4.39 (Win64) OpenSSL/1.1.1b mod_fcgid/2.3.9a mod_log_rotate/1.02</elem>
</script><script id="http-title" output="\xE7\xAB\x99\xE7\x82\xB9\xE5\x88\x9B\xE5\xBB\xBA\xE6\x88\x90\xE5\x8A\x9F-phpstudy for windows"><elem key="title">\xE7\xAB\x99\xE7\x82\xB9\xE5\x88\x9B\xE5\xBB\xBA\xE6\x88\x90\xE5\x8A\x9F-phpstudy for windows</elem>
</script><script id="http-methods" output="&#xa;  Supported Methods: HEAD GET POST OPTIONS TRACE&#xa;  Potentially risky methods: TRACE"><table key="Supported Methods">
<elem>HEAD</elem>
<elem>GET</elem>
<elem>POST</elem>
<elem>OPTIONS</elem>
<elem>TRACE</elem>
</table>
<table key="Potentially risky methods">
<elem>TRACE</elem>
</table>
</script></port>
<port protocol="tcp" portid="9097"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="unknown" servicefp="SF-Port9097-TCP:V=7.92%I=7%D=8/5%Time=68917AF1%P=i686-pc-windows-windows%r(GenericLines,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(GetRequest,9F,&quot;HTTP/1\.0\x20401\x20Unauthorized\r\nContent-Type:\x20application/json\r\nVary:\x20Origin\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:57\x20GMT\r\nContent-Length:\x2027\r\n\r\n{\&quot;message\&quot;:\&quot;Unauthorized\&quot;}\n&quot;)%r(HTTPOptions,75,&quot;HTTP/1\.0\x20405\x20Method\x20Not\x20Allowed\r\nAllow:\x20GET\r\nVary:\x20Origin\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:57\x20GMT\r\nContent-Length:\x200\r\n\r\n&quot;)%r(RTSPRequest,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(Help,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(SSLSessionReq,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(TerminalServerCookie,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(TLSSessionReq,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(Kerberos,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(FourOhFourRequest,BE,&quot;HTTP/1\.0\x20404\x20Not\x20Found\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nVary:\x20Origin\r\nX-Content-Type-Options:\x20nosniff\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:31:22\x20GMT\r\nContent-Length:\x2019\r\n\r\n404\x20page\x20not\x20found\n&quot;)%r(LPDString,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(LDAPSearchReq,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;);" method="table" conf="3"/><script id="fingerprint-strings" output="&#xa;  FourOhFourRequest: &#xa;    HTTP/1.0 404 Not Found&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Vary: Origin&#xa;    X-Content-Type-Options: nosniff&#xa;    Date: Tue, 05 Aug 2025 03:31:22 GMT&#xa;    Content-Length: 19&#xa;    page not found&#xa;  GenericLines, Help, Kerberos, LDAPSearchReq, LPDString, RTSPRequest, SSLSessionReq, TLSSessionReq, TerminalServerCookie: &#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Connection: close&#xa;    Request&#xa;  GetRequest: &#xa;    HTTP/1.0 401 Unauthorized&#xa;    Content-Type: application/json&#xa;    Vary: Origin&#xa;    Date: Tue, 05 Aug 2025 03:30:57 GMT&#xa;    Content-Length: 27&#xa;    {&quot;message&quot;:&quot;Unauthorized&quot;}&#xa;  HTTPOptions: &#xa;    HTTP/1.0 405 Method Not Allowed&#xa;    Allow: GET&#xa;    Vary: Origin&#xa;    Date: Tue, 05 Aug 2025 03:30:57 GMT&#xa;    Content-Length: 0"><elem key="FourOhFourRequest">&#xa;    HTTP/1.0 404 Not Found&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Vary: Origin&#xa;    X-Content-Type-Options: nosniff&#xa;    Date: Tue, 05 Aug 2025 03:31:22 GMT&#xa;    Content-Length: 19&#xa;    page not found</elem>
<elem key="GenericLines, Help, Kerberos, LDAPSearchReq, LPDString, RTSPRequest, SSLSessionReq, TLSSessionReq, TerminalServerCookie">&#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Connection: close&#xa;    Request</elem>
<elem key="GetRequest">&#xa;    HTTP/1.0 401 Unauthorized&#xa;    Content-Type: application/json&#xa;    Vary: Origin&#xa;    Date: Tue, 05 Aug 2025 03:30:57 GMT&#xa;    Content-Length: 27&#xa;    {&quot;message&quot;:&quot;Unauthorized&quot;}</elem>
<elem key="HTTPOptions">&#xa;    HTTP/1.0 405 Method Not Allowed&#xa;    Allow: GET&#xa;    Vary: Origin&#xa;    Date: Tue, 05 Aug 2025 03:30:57 GMT&#xa;    Content-Length: 0</elem>
</script></port>
<port protocol="tcp" portid="9210"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="oma-mlp" servicefp="SF-Port9210-TCP:V=7.92%I=7%D=8/5%Time=68917AEC%P=i686-pc-windows-windows%r(GetRequest,143,&quot;HTTP/1\.1\x20200\x20OK\r\nContent-Type:\x20application/json\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\nConnection:\x20close\r\n\r\neyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\.eyJlcnJDb2RlIjo0MDAxLCJlcnJNc2ciOiLor7fmsYLmlbDmja7nu5PmnoTplJnor68iLCJwb3J0Ijo5MjEwLCJpc09ubHlHdWlsZCI6ZmFsc2UsImlhdCI6MTc1NDM2NDY1Mn0\.WeNvfttf6IOWQTxtiwHeiUF6bAU6MIlYn2xnDTWkNuM&quot;)%r(HTTPOptions,143,&quot;HTTP/1\.1\x20200\x20OK\r\nContent-Type:\x20application/json\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\nConnection:\x20close\r\n\r\neyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\.eyJlcnJDb2RlIjo0MDAxLCJlcnJNc2ciOiLor7fmsYLmlbDmja7nu5PmnoTplJnor68iLCJwb3J0Ijo5MjEwLCJpc09ubHlHdWlsZCI6ZmFsc2UsImlhdCI6MTc1NDM2NDY1Mn0\.WeNvfttf6IOWQTxtiwHeiUF6bAU6MIlYn2xnDTWkNuM&quot;)%r(RTSPRequest,143,&quot;HTTP/1\.1\x20200\x20OK\r\nContent-Type:\x20application/json\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\nConnection:\x20close\r\n\r\neyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\.eyJlcnJDb2RlIjo0MDAxLCJlcnJNc2ciOiLor7fmsYLmlbDmja7nu5PmnoTplJnor68iLCJwb3J0Ijo5MjEwLCJpc09ubHlHdWlsZCI6ZmFsc2UsImlhdCI6MTc1NDM2NDY1Mn0\.WeNvfttf6IOWQTxtiwHeiUF6bAU6MIlYn2xnDTWkNuM&quot;)%r(RPCCheck,2F,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\n\r\n&quot;)%r(DNSVersionBindReqTCP,2F,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\n\r\n&quot;)%r(DNSStatusRequestTCP,2F,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\n\r\n&quot;)%r(Help,2F,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\n\r\n&quot;)%r(SSLSessionReq,2F,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\n\r\n&quot;)%r(TerminalServerCookie,2F,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\n\r\n&quot;)%r(TLSSessionReq,2F,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\n\r\n&quot;)%r(Kerberos,2F,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\n\r\n&quot;)%r(SMBProgNeg,2F,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\n\r\n&quot;)%r(X11Probe,2F,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\n\r\n&quot;);" method="table" conf="3"/><script id="fingerprint-strings" output="&#xa;  DNSStatusRequestTCP, DNSVersionBindReqTCP, Help, Kerberos, RPCCheck, SMBProgNeg, SSLSessionReq, TLSSessionReq, TerminalServerCookie, X11Probe: &#xa;    HTTP/1.1 400 Bad Request&#xa;    Connection: close&#xa;  GetRequest, HTTPOptions, RTSPRequest: &#xa;    HTTP/1.1 200 OK&#xa;    Content-Type: application/json&#xa;    Date: Tue, 05 Aug 2025 03:30:52 GMT&#xa;    Connection: close&#xa;    eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlcnJDb2RlIjo0MDAxLCJlcnJNc2ciOiLor7fmsYLmlbDmja7nu5PmnoTplJnor68iLCJwb3J0Ijo5MjEwLCJpc09ubHlHdWlsZCI6ZmFsc2UsImlhdCI6MTc1NDM2NDY1Mn0.WeNvfttf6IOWQTxtiwHeiUF6bAU6MIlYn2xnDTWkNuM"><elem key="DNSStatusRequestTCP, DNSVersionBindReqTCP, Help, Kerberos, RPCCheck, SMBProgNeg, SSLSessionReq, TLSSessionReq, TerminalServerCookie, X11Probe">&#xa;    HTTP/1.1 400 Bad Request&#xa;    Connection: close</elem>
<elem key="GetRequest, HTTPOptions, RTSPRequest">&#xa;    HTTP/1.1 200 OK&#xa;    Content-Type: application/json&#xa;    Date: Tue, 05 Aug 2025 03:30:52 GMT&#xa;    Connection: close&#xa;    eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlcnJDb2RlIjo0MDAxLCJlcnJNc2ciOiLor7fmsYLmlbDmja7nu5PmnoTplJnor68iLCJwb3J0Ijo5MjEwLCJpc09ubHlHdWlsZCI6ZmFsc2UsImlhdCI6MTc1NDM2NDY1Mn0.WeNvfttf6IOWQTxtiwHeiUF6bAU6MIlYn2xnDTWkNuM</elem>
</script></port>
<port protocol="tcp" portid="10000"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="snet-sensor-mgmt" servicefp="SF-Port10000-TCP:V=7.92%T=SSL%I=7%D=8/5%Time=68917AF3%P=i686-pc-windows-windows%r(GetRequest,7E,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\nContent-Type:\x20application/json\r\nContent-Length:\x2027\r\n\r\n{\&quot;info\&quot;:\&quot;Invalid\x20request!\&quot;}&quot;)%r(DNSVersionBindReqTCP,69,&quot;HTTP/1\.0\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\nContent-Type:\x20text/plain\r\nContent-Length:\x2012\r\n\r\nBad\x20Request\.&quot;)%r(SMBProgNeg,69,&quot;HTTP/1\.0\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\nContent-Type:\x20text/plain\r\nContent-Length:\x2012\r\n\r\nBad\x20Request\.&quot;)%r(FourOhFourRequest,7E,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\nContent-Type:\x20application/json\r\nContent-Length:\x2027\r\n\r\n{\&quot;info\&quot;:\&quot;Invalid\x20request!\&quot;}&quot;)%r(oracle-tns,69,&quot;HTTP/1\.0\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\nContent-Type:\x20text/plain\r\nContent-Length:\x2012\r\n\r\nBad\x20Request\.&quot;)%r(ms-sql-s,69,&quot;HTTP/1\.0\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\nContent-Type:\x20text/plain\r\nContent-Length:\x2012\r\n\r\nBad\x20Request\.&quot;);" tunnel="ssl" method="table" conf="3"/><script id="ssl-cert" output="Subject: commonName=localhost.pan.baidu.com/organizationName=BeiJing Baidu Netcom Science Technology Co., Ltd/stateOrProvinceName=\xE5\x8C\x97\xE4\xBA\xAC\xE5\xB8\x82/countryName=CN&#xa;Subject Alternative Name: DNS:localhost.pan.baidu.com&#xa;Issuer: commonName=DigiCert Secure Site Pro G2 TLS CN RSA4096 SHA256 2022 CA1/organizationName=DigiCert, Inc./countryName=US&#xa;Public Key type: rsa&#xa;Public Key bits: 2048&#xa;Signature Algorithm: sha256WithRSAEncryption&#xa;Not valid before: 2025-06-16T00:00:00&#xa;Not valid after:  2026-07-10T23:59:59&#xa;MD5:   940c a7af c0cd 07d1 d626 defd 3f55 ddaf&#xa;SHA-1: 2f83 4863 b2af 6e0b 2022 ee97 ad28 e1f9 1968 f74e"><table key="subject">
<elem key="commonName">localhost.pan.baidu.com</elem>
<elem key="countryName">CN</elem>
<elem key="organizationName">BeiJing Baidu Netcom Science Technology Co., Ltd</elem>
<elem key="stateOrProvinceName">\xE5\x8C\x97\xE4\xBA\xAC\xE5\xB8\x82</elem>
</table>
<table key="issuer">
<elem key="commonName">DigiCert Secure Site Pro G2 TLS CN RSA4096 SHA256 2022 CA1</elem>
<elem key="countryName">US</elem>
<elem key="organizationName">DigiCert, Inc.</elem>
</table>
<table key="pubkey">
<elem key="type">rsa</elem>
<elem key="bits">2048</elem>
<elem key="modulus">D6ED53FE06B609C7C2ED85EF4E4C8C8058D5EB08EC0D3FB3C3445725E80DF8A4E459F0F4DAC2DC12FCE129476F7845DC34C8B93860DF6F4245C2F1F4F32A9B238FFE3B497E908C1CBCB93A18BCA96F6DB0EBE0B96936CAFB06224176D2F289BDB4577C840B7D69AED85A8A7196229DD284A040713BA4B919B07CC9DE660E476F6009D958369B236A2C50E0302413E8CF3373D0ABB4E095A9664E636918321A08B535C3463431FD71081336EBD884DCA6F6BC665D6C2007737F856547ADD65080AEFBA72FE0FDCE5FFE52D3C395EB0248D30D814FAFBA591A19BB6F6FED475C17DC0A17E7C0D33D4572900842C4C57E06366B8B9D6291DEE75771F92846BD72C5</elem>
<elem key="exponent">65537</elem>
</table>
<table key="extensions">
<table>
<elem key="name">X509v3 Authority Key Identifier</elem>
<elem key="value">keyid:E1:6C:C3:94:85:6F:E7:41:2F:55:7A:33:7D:8F:5F:B6:20:50:36:15&#xa;</elem>
</table>
<table>
<elem key="name">X509v3 Subject Key Identifier</elem>
<elem key="value">4C:0F:ED:C7:56:34:15:CD:33:A2:64:4E:D6:18:AF:41:5C:0E:1B:E0</elem>
</table>
<table>
<elem key="name">X509v3 Subject Alternative Name</elem>
<elem key="value">DNS:localhost.pan.baidu.com</elem>
</table>
<table>
<elem key="name">X509v3 Certificate Policies</elem>
<elem key="value">Policy: **********.2.2&#xa;  CPS: http://www.digicert.com/CPS&#xa;</elem>
</table>
<table>
<elem key="name">X509v3 Key Usage</elem>
<elem key="value">Digital Signature, Key Encipherment</elem>
<elem key="critical">true</elem>
</table>
<table>
<elem key="name">X509v3 Extended Key Usage</elem>
<elem key="value">TLS Web Server Authentication, TLS Web Client Authentication</elem>
</table>
<table>
<elem key="name">X509v3 CRL Distribution Points</elem>
<elem key="value">&#xa;Full Name:&#xa;  URI:http://crl.digicert.cn/DigiCertSecureSiteProG2TLSCNRSA4096SHA2562022CA1.crl&#xa;</elem>
</table>
<table>
<elem key="name">Authority Information Access</elem>
<elem key="value">OCSP - URI:http://ocsp.digicert.cn&#xa;CA Issuers - URI:http://cacerts.digicert.cn/DigiCertSecureSiteProG2TLSCNRSA4096SHA2562022CA1.crt&#xa;</elem>
</table>
<table>
<elem key="name">X509v3 Basic Constraints</elem>
<elem key="value">CA:FALSE</elem>
<elem key="critical">true</elem>
</table>
<table>
<elem key="name">CT Precertificate SCTs</elem>
<elem key="value">Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : D8:09:55:3B:94:4F:7A:FF:C8:16:19:6F:94:4F:85:AB:&#xa;                B0:F8:FC:5E:87:55:26:0F:15:D1:2E:72:BB:45:4B:14&#xa;    Timestamp : Jun 16 01:21:10.170 2025 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:45:02:21:00:8B:BC:04:95:B2:BC:49:C1:54:CC:2E:&#xa;                89:0B:8C:CE:62:EA:18:82:92:E7:F6:AD:DA:0B:4A:43:&#xa;                EA:AF:D4:65:25:02:20:70:2C:90:B9:B2:00:AA:BE:EA:&#xa;                0B:66:77:7A:75:99:FE:86:4F:A7:A1:10:DA:BA:E8:64:&#xa;                8E:52:8E:A7:A9:27:BD&#xa;Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : C2:31:7E:57:45:19:A3:45:EE:7F:38:DE:B2:90:41:EB:&#xa;                C7:C2:21:5A:22:BF:7F:D5:B5:AD:76:9A:D9:0E:52:CD&#xa;    Timestamp : Jun 16 01:21:10.146 2025 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:44:02:20:4C:BE:7D:9B:0C:AD:64:BF:05:92:F1:5C:&#xa;                6D:04:47:0C:19:C3:C0:DE:97:64:80:49:2A:F3:59:B6:&#xa;                A2:69:DE:0D:02:20:00:F6:7D:30:DE:C8:EF:A7:88:26:&#xa;                D2:8B:7F:32:F9:7D:57:2A:BB:04:3C:C2:1C:79:A5:79:&#xa;                6F:33:07:CF:64:24&#xa;Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : 94:4E:43:87:FA:EC:C1:EF:81:F3:19:24:26:A8:18:65:&#xa;                01:C7:D3:5F:38:02:01:3F:72:67:7D:55:37:2E:19:D8&#xa;    Timestamp : Jun 16 01:21:10.172 2025 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:44:02:20:32:61:67:03:55:69:95:61:BA:21:89:0A:&#xa;                20:AD:07:10:C8:E1:3E:68:79:BA:8F:F8:F4:4D:BB:8D:&#xa;                CD:10:7E:C0:02:20:41:DF:6C:27:0A:B5:3C:7C:58:CC:&#xa;                02:0E:35:88:D3:68:17:A6:99:E6:BE:80:1C:60:EE:6F:&#xa;                E3:15:20:44:FF:F2</elem>
</table>
</table>
<elem key="sig_algo">sha256WithRSAEncryption</elem>
<table key="validity">
<elem key="notBefore">2025-06-16T00:00:00</elem>
<elem key="notAfter">2026-07-10T23:59:59</elem>
</table>
<elem key="md5">940ca7afc0cd07d1d626defd3f55ddaf</elem>
<elem key="sha1">2f834863b2af6e0b2022ee97ad28e1f91968f74e</elem>
<elem key="pem">-&#45;&#45;&#45;&#45;BEGIN CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;MIIHzTCCBbWgAwIBAgIQDExnNJR8+rxF6e88N65w7jANBgkqhkiG9w0BAQsFADBr&#xa;MQswCQYDVQQGEwJVUzEXMBUGA1UEChMORGlnaUNlcnQsIEluYy4xQzBBBgNVBAMT&#xa;OkRpZ2lDZXJ0IFNlY3VyZSBTaXRlIFBybyBHMiBUTFMgQ04gUlNBNDA5NiBTSEEy&#xa;NTYgMjAyMiBDQTEwHhcNMjUwNjE2MDAwMDAwWhcNMjYwNzEwMjM1OTU5WjB+MQsw&#xa;CQYDVQQGEwJDTjESMBAGA1UECAwJ5YyX5Lqs5biCMTkwNwYDVQQKEzBCZWlKaW5n&#xa;IEJhaWR1IE5ldGNvbSBTY2llbmNlIFRlY2hub2xvZ3kgQ28uLCBMdGQxIDAeBgNV&#xa;BAMTF2xvY2FsaG9zdC5wYW4uYmFpZHUuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOC&#xa;AQ8AMIIBCgKCAQEA1u1T/ga2CcfC7YXvTkyMgFjV6wjsDT+zw0RXJegN+KTkWfD0&#xa;2sLcEvzhKUdveEXcNMi5OGDfb0JFwvH08yqbI4/+O0l+kIwcvLk6GLypb22w6+C5&#xa;aTbK+wYiQXbS8om9tFd8hAt9aa7YWopxliKd0oSgQHE7pLkZsHzJ3mYOR29gCdlY&#xa;NpsjaixQ4DAkE+jPM3PQq7TglalmTmNpGDIaCLU1w0Y0Mf1xCBM269iE3Kb2vGZd&#xa;bCAHc3+FZUet1lCArvunL+D9zl/+UtPDlesCSNMNgU+vulkaGbtvb+1HXBfcChfn&#xa;wNM9RXKQCELExX4GNmuLnWKR3udXcfkoRr1yxQIDAQABo4IDWDCCA1QwHwYDVR0j&#xa;BBgwFoAU4WzDlIVv50EvVXozfY9ftiBQNhUwHQYDVR0OBBYEFEwP7cdWNBXNM6Jk&#xa;TtYYr0FcDhvgMCIGA1UdEQQbMBmCF2xvY2FsaG9zdC5wYW4uYmFpZHUuY29tMD4G&#xa;A1UdIAQ3MDUwMwYGZ4EMAQICMCkwJwYIKwYBBQUHAgEWG2h0dHA6Ly93d3cuZGln&#xa;aWNlcnQuY29tL0NQUzAOBgNVHQ8BAf8EBAMCBaAwHQYDVR0lBBYwFAYIKwYBBQUH&#xa;AwEGCCsGAQUFBwMCMFwGA1UdHwRVMFMwUaBPoE2GS2h0dHA6Ly9jcmwuZGlnaWNl&#xa;cnQuY24vRGlnaUNlcnRTZWN1cmVTaXRlUHJvRzJUTFNDTlJTQTQwOTZTSEEyNTYy&#xa;MDIyQ0ExLmNybDCBkgYIKwYBBQUHAQEEgYUwgYIwIwYIKwYBBQUHMAGGF2h0dHA6&#xa;Ly9vY3NwLmRpZ2ljZXJ0LmNuMFsGCCsGAQUFBzAChk9odHRwOi8vY2FjZXJ0cy5k&#xa;aWdpY2VydC5jbi9EaWdpQ2VydFNlY3VyZVNpdGVQcm9HMlRMU0NOUlNBNDA5NlNI&#xa;QTI1NjIwMjJDQTEuY3J0MAwGA1UdEwEB/wQCMAAwggF8BgorBgEEAdZ5AgQCBIIB&#xa;bASCAWgBZgB2ANgJVTuUT3r/yBYZb5RPhauw+Pxeh1UmDxXRLnK7RUsUAAABl3ZT&#xa;dBoAAAQDAEcwRQIhAIu8BJWyvEnBVMwuiQuMzmLqGIKS5/at2gtKQ+qv1GUlAiBw&#xa;LJC5sgCqvuoLZnd6dZn+hk+noRDauuhkjlKOp6knvQB1AMIxfldFGaNF7n843rKQ&#xa;QevHwiFaIr9/1bWtdprZDlLNAAABl3ZTdAIAAAQDAEYwRAIgTL59mwytZL8FkvFc&#xa;bQRHDBnDwN6XZIBJKvNZtqJp3g0CIAD2fTDeyO+niCbSi38y+X1XKrsEPMIceaV5&#xa;bzMHz2QkAHUAlE5Dh/rswe+B8xkkJqgYZQHH0184AgE/cmd9VTcuGdgAAAGXdlN0&#xa;HAAABAMARjBEAiAyYWcDVWmVYbohiQogrQcQyOE+aHm6j/j0TbuNzRB+wAIgQd9s&#xa;Jwq1PHxYzAIONYjTaBemmea+gBxg7m/jFSBE//IwDQYJKoZIhvcNAQELBQADggIB&#xa;AKnvazuS7+qFQcJ1eXrtY6bXozepuUvgWcvOURtpw6LUal0YtTPLWD/t4Hf7Aw7J&#xa;V8BegP1wJx+XmBUPb7Dm/vDErRY6w3GrIjpNcFKnDhj2kKrSkDYzt5gxxf36E246&#xa;SUXkoiBZAYYgyZMKn/AmsSxUw9SW61124hFD4n5RpR5Ew+i/pudxI3ofuMLxtC0D&#xa;juWuXr5dDze3s8flpdRLrBAq67LbU7A/RWFTU5DL0inVThJx5UaTL1iP//YFsxu9&#xa;Dj1OMvYo9xxi+uqHV2tOMYGJOTiklGfkg1soxgaJdqZLS1i+c2MYhI9PyGi2SuQR&#xa;L3lzTkfUVYTJwIgKOU0GdSXH/rpFh8hUo4MxgCbiwrfJDIX89+Q7Q7LZiifhfAvZ&#xa;eDjUDJLCJHV2TtSyyde/EANUX4Iyb45Y+NN+nQODqnKz3Hi3JRlNsfPmGcCbdwjV&#xa;UC/OkuJkHOtgL9weS2KvESC79i701K+k4ACVuv7JwVPBufJ7wHg08wdV9jruBiSk&#xa;FuUtVk5Hl8Q7GMcwXZSRHcG8XDYahVDyyj1HArC32RjUlgbLRFpdRBRCabPEt/AA&#xa;anZ+irXR6DcwY3xxjkbfmqtWCGQxO+PLpD4P+Fo5sAJuA6kof5V2dwJiZXZvGYBI&#xa;lkF8bsXJYWacQHnzMqsLXQy7vCt/GGgv50VOLPN7riq6&#xa;-&#45;&#45;&#45;&#45;END CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;</elem>
</script><script id="ssl-date" output="TLS randomness does not represent time"></script><script id="fingerprint-strings" output="&#xa;  DNSVersionBindReqTCP, SMBProgNeg, ms-sql-s, oracle-tns: &#xa;    HTTP/1.0 400 Bad Request&#xa;    Connection: close&#xa;    Content-Type: text/plain&#xa;    Content-Length: 12&#xa;    Request.&#xa;  FourOhFourRequest, GetRequest: &#xa;    HTTP/1.1 400 Bad Request&#xa;    Connection: close&#xa;    Content-Type: application/json&#xa;    Content-Length: 27&#xa;    {&quot;info&quot;:&quot;Invalid request!&quot;}"><elem key="DNSVersionBindReqTCP, SMBProgNeg, ms-sql-s, oracle-tns">&#xa;    HTTP/1.0 400 Bad Request&#xa;    Connection: close&#xa;    Content-Type: text/plain&#xa;    Content-Length: 12&#xa;    Request.</elem>
<elem key="FourOhFourRequest, GetRequest">&#xa;    HTTP/1.1 400 Bad Request&#xa;    Connection: close&#xa;    Content-Type: application/json&#xa;    Content-Length: 27&#xa;    {&quot;info&quot;:&quot;Invalid request!&quot;}</elem>
</script></port>
<port protocol="tcp" portid="11200"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="rtsp" servicefp="SF-Port11200-TCP:V=7.92%I=7%D=8/5%Time=68917AEC%P=i686-pc-windows-windows%r(GenericLines,53,&quot;\x20405\x20Method\x20not\x20allowed\r\nConnection:\x20close\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2011:30:41\x20GMT\r\n\r\n&quot;)%r(HTTPOptions,5B,&quot;HTTP/1\.0\x20405\x20Method\x20not\x20allowed\r\nConnection:\x20close\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2011:30:52\x20GMT\r\n\r\n&quot;)%r(RTSPRequest,5B,&quot;RTSP/1\.0\x20405\x20Method\x20not\x20allowed\r\nConnection:\x20close\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2011:30:57\x20GMT\r\n\r\n&quot;)%r(SIPOptions,5A,&quot;SIP/2\.0\x20405\x20Method\x20not\x20allowed\r\nConnection:\x20close\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2011:31:02\x20GMT\r\n\r\n&quot;);" method="probed" conf="10"/><script id="fingerprint-strings" output="&#xa;  GenericLines: &#xa;    405 Method not allowed&#xa;    Connection: close&#xa;    Date: Tue, 05 Aug 2025 11:30:41 GMT&#xa;  HTTPOptions: &#xa;    HTTP/1.0 405 Method not allowed&#xa;    Connection: close&#xa;    Date: Tue, 05 Aug 2025 11:30:52 GMT&#xa;  RTSPRequest: &#xa;    RTSP/1.0 405 Method not allowed&#xa;    Connection: close&#xa;    Date: Tue, 05 Aug 2025 11:30:57 GMT&#xa;  SIPOptions: &#xa;    SIP/2.0 405 Method not allowed&#xa;    Connection: close&#xa;    Date: Tue, 05 Aug 2025 11:31:02 GMT"><elem key="GenericLines">&#xa;    405 Method not allowed&#xa;    Connection: close&#xa;    Date: Tue, 05 Aug 2025 11:30:41 GMT</elem>
<elem key="HTTPOptions">&#xa;    HTTP/1.0 405 Method not allowed&#xa;    Connection: close&#xa;    Date: Tue, 05 Aug 2025 11:30:52 GMT</elem>
<elem key="RTSPRequest">&#xa;    RTSP/1.0 405 Method not allowed&#xa;    Connection: close&#xa;    Date: Tue, 05 Aug 2025 11:30:57 GMT</elem>
<elem key="SIPOptions">&#xa;    SIP/2.0 405 Method not allowed&#xa;    Connection: close&#xa;    Date: Tue, 05 Aug 2025 11:31:02 GMT</elem>
</script><script id="rtsp-methods" output="ERROR: Script execution failed (use -d to debug)"/></port>
<port protocol="tcp" portid="11434"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="unknown" servicefp="SF-Port11434-TCP:V=7.92%I=7%D=8/5%Time=68917AE7%P=i686-pc-windows-windows%r(GenericLines,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(GetRequest,86,&quot;HTTP/1\.0\x20200\x20OK\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:47\x20GMT\r\nContent-Length:\x2017\r\n\r\nOllama\x20is\x20running&quot;)%r(HTTPOptions,52,&quot;HTTP/1\.0\x20204\x20No\x20Content\r\nAllow:\x20HEAD,\x20GET\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:47\x20GMT\r\n\r\n&quot;)%r(RTSPRequest,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(Help,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(SSLSessionReq,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(TerminalServerCookie,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(TLSSessionReq,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(Kerberos,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(FourOhFourRequest,7F,&quot;HTTP/1\.0\x20404\x20Not\x20Found\r\nContent-Type:\x20text/plain\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:31:12\x20GMT\r\nContent-Length:\x2018\r\n\r\n404\x20page\x20not\x20found&quot;)%r(LPDString,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(LDAPSearchReq,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(SIPOptions,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;);" method="table" conf="3"/><script id="fingerprint-strings" output="&#xa;  FourOhFourRequest: &#xa;    HTTP/1.0 404 Not Found&#xa;    Content-Type: text/plain&#xa;    Date: Tue, 05 Aug 2025 03:31:12 GMT&#xa;    Content-Length: 18&#xa;    page not found&#xa;  GenericLines, Help, Kerberos, LDAPSearchReq, LPDString, RTSPRequest, SIPOptions, SSLSessionReq, TLSSessionReq, TerminalServerCookie: &#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Connection: close&#xa;    Request&#xa;  GetRequest: &#xa;    HTTP/1.0 200 OK&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Date: Tue, 05 Aug 2025 03:30:47 GMT&#xa;    Content-Length: 17&#xa;    Ollama is running&#xa;  HTTPOptions: &#xa;    HTTP/1.0 204 No Content&#xa;    Allow: HEAD, GET&#xa;    Date: Tue, 05 Aug 2025 03:30:47 GMT"><elem key="FourOhFourRequest">&#xa;    HTTP/1.0 404 Not Found&#xa;    Content-Type: text/plain&#xa;    Date: Tue, 05 Aug 2025 03:31:12 GMT&#xa;    Content-Length: 18&#xa;    page not found</elem>
<elem key="GenericLines, Help, Kerberos, LDAPSearchReq, LPDString, RTSPRequest, SIPOptions, SSLSessionReq, TLSSessionReq, TerminalServerCookie">&#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Connection: close&#xa;    Request</elem>
<elem key="GetRequest">&#xa;    HTTP/1.0 200 OK&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Date: Tue, 05 Aug 2025 03:30:47 GMT&#xa;    Content-Length: 17&#xa;    Ollama is running</elem>
<elem key="HTTPOptions">&#xa;    HTTP/1.0 204 No Content&#xa;    Allow: HEAD, GET&#xa;    Date: Tue, 05 Aug 2025 03:30:47 GMT</elem>
</script></port>
<port protocol="tcp" portid="16422"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="tcpwrapped" method="probed" conf="8"/></port>
<port protocol="tcp" portid="20714"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="tcpwrapped" method="probed" conf="8"/></port>
<port protocol="tcp" portid="28317"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="unknown" servicefp="SF-Port28317-TCP:V=7.92%I=7%D=8/5%Time=68917AEC%P=i686-pc-windows-windows%r(GetRequest,136,&quot;HTTP/1\.1\x20200\x20OK\r\nServer:\x20MySocket\x20Server\r\nDate:\x20Tue,\x205\x20Aug\x202025\x203:30:52\x20GMT\r\nAccess-Control-Allow-Origin:\x20\*\r\nAccess-Control-Allow-Methods:\x20POST,\x20GET,\x20OPTIONS\r\nAccess-Control-Allow-Private-Network:\x20true\r\nContent-Type:\x20text/html;\x20charset=utf-8\r\nContent-Length:\x2023\r\nAccept-Ranges:\x20bytes\r\n\r\n{\&quot;ret\&quot;:1,\&quot;version\&quot;:\&quot;3\&quot;}&quot;)%r(HTTPOptions,12E,&quot;HTTP/1\.1\x20200\x20OK\r\nServer:\x20MySocket\x20Server\r\nDate:\x20Tue,\x205\x20Aug\x202025\x203:30:52\x20GMT\r\nAccess-Control-Allow-Origin:\x20\*\r\nAccess-Control-Allow-Methods:\x20POST,\x20GET,\x20OPTIONS\r\nAccess-Control-Allow-Private-Network:\x20true\r\nContent-Type:\x20text/html;\x20charset=utf-8\r\nContent-Length:\x2015\r\nAccept-Ranges:\x20bytes\r\n\r\n{\&quot;ret\&quot;:1,\&quot;\&quot;:\&quot;\&quot;}&quot;)%r(RTSPRequest,12E,&quot;HTTP/1\.1\x20200\x20OK\r\nServer:\x20MySocket\x20Server\r\nDate:\x20Tue,\x205\x20Aug\x202025\x203:30:52\x20GMT\r\nAccess-Control-Allow-Origin:\x20\*\r\nAccess-Control-Allow-Methods:\x20POST,\x20GET,\x20OPTIONS\r\nAccess-Control-Allow-Private-Network:\x20true\r\nContent-Type:\x20text/html;\x20charset=utf-8\r\nContent-Length:\x2015\r\nAccept-Ranges:\x20bytes\r\n\r\n{\&quot;ret\&quot;:1,\&quot;\&quot;:\&quot;\&quot;}&quot;)%r(FourOhFourRequest,136,&quot;HTTP/1\.1\x20200\x20OK\r\nServer:\x20MySocket\x20Server\r\nDate:\x20Tue,\x205\x20Aug\x202025\x203:31:45\x20GMT\r\nAccess-Control-Allow-Origin:\x20\*\r\nAccess-Control-Allow-Methods:\x20POST,\x20GET,\x20OPTIONS\r\nAccess-Control-Allow-Private-Network:\x20true\r\nContent-Type:\x20text/html;\x20charset=utf-8\r\nContent-Length:\x2023\r\nAccept-Ranges:\x20bytes\r\n\r\n{\&quot;ret\&quot;:1,\&quot;version\&quot;:\&quot;3\&quot;}&quot;)%r(SIPOptions,12D,&quot;HTTP/1\.1\x20200\x20OK\r\nServer:\x20MySocket\x20Server\r\nDate:\x20Tue,\x205\x20Aug\x202025\x203:32:0\x20GMT\r\nAccess-Control-Allow-Origin:\x20\*\r\nAccess-Control-Allow-Methods:\x20POST,\x20GET,\x20OPTIONS\r\nAccess-Control-Allow-Private-Network:\x20true\r\nContent-Type:\x20text/html;\x20charset=utf-8\r\nContent-Length:\x2015\r\nAccept-Ranges:\x20bytes\r\n\r\n{\&quot;ret\&quot;:1,\&quot;\&quot;:\&quot;\&quot;}&quot;);" method="table" conf="3"/><script id="fingerprint-strings" output="&#xa;  FourOhFourRequest: &#xa;    HTTP/1.1 200 OK&#xa;    Server: MySocket Server&#xa;    Date: Tue, 5 Aug 2025 3:31:45 GMT&#xa;    Access-Control-Allow-Origin: *&#xa;    Access-Control-Allow-Methods: POST, GET, OPTIONS&#xa;    Access-Control-Allow-Private-Network: true&#xa;    Content-Type: text/html; charset=utf-8&#xa;    Content-Length: 23&#xa;    Accept-Ranges: bytes&#xa;    {&quot;ret&quot;:1,&quot;version&quot;:&quot;3&quot;}&#xa;  GetRequest: &#xa;    HTTP/1.1 200 OK&#xa;    Server: MySocket Server&#xa;    Date: Tue, 5 Aug 2025 3:30:52 GMT&#xa;    Access-Control-Allow-Origin: *&#xa;    Access-Control-Allow-Methods: POST, GET, OPTIONS&#xa;    Access-Control-Allow-Private-Network: true&#xa;    Content-Type: text/html; charset=utf-8&#xa;    Content-Length: 23&#xa;    Accept-Ranges: bytes&#xa;    {&quot;ret&quot;:1,&quot;version&quot;:&quot;3&quot;}&#xa;  HTTPOptions, RTSPRequest: &#xa;    HTTP/1.1 200 OK&#xa;    Server: MySocket Server&#xa;    Date: Tue, 5 Aug 2025 3:30:52 GMT&#xa;    Access-Control-Allow-Origin: *&#xa;    Access-Control-Allow-Methods: POST, GET, OPTIONS&#xa;    Access-Control-Allow-Private-Network: true&#xa;    Content-Type: text/html; charset=utf-8&#xa;    Content-Length: 15&#xa;    Accept-Ranges: bytes&#xa;    {&quot;ret&quot;:1,&quot;&quot;:&quot;&quot;}&#xa;  SIPOptions: &#xa;    HTTP/1.1 200 OK&#xa;    Server: MySocket Server&#xa;    Date: Tue, 5 Aug 2025 3:32:0 GMT&#xa;    Access-Control-Allow-Origin: *&#xa;    Access-Control-Allow-Methods: POST, GET, OPTIONS&#xa;    Access-Control-Allow-Private-Network: true&#xa;    Content-Type: text/html; charset=utf-8&#xa;    Content-Length: 15&#xa;    Accept-Ranges: bytes&#xa;    {&quot;ret&quot;:1,&quot;&quot;:&quot;&quot;}"><elem key="FourOhFourRequest">&#xa;    HTTP/1.1 200 OK&#xa;    Server: MySocket Server&#xa;    Date: Tue, 5 Aug 2025 3:31:45 GMT&#xa;    Access-Control-Allow-Origin: *&#xa;    Access-Control-Allow-Methods: POST, GET, OPTIONS&#xa;    Access-Control-Allow-Private-Network: true&#xa;    Content-Type: text/html; charset=utf-8&#xa;    Content-Length: 23&#xa;    Accept-Ranges: bytes&#xa;    {&quot;ret&quot;:1,&quot;version&quot;:&quot;3&quot;}</elem>
<elem key="GetRequest">&#xa;    HTTP/1.1 200 OK&#xa;    Server: MySocket Server&#xa;    Date: Tue, 5 Aug 2025 3:30:52 GMT&#xa;    Access-Control-Allow-Origin: *&#xa;    Access-Control-Allow-Methods: POST, GET, OPTIONS&#xa;    Access-Control-Allow-Private-Network: true&#xa;    Content-Type: text/html; charset=utf-8&#xa;    Content-Length: 23&#xa;    Accept-Ranges: bytes&#xa;    {&quot;ret&quot;:1,&quot;version&quot;:&quot;3&quot;}</elem>
<elem key="HTTPOptions, RTSPRequest">&#xa;    HTTP/1.1 200 OK&#xa;    Server: MySocket Server&#xa;    Date: Tue, 5 Aug 2025 3:30:52 GMT&#xa;    Access-Control-Allow-Origin: *&#xa;    Access-Control-Allow-Methods: POST, GET, OPTIONS&#xa;    Access-Control-Allow-Private-Network: true&#xa;    Content-Type: text/html; charset=utf-8&#xa;    Content-Length: 15&#xa;    Accept-Ranges: bytes&#xa;    {&quot;ret&quot;:1,&quot;&quot;:&quot;&quot;}</elem>
<elem key="SIPOptions">&#xa;    HTTP/1.1 200 OK&#xa;    Server: MySocket Server&#xa;    Date: Tue, 5 Aug 2025 3:32:0 GMT&#xa;    Access-Control-Allow-Origin: *&#xa;    Access-Control-Allow-Methods: POST, GET, OPTIONS&#xa;    Access-Control-Allow-Private-Network: true&#xa;    Content-Type: text/html; charset=utf-8&#xa;    Content-Length: 15&#xa;    Accept-Ranges: bytes&#xa;    {&quot;ret&quot;:1,&quot;&quot;:&quot;&quot;}</elem>
</script></port>
<port protocol="tcp" portid="33331"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="diamondport" servicefp="SF-Port33331-TCP:V=7.92%I=7%D=8/5%Time=68917AEC%P=i686-pc-windows-windows%r(GetRequest,52,&quot;HTTP/1\.0\x20404\x20Not\x20Found\r\ncontent-length:\x200\r\ndate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\n\r\n&quot;)%r(HTTPOptions,52,&quot;HTTP/1\.0\x20404\x20Not\x20Found\r\ncontent-length:\x200\r\ndate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\n\r\n&quot;)%r(RTSPRequest,54,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\ncontent-length:\x200\r\ndate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\n\r\n&quot;)%r(RPCCheck,54,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\ncontent-length:\x200\r\ndate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\n\r\n&quot;)%r(DNSVersionBindReqTCP,54,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\ncontent-length:\x200\r\ndate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\n\r\n&quot;)%r(DNSStatusRequestTCP,54,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\ncontent-length:\x200\r\ndate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\n\r\n&quot;)%r(Help,54,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\ncontent-length:\x200\r\ndate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\n\r\n&quot;)%r(SSLSessionReq,54,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\ncontent-length:\x200\r\ndate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\n\r\n&quot;)%r(TerminalServerCookie,54,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\ncontent-length:\x200\r\ndate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\n\r\n&quot;)%r(TLSSessionReq,54,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\ncontent-length:\x200\r\ndate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\n\r\n&quot;)%r(Kerberos,54,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\ncontent-length:\x200\r\ndate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\n\r\n&quot;)%r(SMBProgNeg,54,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\ncontent-length:\x200\r\ndate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\n\r\n&quot;)%r(X11Probe,54,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\ncontent-length:\x200\r\ndate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\n\r\n&quot;)%r(FourOhFourRequest,52,&quot;HTTP/1\.0\x20404\x20Not\x20Found\r\ncontent-length:\x200\r\ndate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\n\r\n&quot;);" method="table" conf="3"/><script id="fingerprint-strings" output="&#xa;  DNSStatusRequestTCP, DNSVersionBindReqTCP, Help, Kerberos, RPCCheck, RTSPRequest, SMBProgNeg, SSLSessionReq, TLSSessionReq, TerminalServerCookie, X11Probe: &#xa;    HTTP/1.1 400 Bad Request&#xa;    content-length: 0&#xa;    date: Tue, 05 Aug 2025 03:30:52 GMT&#xa;  FourOhFourRequest, GetRequest, HTTPOptions: &#xa;    HTTP/1.0 404 Not Found&#xa;    content-length: 0&#xa;    date: Tue, 05 Aug 2025 03:30:52 GMT"><elem key="DNSStatusRequestTCP, DNSVersionBindReqTCP, Help, Kerberos, RPCCheck, RTSPRequest, SMBProgNeg, SSLSessionReq, TLSSessionReq, TerminalServerCookie, X11Probe">&#xa;    HTTP/1.1 400 Bad Request&#xa;    content-length: 0&#xa;    date: Tue, 05 Aug 2025 03:30:52 GMT</elem>
<elem key="FourOhFourRequest, GetRequest, HTTPOptions">&#xa;    HTTP/1.0 404 Not Found&#xa;    content-length: 0&#xa;    date: Tue, 05 Aug 2025 03:30:52 GMT</elem>
</script></port>
<port protocol="tcp" portid="36510"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="unknown" servicefp="SF-Port36510-TCP:V=7.92%I=7%D=8/5%Time=68917AE7%P=i686-pc-windows-windows%r(GenericLines,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(GetRequest,C6,&quot;HTTP/1\.0\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nSec-Websocket-Version:\x2013\r\nX-Content-Type-Options:\x20nosniff\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:47\x20GMT\r\nContent-Length:\x2012\r\n\r\nBad\x20Request\n&quot;)%r(HTTPOptions,C6,&quot;HTTP/1\.0\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nSec-Websocket-Version:\x2013\r\nX-Content-Type-Options:\x20nosniff\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:47\x20GMT\r\nContent-Length:\x2012\r\n\r\nBad\x20Request\n&quot;)%r(RTSPRequest,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(Help,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(SSLSessionReq,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(TerminalServerCookie,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(TLSSessionReq,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(Kerberos,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(FourOhFourRequest,C6,&quot;HTTP/1\.0\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nSec-Websocket-Version:\x2013\r\nX-Content-Type-Options:\x20nosniff\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:31:12\x20GMT\r\nContent-Length:\x2012\r\n\r\nBad\x20Request\n&quot;)%r(LPDString,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;);" method="table" conf="3"/><script id="fingerprint-strings" output="&#xa;  FourOhFourRequest: &#xa;    HTTP/1.0 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Sec-Websocket-Version: 13&#xa;    X-Content-Type-Options: nosniff&#xa;    Date: Tue, 05 Aug 2025 03:31:12 GMT&#xa;    Content-Length: 12&#xa;    Request&#xa;  GenericLines, Help, Kerberos, LPDString, RTSPRequest, SSLSessionReq, TLSSessionReq, TerminalServerCookie: &#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Connection: close&#xa;    Request&#xa;  GetRequest, HTTPOptions: &#xa;    HTTP/1.0 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Sec-Websocket-Version: 13&#xa;    X-Content-Type-Options: nosniff&#xa;    Date: Tue, 05 Aug 2025 03:30:47 GMT&#xa;    Content-Length: 12&#xa;    Request"><elem key="FourOhFourRequest">&#xa;    HTTP/1.0 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Sec-Websocket-Version: 13&#xa;    X-Content-Type-Options: nosniff&#xa;    Date: Tue, 05 Aug 2025 03:31:12 GMT&#xa;    Content-Length: 12&#xa;    Request</elem>
<elem key="GenericLines, Help, Kerberos, LPDString, RTSPRequest, SSLSessionReq, TLSSessionReq, TerminalServerCookie">&#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Connection: close&#xa;    Request</elem>
<elem key="GetRequest, HTTPOptions">&#xa;    HTTP/1.0 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Sec-Websocket-Version: 13&#xa;    X-Content-Type-Options: nosniff&#xa;    Date: Tue, 05 Aug 2025 03:30:47 GMT&#xa;    Content-Length: 12&#xa;    Request</elem>
</script></port>
<port protocol="tcp" portid="37510"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="unknown" servicefp="SF-Port37510-TCP:V=7.92%I=7%D=8/5%Time=68917AE9%P=i686-pc-windows-windows%r(GenericLines,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(GetRequest,C8,&quot;HTTP/1\.0\x20404\x20Not\x20Found\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nX-Content-Type-Options:\x20nosniff\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:49\x20GMT\r\nContent-Length:\x2043\r\n\r\n404\x20page\x20not\x20found\nPowered\x20by\x20Alibaba\x20Cloud&quot;)%r(HTTPOptions,C8,&quot;HTTP/1\.0\x20404\x20Not\x20Found\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nX-Content-Type-Options:\x20nosniff\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:49\x20GMT\r\nContent-Length:\x2043\r\n\r\n404\x20page\x20not\x20found\nPowered\x20by\x20Alibaba\x20Cloud&quot;)%r(RTSPRequest,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(Help,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(SSLSessionReq,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(TerminalServerCookie,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(TLSSessionReq,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(Kerberos,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(FourOhFourRequest,C8,&quot;HTTP/1\.0\x20404\x20Not\x20Found\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nX-Content-Type-Options:\x20nosniff\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:31:14\x20GMT\r\nContent-Length:\x2043\r\n\r\n404\x20page\x20not\x20found\nPowered\x20by\x20Alibaba\x20Cloud&quot;)%r(LPDString,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;);" method="table" conf="3"/><script id="fingerprint-strings" output="&#xa;  FourOhFourRequest: &#xa;    HTTP/1.0 404 Not Found&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    X-Content-Type-Options: nosniff&#xa;    Date: Tue, 05 Aug 2025 03:31:14 GMT&#xa;    Content-Length: 43&#xa;    page not found&#xa;    Powered by Alibaba Cloud&#xa;  GenericLines, Help, Kerberos, LPDString, RTSPRequest, SSLSessionReq, TLSSessionReq, TerminalServerCookie: &#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Connection: close&#xa;    Request&#xa;  GetRequest, HTTPOptions: &#xa;    HTTP/1.0 404 Not Found&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    X-Content-Type-Options: nosniff&#xa;    Date: Tue, 05 Aug 2025 03:30:49 GMT&#xa;    Content-Length: 43&#xa;    page not found&#xa;    Powered by Alibaba Cloud"><elem key="FourOhFourRequest">&#xa;    HTTP/1.0 404 Not Found&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    X-Content-Type-Options: nosniff&#xa;    Date: Tue, 05 Aug 2025 03:31:14 GMT&#xa;    Content-Length: 43&#xa;    page not found&#xa;    Powered by Alibaba Cloud</elem>
<elem key="GenericLines, Help, Kerberos, LPDString, RTSPRequest, SSLSessionReq, TLSSessionReq, TerminalServerCookie">&#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Connection: close&#xa;    Request</elem>
<elem key="GetRequest, HTTPOptions">&#xa;    HTTP/1.0 404 Not Found&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    X-Content-Type-Options: nosniff&#xa;    Date: Tue, 05 Aug 2025 03:30:49 GMT&#xa;    Content-Length: 43&#xa;    page not found&#xa;    Powered by Alibaba Cloud</elem>
</script></port>
<port protocol="tcp" portid="49664"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="msrpc" product="Microsoft Windows RPC" ostype="Windows" method="probed" conf="10"><cpe>cpe:/o:microsoft:windows</cpe></service></port>
<port protocol="tcp" portid="49665"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="msrpc" product="Microsoft Windows RPC" ostype="Windows" method="probed" conf="10"><cpe>cpe:/o:microsoft:windows</cpe></service></port>
<port protocol="tcp" portid="49666"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="msrpc" product="Microsoft Windows RPC" ostype="Windows" method="probed" conf="10"><cpe>cpe:/o:microsoft:windows</cpe></service></port>
<port protocol="tcp" portid="49667"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="msrpc" product="Microsoft Windows RPC" ostype="Windows" method="probed" conf="10"><cpe>cpe:/o:microsoft:windows</cpe></service></port>
<port protocol="tcp" portid="49668"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="msrpc" product="Microsoft Windows RPC" ostype="Windows" method="probed" conf="10"><cpe>cpe:/o:microsoft:windows</cpe></service></port>
<port protocol="tcp" portid="49729"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="msrpc" product="Microsoft Windows RPC" ostype="Windows" method="probed" conf="10"><cpe>cpe:/o:microsoft:windows</cpe></service></port>
<port protocol="tcp" portid="50915"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="unknown" servicefp="SF-Port50915-TCP:V=7.92%I=7%D=8/5%Time=68917AF2%P=i686-pc-windows-windows%r(GenericLines,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(GetRequest,17C0,&quot;HTTP/1\.0\x20200\x20OK\r\nAccept-Ranges:\x20bytes\r\nContent-Length:\x205940\r\nContent-Type:\x20text/html;\x20charset=utf-8\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:59\x20GMT\r\n\r\n&lt;!doctype\x20html&gt;\r\n&lt;html\x20lang=\&quot;en\&quot;\x20style=\&quot;overflow:\x20hidden\&quot;&gt;\r\n\x20\x20&lt;head&gt;\r\n\x20\x20\x20\x20&lt;meta\x20charset=\&quot;UTF-8\&quot;\x20/&gt;\r\n\x20\x20\x20\x20&lt;link\x20rel=\&quot;icon\&quot;\x20type=\&quot;image/svg\+xml\&quot;\x20href=\&quot;/vite\.svg\&quot;\x20/&gt;\r\n\x20\x20\x20\x20&lt;meta\x20name=\&quot;viewport\&quot;\x20content=\&quot;width=device-width,\x20initial-scale=1\.0\&quot;\x20/&gt;\r\r\n\x20\x20\x20\x20&lt;title&gt;Ollama&lt;/title&gt;\r\n\x20\x20\x20\x20&lt;script\x20type=\&quot;module\&quot;\x20crossorigin\x20src=\&quot;/assets/index-Uysc-wCI\.js\&quot;&gt;&lt;/script&gt;\n\x20\x20\x20\x20&lt;link\x20rel=\&quot;stylesheet\&quot;\x20crossorigin\x20href=\&quot;/assets/index-Cxw75_-Q\.css\&quot;&gt;\n\x20\x20&lt;/head&gt;\r\n\x20\x20&lt;body&gt;\r\n\x20\x20\x20\x20&lt;div\x20id=\&quot;root\&quot;&gt;&lt;/div&gt;\r\r\n\x20\x20\x20\x20&lt;script&gt;\r\n\x20\x20\x20\x20\x20\x20//\x20Initialize\x20webview\x20API\x20object\x20if\x20individual\x20functions\x20are\x20available\r\n\x20\x20\x20\x20\x20\x20if\x20\(typeof\x20window\.selectImageFile\x20===\x20\&quot;function\&quot;\)\x20{\r\n\x20\x20\x20\x20\x20\x20\x20\x20window\.webview\x20=\x20{\r\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20selectImageFile:\x20function\x20\(\)\x20{\r\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20return\x20new\x20Promise\(\(resolve\)\x20=&gt;\x20{\r\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20window&quot;)%r(HTTPOptions,97,&quot;HTTP/1\.0\x20200\x20OK\r\nX-Frame-Options:\x20DENY\r\nX-Request-Id:\x201754364659376965600\r\nX-Version:\x200\.9\.6\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:59\x20GMT\r\nContent-Length:\x200\r\n\r\n&quot;)%r(RTSPRequest,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;)%r(Help,67,&quot;HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request&quot;);" method="table" conf="3"/><script id="fingerprint-strings" output="&#xa;  GenericLines, Help, RTSPRequest: &#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Connection: close&#xa;    Request&#xa;  GetRequest: &#xa;    HTTP/1.0 200 OK&#xa;    Accept-Ranges: bytes&#xa;    Content-Length: 5940&#xa;    Content-Type: text/html; charset=utf-8&#xa;    Date: Tue, 05 Aug 2025 03:30:59 GMT&#xa;    &lt;!doctype html&gt;&#xa;    &lt;html lang=&quot;en&quot; style=&quot;overflow: hidden&quot;&gt;&#xa;    &lt;head&gt;&#xa;    &lt;meta charset=&quot;UTF-8&quot; /&gt;&#xa;    &lt;link rel=&quot;icon&quot; type=&quot;image/svg+xml&quot; href=&quot;/vite.svg&quot; /&gt;&#xa;    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot; /&gt;&#xa;    &lt;title&gt;Ollama&lt;/title&gt;&#xa;    &lt;script type=&quot;module&quot; crossorigin src=&quot;/assets/index-Uysc-wCI.js&quot;&gt;&lt;/script&gt;&#xa;    &lt;link rel=&quot;stylesheet&quot; crossorigin href=&quot;/assets/index-Cxw75_-Q.css&quot;&gt;&#xa;    &lt;/head&gt;&#xa;    &lt;body&gt;&#xa;    &lt;div id=&quot;root&quot;&gt;&lt;/div&gt;&#xa;    &lt;script&gt;&#xa;    Initialize webview API object if individual functions are available&#xa;    (typeof window.selectImageFile === &quot;function&quot;) {&#xa;    window.webview = {&#xa;    selectImageFile: function () {&#xa;    return new Promise((resolve) =&gt; {&#xa;    window&#xa;  HTTPOptions: &#xa;    HTTP/1.0 200 OK&#xa;    X-Frame-Options: DENY&#xa;    X-Request-Id: 1754364659376965600&#xa;    X-Version: 0.9.6&#xa;    Date: Tue, 05 Aug 2025 03:30:59 GMT&#xa;    Content-Length: 0"><elem key="GenericLines, Help, RTSPRequest">&#xa;    HTTP/1.1 400 Bad Request&#xa;    Content-Type: text/plain; charset=utf-8&#xa;    Connection: close&#xa;    Request</elem>
<elem key="GetRequest">&#xa;    HTTP/1.0 200 OK&#xa;    Accept-Ranges: bytes&#xa;    Content-Length: 5940&#xa;    Content-Type: text/html; charset=utf-8&#xa;    Date: Tue, 05 Aug 2025 03:30:59 GMT&#xa;    &lt;!doctype html&gt;&#xa;    &lt;html lang=&quot;en&quot; style=&quot;overflow: hidden&quot;&gt;&#xa;    &lt;head&gt;&#xa;    &lt;meta charset=&quot;UTF-8&quot; /&gt;&#xa;    &lt;link rel=&quot;icon&quot; type=&quot;image/svg+xml&quot; href=&quot;/vite.svg&quot; /&gt;&#xa;    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot; /&gt;&#xa;    &lt;title&gt;Ollama&lt;/title&gt;&#xa;    &lt;script type=&quot;module&quot; crossorigin src=&quot;/assets/index-Uysc-wCI.js&quot;&gt;&lt;/script&gt;&#xa;    &lt;link rel=&quot;stylesheet&quot; crossorigin href=&quot;/assets/index-Cxw75_-Q.css&quot;&gt;&#xa;    &lt;/head&gt;&#xa;    &lt;body&gt;&#xa;    &lt;div id=&quot;root&quot;&gt;&lt;/div&gt;&#xa;    &lt;script&gt;&#xa;    Initialize webview API object if individual functions are available&#xa;    (typeof window.selectImageFile === &quot;function&quot;) {&#xa;    window.webview = {&#xa;    selectImageFile: function () {&#xa;    return new Promise((resolve) =&gt; {&#xa;    window</elem>
<elem key="HTTPOptions">&#xa;    HTTP/1.0 200 OK&#xa;    X-Frame-Options: DENY&#xa;    X-Request-Id: 1754364659376965600&#xa;    X-Version: 0.9.6&#xa;    Date: Tue, 05 Aug 2025 03:30:59 GMT&#xa;    Content-Length: 0</elem>
</script></port>
<port protocol="tcp" portid="53166"><state state="open" reason="syn-ack" reason_ttl="128"/></port>
<port protocol="tcp" portid="63342"><state state="open" reason="syn-ack" reason_ttl="128"/><service name="http" product="PyCharm" version="2025.1.3.1" method="probed" conf="10"><cpe>cpe:/a:jetbrains:pycharm:2025.1.3.1</cpe></service><script id="http-server-header" output="PyCharm 2025.1.3.1"><elem>PyCharm 2025.1.3.1</elem>
</script><script id="http-title" output="404 Not Found"><elem key="title">404 Not Found</elem>
</script><script id="http-favicon" output="Unknown favicon MD5: D9A4C3BB85E108991879486564B92E99"/><script id="http-methods" output="&#xa;  Supported Methods: GET HEAD POST OPTIONS"><table key="Supported Methods">
<elem>GET</elem>
<elem>HEAD</elem>
<elem>POST</elem>
<elem>OPTIONS</elem>
</table>
</script></port>
</ports>
<os><portused state="open" proto="tcp" portid="80"/>
<portused state="closed" proto="tcp" portid="1"/>
<portused state="closed" proto="udp" portid="39367"/>
<osmatch name="Microsoft Windows 10 1607" accuracy="99" line="69751">
<osclass type="general purpose" vendor="Microsoft" osfamily="Windows" osgen="10" accuracy="99"><cpe>cpe:/o:microsoft:windows_10:1607</cpe></osclass>
</osmatch>
<osmatch name="Microsoft Windows 10 1511" accuracy="96" line="69516">
<osclass type="general purpose" vendor="Microsoft" osfamily="Windows" osgen="10" accuracy="96"><cpe>cpe:/o:microsoft:windows_10:1511</cpe></osclass>
</osmatch>
<osmatch name="Microsoft Windows 10 1703" accuracy="96" line="69823">
<osclass type="general purpose" vendor="Microsoft" osfamily="Windows" osgen="10" accuracy="96"><cpe>cpe:/o:microsoft:windows_10:1703</cpe></osclass>
</osmatch>
<osmatch name="Microsoft Windows 7 or 8.1 R1" accuracy="94" line="76914">
<osclass type="general purpose" vendor="Microsoft" osfamily="Windows" osgen="7" accuracy="94"><cpe>cpe:/o:microsoft:windows_7</cpe></osclass>
<osclass type="general purpose" vendor="Microsoft" osfamily="Windows" osgen="8.1" accuracy="94"><cpe>cpe:/o:microsoft:windows_8.1:r1</cpe></osclass>
</osmatch>
<osmatch name="Microsoft Windows 10 10586 - 14393" accuracy="93" line="69419">
<osclass type="general purpose" vendor="Microsoft" osfamily="Windows" osgen="10" accuracy="93"><cpe>cpe:/o:microsoft:windows_10</cpe></osclass>
</osmatch>
<osmatch name="Microsoft Windows 10 1809 - 1909" accuracy="92" line="69956">
<osclass type="general purpose" vendor="Microsoft" osfamily="Windows" osgen="10" accuracy="92"><cpe>cpe:/o:microsoft:windows_10</cpe></osclass>
</osmatch>
<osmatch name="Microsoft Windows 7 SP1" accuracy="92" line="77640">
<osclass type="general purpose" vendor="Microsoft" osfamily="Windows" osgen="7" accuracy="92"><cpe>cpe:/o:microsoft:windows_7::sp1</cpe></osclass>
</osmatch>
<osmatch name="Microsoft Windows 10" accuracy="92" line="69325">
<osclass type="general purpose" vendor="Microsoft" osfamily="Windows" osgen="10" accuracy="92"><cpe>cpe:/o:microsoft:windows_10</cpe></osclass>
</osmatch>
<osmatch name="Microsoft Windows 10 1709 - 1803" accuracy="92" line="69915">
<osclass type="general purpose" vendor="Microsoft" osfamily="Windows" osgen="10" accuracy="92"><cpe>cpe:/o:microsoft:windows_10</cpe></osclass>
</osmatch>
<osmatch name="Microsoft Windows Server 2012 R2" accuracy="91" line="76019">
<osclass type="general purpose" vendor="Microsoft" osfamily="Windows" osgen="2012" accuracy="91"><cpe>cpe:/o:microsoft:windows_server_2012:r2</cpe></osclass>
</osmatch>
<osfingerprint fingerprint="OS:SCAN(V=7.92%E=4%D=8/5%OT=80%CT=1%CU=39367%PV=N%DS=0%DC=L%G=Y%TM=68917BFD&#xa;OS:%P=i686-pc-windows-windows)SEQ(SP=102%GCD=1%ISR=105%CI=I%II=I%TS=A)OPS(O&#xa;OS:1=MFFD7NW8ST11%O2=MFFD7NW8ST11%O3=MFFD7NW8NNT11%O4=MFFD7NW8ST11%O5=MFFD7&#xa;OS:NW8ST11%O6=MFFD7ST11)WIN(W1=FFFF%W2=FFFF%W3=FFFF%W4=FFFF%W5=FFFF%W6=FFFF&#xa;OS:)ECN(R=Y%DF=Y%T=80%W=FFFF%O=MFFD7NW8NNS%CC=N%Q=)T1(R=Y%DF=Y%T=80%S=O%A=S&#xa;OS:+%F=AS%RD=0%Q=)T2(R=Y%DF=Y%T=80%W=0%S=Z%A=S%F=AR%O=%RD=0%Q=)T3(R=Y%DF=Y%&#xa;OS:T=80%W=0%S=Z%A=O%F=AR%O=%RD=0%Q=)T4(R=Y%DF=Y%T=80%W=0%S=A%A=O%F=R%O=%RD=&#xa;OS:0%Q=)T5(R=Y%DF=Y%T=80%W=0%S=Z%A=S+%F=AR%O=%RD=0%Q=)T6(R=Y%DF=Y%T=80%W=0%&#xa;OS:S=A%A=O%F=R%O=%RD=0%Q=)T7(R=Y%DF=Y%T=80%W=0%S=Z%A=S+%F=AR%O=%RD=0%Q=)U1(&#xa;OS:R=Y%DF=N%T=80%IPL=164%UN=0%RIPL=G%RID=G%RIPCK=Z%RUCK=G%RUD=G)IE(R=Y%DFI=&#xa;OS:N%T=80%CD=Z)&#xa;"/>
</os>
<uptime seconds="8363" lastboot="Tue Aug  5 09:16:02 2025"/>
<distance value="0"/>
<tcpsequence index="258" difficulty="Good luck!" values="A714EF61,A59337A6,FF37487D,20D29891,3DC39CE4,7ADD76D8"/>
<ipidsequence class="Busy server or unknown class" values="F79B,F7A9,F7B3,F7B5,F7B7,F7B9"/>
<tcptssequence class="1000HZ" values="7DFBBD,7DFC2A,7DFC95,7DFD01,7DFD6D,7DFDDC"/>
<hostscript><script id="smb2-time" output="&#xa;  date: 2025-08-05T03:33:41&#xa;  start_date: N/A"><elem key="date">2025-08-05T03:33:41</elem>
<elem key="start_date">N/A</elem>
</script><script id="smb2-security-mode" output="&#xa;  3.1.1: &#xa;    Message signing enabled but not required"><table key="3.1.1">
<elem>Message signing enabled but not required</elem>
</table>
</script></hostscript><times srtt="446" rttvar="528" to="100000"/>
</host>
<taskbegin task="NSE" time="1754364925"/>
<taskend task="NSE" time="1754364925"/>
<taskbegin task="NSE" time="1754364925"/>
<taskend task="NSE" time="1754364925"/>
<taskbegin task="NSE" time="1754364925"/>
<taskend task="NSE" time="1754364925"/>
<runstats><finished time="1754364925" timestr="Tue Aug  5 11:35:25 2025" summary="Nmap done at Tue Aug  5 11:35:25 2025; 1 IP address (1 host up) scanned in 295.71 seconds" elapsed="295.71" exit="success"/><hosts up="1" down="0" total="1"/>
</runstats>
</nmaprun>
