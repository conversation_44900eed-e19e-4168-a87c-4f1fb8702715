#!/bin/bash

echo "========================================"
echo "小飞飞科技公司人事管理系统"
echo "XiaoFeiFei HR Management System v3.2.1"
echo "========================================"
echo ""
echo "正在启动系统..."
echo ""

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: 未找到Python，请先安装Python"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "使用Python版本:"
$PYTHON_CMD --version

# 检查Flask是否安装
$PYTHON_CMD -c "import flask" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装Flask..."
    pip install flask
fi

# 启动应用
echo ""
echo "启动人事管理系统..."
echo "访问地址: http://localhost:3000"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

$PYTHON_CMD app.py
