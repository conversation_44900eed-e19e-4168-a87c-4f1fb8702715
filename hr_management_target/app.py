from flask import Flask, render_template, request, redirect, url_for, session, flash
import sqlite3
import os
from datetime import datetime

app = Flask(__name__, template_folder='templates')
app.secret_key = 'hr_system_2024_secret_key'

def init_database():
    """初始化人事管理系统数据库和测试数据"""
    # 确保数据库文件在当前目录
    db_path = os.path.join(os.path.dirname(__file__), 'hr_management_system.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 删除旧的表结构（如果存在）
    cursor.execute('DROP TABLE IF EXISTS information_schema_tables')
    cursor.execute('DROP TABLE IF EXISTS information_schema_columns')
    cursor.execute('DROP TABLE IF EXISTS information_schema_schemata')
    cursor.execute('DROP TABLE IF EXISTS information_schema_key_column_usage')
    cursor.execute('DROP TABLE IF EXISTS information_schema_table_constraints')

    # 创建完整的information_schema结构（模拟MySQL）

    # 1. INFORMATION_SCHEMA.TABLES - 表信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_tables (
            table_catalog TEXT DEFAULT 'def',
            table_schema TEXT,
            table_name TEXT,
            table_type TEXT,
            engine TEXT DEFAULT 'InnoDB',
            version INTEGER DEFAULT 10,
            row_format TEXT DEFAULT 'Dynamic',
            table_rows INTEGER DEFAULT 0,
            avg_row_length INTEGER DEFAULT 0,
            data_length INTEGER DEFAULT 16384,
            max_data_length INTEGER DEFAULT 0,
            index_length INTEGER DEFAULT 0,
            data_free INTEGER DEFAULT 0,
            auto_increment INTEGER,
            create_time TEXT,
            update_time TEXT,
            check_time TEXT,
            table_collation TEXT DEFAULT 'utf8mb4_0900_ai_ci',
            checksum INTEGER,
            create_options TEXT,
            table_comment TEXT DEFAULT ''
        )
    ''')

    # 2. INFORMATION_SCHEMA.COLUMNS - 列信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_columns (
            table_catalog TEXT DEFAULT 'def',
            table_schema TEXT,
            table_name TEXT,
            column_name TEXT,
            ordinal_position INTEGER,
            column_default TEXT,
            is_nullable TEXT DEFAULT 'YES',
            data_type TEXT,
            character_maximum_length INTEGER,
            character_octet_length INTEGER,
            numeric_precision INTEGER,
            numeric_scale INTEGER,
            datetime_precision INTEGER,
            character_set_name TEXT DEFAULT 'utf8mb4',
            collation_name TEXT DEFAULT 'utf8mb4_0900_ai_ci',
            column_type TEXT,
            column_key TEXT DEFAULT '',
            extra TEXT DEFAULT '',
            privileges TEXT DEFAULT 'select,insert,update,references',
            column_comment TEXT DEFAULT '',
            generation_expression TEXT DEFAULT '',
            srs_id INTEGER
        )
    ''')

    # 3. INFORMATION_SCHEMA.SCHEMATA - 数据库信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_schemata (
            catalog_name TEXT DEFAULT 'def',
            schema_name TEXT,
            default_character_set_name TEXT DEFAULT 'utf8mb4',
            default_collation_name TEXT DEFAULT 'utf8mb4_0900_ai_ci',
            sql_path TEXT
        )
    ''')

    # 4. INFORMATION_SCHEMA.KEY_COLUMN_USAGE - 键列使用信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_key_column_usage (
            constraint_catalog TEXT DEFAULT 'def',
            constraint_schema TEXT,
            constraint_name TEXT,
            table_catalog TEXT DEFAULT 'def',
            table_schema TEXT,
            table_name TEXT,
            column_name TEXT,
            ordinal_position INTEGER,
            position_in_unique_constraint INTEGER,
            referenced_table_schema TEXT,
            referenced_table_name TEXT,
            referenced_column_name TEXT
        )
    ''')

    # 5. INFORMATION_SCHEMA.TABLE_CONSTRAINTS - 表约束信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_table_constraints (
            constraint_catalog TEXT DEFAULT 'def',
            constraint_schema TEXT,
            constraint_name TEXT,
            table_schema TEXT,
            table_name TEXT,
            constraint_type TEXT,
            enforced TEXT DEFAULT 'YES'
        )
    ''')

    # 6. INFORMATION_SCHEMA.STATISTICS - 索引统计信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_statistics (
            table_catalog TEXT DEFAULT 'def',
            table_schema TEXT,
            table_name TEXT,
            non_unique INTEGER,
            index_schema TEXT,
            index_name TEXT,
            seq_in_index INTEGER,
            column_name TEXT,
            collation TEXT,
            cardinality INTEGER,
            sub_part INTEGER,
            packed TEXT,
            nullable TEXT,
            index_type TEXT DEFAULT 'BTREE',
            comment TEXT DEFAULT '',
            index_comment TEXT DEFAULT ''
        )
    ''')

    # 7. INFORMATION_SCHEMA.VIEWS - 视图信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_views (
            table_catalog TEXT DEFAULT 'def',
            table_schema TEXT,
            table_name TEXT,
            view_definition TEXT,
            check_option TEXT DEFAULT 'NONE',
            is_updatable TEXT DEFAULT 'NO',
            definer TEXT,
            security_type TEXT DEFAULT 'DEFINER',
            character_set_client TEXT DEFAULT 'utf8mb4',
            collation_connection TEXT DEFAULT 'utf8mb4_0900_ai_ci'
        )
    ''')

    # 8. INFORMATION_SCHEMA.ROUTINES - 存储过程和函数信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_routines (
            specific_name TEXT,
            routine_catalog TEXT DEFAULT 'def',
            routine_schema TEXT,
            routine_name TEXT,
            routine_type TEXT,
            data_type TEXT,
            character_maximum_length INTEGER,
            character_octet_length INTEGER,
            numeric_precision INTEGER,
            numeric_scale INTEGER,
            datetime_precision INTEGER,
            character_set_name TEXT,
            collation_name TEXT,
            dtd_identifier TEXT,
            routine_body TEXT DEFAULT 'SQL',
            routine_definition TEXT,
            external_name TEXT,
            external_language TEXT,
            parameter_style TEXT DEFAULT 'SQL',
            is_deterministic TEXT DEFAULT 'NO',
            sql_data_access TEXT DEFAULT 'CONTAINS SQL',
            sql_path TEXT,
            security_type TEXT DEFAULT 'DEFINER',
            created TIMESTAMP,
            last_altered TIMESTAMP,
            sql_mode TEXT,
            routine_comment TEXT DEFAULT '',
            definer TEXT,
            character_set_client TEXT DEFAULT 'utf8mb4',
            collation_connection TEXT DEFAULT 'utf8mb4_0900_ai_ci',
            database_collation TEXT DEFAULT 'utf8mb4_0900_ai_ci'
        )
    ''')

    # 9. INFORMATION_SCHEMA.TRIGGERS - 触发器信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_triggers (
            trigger_catalog TEXT DEFAULT 'def',
            trigger_schema TEXT,
            trigger_name TEXT,
            event_manipulation TEXT,
            event_object_catalog TEXT DEFAULT 'def',
            event_object_schema TEXT,
            event_object_table TEXT,
            action_order INTEGER,
            action_condition TEXT,
            action_statement TEXT,
            action_orientation TEXT DEFAULT 'ROW',
            action_timing TEXT,
            action_reference_old_table TEXT,
            action_reference_new_table TEXT,
            action_reference_old_row TEXT DEFAULT 'OLD',
            action_reference_new_row TEXT DEFAULT 'NEW',
            created TIMESTAMP,
            sql_mode TEXT,
            definer TEXT,
            character_set_client TEXT DEFAULT 'utf8mb4',
            collation_connection TEXT DEFAULT 'utf8mb4_0900_ai_ci',
            database_collation TEXT DEFAULT 'utf8mb4_0900_ai_ci'
        )
    ''')

    # 10. INFORMATION_SCHEMA.PROCESSLIST - 进程列表信息
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS information_schema_processlist (
            id INTEGER,
            user TEXT,
            host TEXT,
            db TEXT,
            command TEXT,
            time INTEGER,
            state TEXT,
            info TEXT
        )
    ''')
    
    # 插入INFORMATION_SCHEMA.SCHEMATA数据
    cursor.execute('DELETE FROM information_schema_schemata')
    schemata_data = [
        ('information_schema', 'utf8', 'utf8_general_ci', None),
        ('hr_system_db', 'utf8mb4', 'utf8mb4_0900_ai_ci', None),
        ('mysql', 'utf8mb4', 'utf8mb4_0900_ai_ci', None),
        ('performance_schema', 'utf8mb4', 'utf8mb4_0900_ai_ci', None),
        ('sys', 'utf8mb4', 'utf8mb4_0900_ai_ci', None)
    ]
    cursor.executemany('''
        INSERT INTO information_schema_schemata (schema_name, default_character_set_name, default_collation_name, sql_path)
        VALUES (?, ?, ?, ?)
    ''', schemata_data)

    # 插入INFORMATION_SCHEMA.TABLES数据（简化版本，匹配表结构）
    cursor.execute('DELETE FROM information_schema_tables')
    schema_tables = [
        ('def', 'hr_system_db', 'users', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 10, 1638, 16384, 0, 0, 0, None, '2024-01-15 10:30:00', '2024-07-28 10:30:00', None, 'utf8mb4_0900_ai_ci', None, '', '用户信息表'),
        ('def', 'hr_system_db', 'employees', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 7, 2340, 16384, 0, 0, 0, None, '2024-01-15 10:30:00', '2024-07-28 10:30:00', None, 'utf8mb4_0900_ai_ci', None, '', '员工信息表'),
        ('def', 'hr_system_db', 'departments', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 5, 3276, 16384, 0, 0, 0, None, '2024-01-15 10:30:00', '2024-07-28 10:30:00', None, 'utf8mb4_0900_ai_ci', None, '', '部门信息表'),
        ('def', 'hr_system_db', 'positions', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 8, 2048, 16384, 0, 0, 0, None, '2024-01-15 10:30:00', '2024-07-28 10:30:00', None, 'utf8mb4_0900_ai_ci', None, '', '职位信息表'),
        ('def', 'hr_system_db', 'salary_records', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 7, 2340, 16384, 0, 0, 0, None, '2024-01-15 10:30:00', '2024-07-28 10:30:00', None, 'utf8mb4_0900_ai_ci', None, '', '薪资记录表'),
        ('def', 'hr_system_db', 'attendance', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 5, 3276, 16384, 0, 0, 0, None, '2024-01-15 10:30:00', '2024-07-28 10:30:00', None, 'utf8mb4_0900_ai_ci', None, '', '考勤记录表'),
        ('def', 'hr_system_db', 'performance_reviews', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 5, 3276, 16384, 0, 0, 0, None, '2024-01-15 10:30:00', '2024-07-28 10:30:00', None, 'utf8mb4_0900_ai_ci', None, '', '绩效评估表'),
        ('def', 'hr_system_db', 'admin_users', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 3, 5461, 16384, 0, 0, 0, None, '2024-01-15 10:30:00', '2024-07-28 10:30:00', None, 'utf8mb4_0900_ai_ci', None, '', '管理员用户表'),
        ('def', 'hr_system_db', 'system_config', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 10, 1638, 16384, 0, 0, 0, None, '2024-01-15 10:30:00', '2024-07-28 10:30:00', None, 'utf8mb4_0900_ai_ci', None, '', '系统配置表'),
        ('def', 'hr_system_db', 'audit_logs', 'BASE TABLE', 'InnoDB', 10, 'Dynamic', 0, 0, 16384, 0, 0, 0, None, '2024-01-15 10:30:00', '2024-07-28 10:30:00', None, 'utf8mb4_0900_ai_ci', None, '', '审计日志表')
    ]
    cursor.executemany('''
        INSERT INTO information_schema_tables (table_catalog, table_schema, table_name, table_type, engine, version, row_format, table_rows, avg_row_length, data_length, max_data_length, index_length, data_free, auto_increment, create_time, update_time, check_time, table_collation, checksum, create_options, table_comment)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', schema_tables)
    
    # 插入INFORMATION_SCHEMA.COLUMNS数据（匹配表结构）
    cursor.execute('DELETE FROM information_schema_columns')
    schema_columns = [
        # users表列信息
        ('def', 'hr_system_db', 'users', 'user_id', 1, None, 'NO', 'int', None, None, 10, 0, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'int(11)', 'PRI', 'auto_increment', 'select,insert,update,references', '用户ID', '', None),
        ('def', 'hr_system_db', 'users', 'username', 2, None, 'NO', 'varchar', 50, 200, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(50)', 'UNI', '', 'select,insert,update,references', '用户名', '', None),
        ('def', 'hr_system_db', 'users', 'password', 3, None, 'NO', 'varchar', 255, 1020, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(255)', '', '', 'select,insert,update,references', '密码', '', None),
        ('def', 'hr_system_db', 'users', 'real_name', 4, None, 'NO', 'varchar', 100, 400, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(100)', '', '', 'select,insert,update,references', '真实姓名', '', None),
        ('def', 'hr_system_db', 'users', 'department', 5, None, 'NO', 'varchar', 100, 400, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(100)', '', '', 'select,insert,update,references', '部门', '', None),
        ('def', 'hr_system_db', 'users', 'salary', 6, None, 'YES', 'decimal', None, None, 10, 2, None, None, None, 'decimal(10,2)', '', '', 'select,insert,update,references', '薪资', '', None),
        ('def', 'hr_system_db', 'users', 'hire_date', 7, None, 'YES', 'date', None, None, None, None, None, None, None, 'date', '', '', 'select,insert,update,references', '入职日期', '', None),

        # admin_users表列信息
        ('def', 'hr_system_db', 'admin_users', 'admin_id', 1, None, 'NO', 'int', None, None, 10, 0, None, None, None, 'int(11)', 'PRI', 'auto_increment', 'select,insert,update,references', '管理员ID', '', None),
        ('def', 'hr_system_db', 'admin_users', 'admin_name', 2, None, 'NO', 'varchar', 50, 200, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(50)', 'UNI', '', 'select,insert,update,references', '管理员用户名', '', None),
        ('def', 'hr_system_db', 'admin_users', 'admin_password', 3, None, 'NO', 'varchar', 255, 1020, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(255)', '', '', 'select,insert,update,references', '管理员密码', '', None),

        # system_config表列信息
        ('def', 'hr_system_db', 'system_config', 'config_key', 1, None, 'NO', 'varchar', 100, 400, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'varchar(100)', 'PRI', '', 'select,insert,update,references', '配置键', '', None),
        ('def', 'hr_system_db', 'system_config', 'config_value', 2, None, 'YES', 'text', 65535, 65535, None, None, None, 'utf8mb4', 'utf8mb4_0900_ai_ci', 'text', '', '', 'select,insert,update,references', '配置值', '', None)
    ]
    cursor.executemany('''
        INSERT INTO information_schema_columns (table_catalog, table_schema, table_name, column_name, ordinal_position, column_default, is_nullable, data_type, character_maximum_length, character_octet_length, numeric_precision, numeric_scale, datetime_precision, character_set_name, collation_name, column_type, column_key, extra, privileges, column_comment, generation_expression, srs_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', schema_columns)

    # 插入INFORMATION_SCHEMA.TABLE_CONSTRAINTS数据
    cursor.execute('DELETE FROM information_schema_table_constraints')
    table_constraints = [
        ('hr_system_db', 'PRIMARY', 'hr_system_db', 'users', 'PRIMARY KEY', 'YES'),
        ('hr_system_db', 'username_unique', 'hr_system_db', 'users', 'UNIQUE', 'YES'),
        ('hr_system_db', 'PRIMARY', 'hr_system_db', 'employees', 'PRIMARY KEY', 'YES'),
        ('hr_system_db', 'PRIMARY', 'hr_system_db', 'departments', 'PRIMARY KEY', 'YES'),
        ('hr_system_db', 'PRIMARY', 'hr_system_db', 'positions', 'PRIMARY KEY', 'YES'),
        ('hr_system_db', 'PRIMARY', 'hr_system_db', 'salary_records', 'PRIMARY KEY', 'YES'),
        ('hr_system_db', 'PRIMARY', 'hr_system_db', 'admin_users', 'PRIMARY KEY', 'YES'),
        ('hr_system_db', 'admin_name_unique', 'hr_system_db', 'admin_users', 'UNIQUE', 'YES'),
        ('hr_system_db', 'PRIMARY', 'hr_system_db', 'system_config', 'PRIMARY KEY', 'YES')
    ]
    cursor.executemany('''
        INSERT INTO information_schema_table_constraints (constraint_schema, constraint_name, table_schema, table_name, constraint_type, enforced)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', table_constraints)

    # 插入INFORMATION_SCHEMA.KEY_COLUMN_USAGE数据
    cursor.execute('DELETE FROM information_schema_key_column_usage')
    key_column_usage = [
        ('hr_system_db', 'PRIMARY', 'hr_system_db', 'users', 'user_id', 1, None, None, None, None),
        ('hr_system_db', 'username_unique', 'hr_system_db', 'users', 'username', 1, None, None, None, None),
        ('hr_system_db', 'PRIMARY', 'hr_system_db', 'employees', 'emp_id', 1, None, None, None, None),
        ('hr_system_db', 'PRIMARY', 'hr_system_db', 'departments', 'dept_id', 1, None, None, None, None),
        ('hr_system_db', 'PRIMARY', 'hr_system_db', 'positions', 'pos_id', 1, None, None, None, None),
        ('hr_system_db', 'PRIMARY', 'hr_system_db', 'salary_records', 'record_id', 1, None, None, None, None),
        ('hr_system_db', 'PRIMARY', 'hr_system_db', 'admin_users', 'admin_id', 1, None, None, None, None),
        ('hr_system_db', 'admin_name_unique', 'hr_system_db', 'admin_users', 'admin_name', 1, None, None, None, None),
        ('hr_system_db', 'PRIMARY', 'hr_system_db', 'system_config', 'config_key', 1, None, None, None, None)
    ]
    cursor.executemany('''
        INSERT INTO information_schema_key_column_usage (constraint_schema, constraint_name, table_schema, table_name, column_name, ordinal_position, position_in_unique_constraint, referenced_table_schema, referenced_table_name, referenced_column_name)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', key_column_usage)

    # 插入INFORMATION_SCHEMA.STATISTICS数据
    cursor.execute('DELETE FROM information_schema_statistics')
    statistics_data = [
        ('def', 'hr_system_db', 'users', 0, 'hr_system_db', 'PRIMARY', 1, 'user_id', 'A', 10, None, None, '', 'BTREE', '', ''),
        ('def', 'hr_system_db', 'users', 0, 'hr_system_db', 'username_unique', 1, 'username', 'A', 10, None, None, '', 'BTREE', '', ''),
        ('def', 'hr_system_db', 'admin_users', 0, 'hr_system_db', 'PRIMARY', 1, 'admin_id', 'A', 3, None, None, '', 'BTREE', '', ''),
        ('def', 'hr_system_db', 'admin_users', 0, 'hr_system_db', 'admin_name_unique', 1, 'admin_name', 'A', 3, None, None, '', 'BTREE', '', '')
    ]
    cursor.executemany('''
        INSERT INTO information_schema_statistics (table_catalog, table_schema, table_name, non_unique, index_schema, index_name, seq_in_index, column_name, collation, cardinality, sub_part, packed, nullable, index_type, comment, index_comment)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', statistics_data)

    # 插入INFORMATION_SCHEMA.VIEWS数据（示例视图）
    cursor.execute('DELETE FROM information_schema_views')
    views_data = [
        ('def', 'hr_system_db', 'employee_view', 'SELECT u.username, u.real_name, u.department FROM users u WHERE u.department IS NOT NULL', 'NONE', 'NO', 'hr_admin@localhost', 'DEFINER', 'utf8mb4', 'utf8mb4_0900_ai_ci')
    ]
    cursor.executemany('''
        INSERT INTO information_schema_views (table_catalog, table_schema, table_name, view_definition, check_option, is_updatable, definer, security_type, character_set_client, collation_connection)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', views_data)

    # 插入INFORMATION_SCHEMA.PROCESSLIST数据（当前进程）
    cursor.execute('DELETE FROM information_schema_processlist')
    processlist_data = [
        (1, 'hr_admin', 'localhost', 'hr_system_db', 'Query', 0, 'executing', 'SELECT * FROM users'),
        (2, 'hr_user', 'localhost', 'hr_system_db', 'Sleep', 300, 'waiting', None),
        (3, 'root', 'localhost', None, 'Query', 0, 'show processlist', 'SHOW PROCESSLIST')
    ]
    cursor.executemany('''
        INSERT INTO information_schema_processlist (id, user, host, db, command, time, state, info)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', processlist_data)
    
    # 创建用户表（包含完整的员工信息）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            user_id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            password TEXT NOT NULL,
            real_name TEXT NOT NULL,
            department TEXT NOT NULL,
            salary DECIMAL(10,2),
            hire_date TEXT
        )
    ''')
    
    # 创建部门表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS departments (
            dept_id INTEGER PRIMARY KEY AUTOINCREMENT,
            dept_name TEXT NOT NULL,
            manager_id INTEGER
        )
    ''')
    
    # 创建职位表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS positions (
            pos_id INTEGER PRIMARY KEY AUTOINCREMENT,
            pos_name TEXT NOT NULL,
            base_salary DECIMAL(10,2)
        )
    ''')
    
    # 创建员工表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employees (
            emp_id INTEGER PRIMARY KEY AUTOINCREMENT,
            emp_name TEXT NOT NULL,
            position_id INTEGER
        )
    ''')
    
    # 创建薪资记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS salary_records (
            record_id INTEGER PRIMARY KEY AUTOINCREMENT,
            emp_id INTEGER,
            monthly_salary DECIMAL(10,2)
        )
    ''')
    
    # 创建考勤表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS attendance (
            att_id INTEGER PRIMARY KEY AUTOINCREMENT,
            emp_id INTEGER,
            work_date TEXT
        )
    ''')
    
    # 创建绩效评估表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS performance_reviews (
            review_id INTEGER PRIMARY KEY AUTOINCREMENT,
            emp_id INTEGER,
            score INTEGER
        )
    ''')
    
    # 创建管理员用户表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS admin_users (
            admin_id INTEGER PRIMARY KEY AUTOINCREMENT,
            admin_name TEXT NOT NULL,
            admin_password TEXT NOT NULL
        )
    ''')
    
    # 创建系统配置表（模拟MySQL信息）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS system_config (
            config_key TEXT PRIMARY KEY,
            config_value TEXT
        )
    ''')
    
    # 插入系统配置信息
    cursor.execute('DELETE FROM system_config')
    system_configs = [
        ('database_version', 'MySQL 8.0.35'),
        ('system_name', '小飞飞科技公司人事管理系统'),
        ('version', '3.2.1'),
        ('last_backup', '2024-07-27 02:00:00'),
        ('admin_email', '<EMAIL>'),
        ('company_name', '小飞飞科技有限公司'),
        ('hr_phone', '021-12345678'),
        ('it_support', 'ext.8888'),
        ('encryption_key', 'HR_AES256_KEY_2024'),
        ('debug_mode', 'disabled')
    ]
    cursor.executemany('''
        INSERT INTO system_config (config_key, config_value)
        VALUES (?, ?)
    ''', system_configs)
    
    # 插入部门数据
    departments_data = [
        ('人事部', 1),
        ('技术部', 2),
        ('财务部', 3),
        ('市场部', 4),
        ('行政部', 5)
    ]
    cursor.execute('DELETE FROM departments')
    cursor.executemany('''
        INSERT INTO departments (dept_name, manager_id)
        VALUES (?, ?)
    ''', departments_data)
    
    # 插入职位数据
    positions_data = [
        ('人事经理', 15000.00),
        ('高级工程师', 18000.00),
        ('财务经理', 16000.00),
        ('市场专员', 8000.00),
        ('行政助理', 6000.00),
        ('软件工程师', 12000.00),
        ('产品经理', 20000.00),
        ('UI设计师', 10000.00)
    ]
    cursor.execute('DELETE FROM positions')
    cursor.executemany('''
        INSERT INTO positions (pos_name, base_salary)
        VALUES (?, ?)
    ''', positions_data)
    
    # 插入用户数据（包含完整员工信息）
    users_data = [
        ('admin', 'Admin@2024!', '张管理', '人事部', 15000.00, '2020-01-15'),
        ('zhangsan', 'Zhang123!', '张三', '技术部', 18000.00, '2021-03-20'),
        ('lisi', 'Li456@', '李四', '财务部', 16000.00, '2020-08-10'),
        ('wangwu', 'Wang789#', '王五', '市场部', 8000.00, '2022-01-05'),
        ('zhaoliu', 'Zhao012$', '赵六', '行政部', 6000.00, '2021-11-30'),
        ('sunqi', 'Sun345%', '孙七', '技术部', 12000.00, '2022-06-15'),
        ('zhouba', 'Zhou678^', '周八', '技术部', 20000.00, '2019-12-01'),
        ('wujiu', 'Wu901&', '吴九', '技术部', 10000.00, '2023-02-28'),
        ('hr_manager', 'HR_Pass2024!', '人事经理', '人事部', 18000.00, '2018-05-20'),
        ('ceo', 'CEO_Secret@123', '小飞飞', '管理层', 50000.00, '2015-01-01')
    ]
    
    cursor.execute('DELETE FROM users')
    cursor.executemany('''
        INSERT INTO users (username, password, real_name, department, salary, hire_date)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', users_data)
    
    # 插入员工表数据
    employees_data = [
        ('张三', 2),
        ('李四', 3),
        ('王五', 4),
        ('赵六', 5),
        ('孙七', 6),
        ('周八', 7),
        ('吴九', 8)
    ]
    cursor.execute('DELETE FROM employees')
    cursor.executemany('''
        INSERT INTO employees (emp_name, position_id)
        VALUES (?, ?)
    ''', employees_data)
    
    # 插入薪资记录数据
    salary_records_data = [
        (1, 18000.00),
        (2, 16000.00),
        (3, 8000.00),
        (4, 6000.00),
        (5, 12000.00),
        (6, 20000.00),
        (7, 10000.00)
    ]
    cursor.execute('DELETE FROM salary_records')
    cursor.executemany('''
        INSERT INTO salary_records (emp_id, monthly_salary)
        VALUES (?, ?)
    ''', salary_records_data)
    
    # 插入考勤数据
    attendance_data = [
        (1, '2024-07-01'),
        (2, '2024-07-01'),
        (3, '2024-07-01'),
        (4, '2024-07-01'),
        (5, '2024-07-01')
    ]
    cursor.execute('DELETE FROM attendance')
    cursor.executemany('''
        INSERT INTO attendance (emp_id, work_date)
        VALUES (?, ?)
    ''', attendance_data)
    
    # 插入绩效评估数据
    performance_data = [
        (1, 85),
        (2, 92),
        (3, 78),
        (4, 88),
        (5, 95)
    ]
    cursor.execute('DELETE FROM performance_reviews')
    cursor.executemany('''
        INSERT INTO performance_reviews (emp_id, score)
        VALUES (?, ?)
    ''', performance_data)
    
    # 插入管理员用户数据
    admin_users = [
        ('root_admin', 'RootAdmin@2024!'),
        ('hr_admin', 'HRAdmin#Secure'),
        ('system_admin', 'SysAdmin@123')
    ]
    cursor.execute('DELETE FROM admin_users')
    cursor.executemany('''
        INSERT INTO admin_users (admin_name, admin_password)
        VALUES (?, ?)
    ''', admin_users)
    
    # 创建模拟MySQL系统函数的视图
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS mysql_functions (
            function_name TEXT,
            function_result TEXT
        )
    ''')
    
    cursor.execute('DELETE FROM mysql_functions')
    mysql_funcs = [
        ('version()', 'MySQL 8.0.35-0ubuntu0.20.04.1'),
        ('database()', 'hr_system_db'),
        ('user()', 'hr_admin@localhost'),
        ('current_user()', 'hr_admin@localhost'),
        ('system_user()', 'hr_admin@localhost'),
        ('session_user()', 'hr_admin@localhost'),
        ('@@version', 'MySQL 8.0.35-0ubuntu0.20.04.1'),
        ('@@version_comment', 'MySQL Community Server - GPL'),
        ('@@hostname', 'hr-server-xiaofeifetech'),
        ('@@datadir', '/var/lib/mysql/'),
        ('@@basedir', '/usr/'),
        ('@@port', '3306')
    ]
    cursor.executemany('''
        INSERT INTO mysql_functions (function_name, function_result)
        VALUES (?, ?)
    ''', mysql_funcs)
    
    conn.commit()
    conn.close()
    print("人事管理系统数据库初始化完成！")

@app.route('/')
def index():
    """主页"""
    return render_template('login.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录处理 - 存在SQL注入漏洞"""
    if request.method == 'POST':
        username = request.form.get('username', '')
        password = request.form.get('password', '')

        if not username or not password:
            flash('请输入用户名和密码', 'error')
            return render_template('login.html')

        # 简单的SQL注入过滤 (难度系数: 4/10)
        def basic_sql_filter(input_str):
            """基础SQL注入过滤，阻止常见万能密码但保留绕过空间"""
            input_lower = input_str.lower().strip()

            # 阻止最常见的万能密码组合
            blocked_patterns = [
                "' or 1=1 #",
                "' or '1'='1' #",
                "admin' #",
                "' or 1=1--",
                "' or '1'='1'--",
                "admin'--",
                "' or 1=1/*",
                "' or true #",
                "' or 'a'='a' #"
            ]

            # 检查是否包含被阻止的模式
            for pattern in blocked_patterns:
                if pattern in input_lower:
                    return False

            # 阻止一些明显的SQL关键词组合（但不是全部）
            if "' or " in input_lower and "=" in input_lower and ("#" in input_lower or "--" in input_lower):
                # 进一步检查是否是简单的万能密码
                if any(simple in input_lower for simple in ["1=1", "'1'='1'", "true", "'a'='a'"]):
                    return False

            return True

        # 应用过滤
        if not basic_sql_filter(username):
            flash('输入包含非法字符，请重新输入', 'error')
            print(f"🛡️ 过滤器阻止了输入: {username}")
            return render_template('login.html')

        if not basic_sql_filter(password):
            flash('输入包含非法字符，请重新输入', 'error')
            print(f"🛡️ 过滤器阻止了输入: {password}")
            return render_template('login.html')
        
        try:
            db_path = os.path.join(os.path.dirname(__file__), 'hr_management_system.db')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 故意存在SQL注入漏洞的查询（只选择3列用于登录验证）
            query = f"SELECT user_id, username, real_name FROM users WHERE username = '{username}' AND password = '{password}'"
            print(f"执行SQL查询: {query}")  # 调试信息
            
            # 处理注释符 - 正确截断注释后的内容
            original_query = query
            print(f"原始查询: {query}")

            # 处理 # 注释符
            if '#' in query:
                comment_pos = query.find('#')
                query = query[:comment_pos].rstrip()
                print(f"处理#注释符后: {query}")

            # 处理 -- 注释符（必须后面跟空格）
            if '-- ' in query:
                comment_pos = query.find('-- ')
                query = query[:comment_pos].rstrip()
                print(f"处理--注释符后: {query}")

            if query != original_query:
                print(f"最终查询: {query}")

            # 在处理注释符后再检查列数
            # 检查ORDER BY列数错误（登录查询只返回3列）
            if "order by" in query.lower():
                import re
                order_match = re.search(r'order by (\d+)', query.lower())
                if order_match:
                    col_num = int(order_match.group(1))
                    if col_num > 3:  # 登录查询只返回3列
                        raise sqlite3.OperationalError("no such column: order by column out of range")

            # 检查UNION列数错误（登录查询需要3列匹配）
            if "union select" in query.lower():
                import re
                # 检查处理注释符后的查询
                union_match = re.search(r'union select (.*?)(?:from|$)', query.lower())
                if union_match:
                    columns_str = union_match.group(1).strip()
                    columns = [col.strip() for col in columns_str.split(',')]
                    print(f"登录检测到UNION SELECT列数: {len(columns)} 列: {columns}")
                    if len(columns) != 3:  # 登录查询需要3列
                        raise sqlite3.OperationalError("The used SELECT statements have a different number of columns")
            
            # 替换MySQL函数为SQLite兼容的值
            mysql_functions = {
                'database()': "'hr_system_db'",
                'version()': "'MySQL 8.0.35-0ubuntu0.20.04.1'",
                'user()': "'hr_admin@localhost'",
                'current_user()': "'hr_admin@localhost'",
                'system_user()': "'hr_admin@localhost'",
                'session_user()': "'hr_admin@localhost'",
                'connection_id()': "12345",
                'schema()': "'hr_system_db'",
                'current_database()': "'hr_system_db'",
                '@@version': "'MySQL 8.0.35-0ubuntu0.20.04.1'",
                '@@version_comment': "'MySQL Community Server - GPL'",
                '@@hostname': "'hr-server-xiaofeifetech'",
                '@@datadir': "'/var/lib/mysql/'",
                '@@basedir': "'/usr/'",
                '@@port': "3306",
                '@@socket': "'/var/run/mysqld/mysqld.sock'",
                'now()': "'2024-07-28 10:30:00'",
                'current_timestamp()': "'2024-07-28 10:30:00'",
                'current_date()': "'2024-07-28'",
                'current_time()': "'10:30:00'",
                'unix_timestamp()': "1722160200",
                'rand()': "0.123456789",
                'pi()': "3.141593",
                'length(database())': "12",
                'char_length(database())': "12",
                'ascii(database())': "104",
                'hex(database())': "'68725F73797374656D5F6462'",
                'bin(1)': "'1'",
                'oct(8)': "'10'",
                'md5(database())': "'5d41402abc4b2a76b9719d911017c592'",
                'sha1(database())': "'aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d'",
                'upper(database())': "'HR_SYSTEM_DB'",
                'lower(database())': "'hr_system_db'",
                'substring(database(),1,2)': "'hr'",
                'substr(database(),1,2)': "'hr'",
                'left(database(),2)': "'hr'",
                'right(database(),2)': "'db'",
                'concat(database(),version())': "'hr_system_dbMySQL 8.0.35-0ubuntu0.20.04.1'",
                'group_concat(database())': "'hr_system_db'",
                'count(*)': "10",
                'sleep(1)': "0",
                'benchmark(1000000,md5(1))': "0"
            }

            original_query_for_functions = query
            for func, replacement in mysql_functions.items():
                if func.lower() in query.lower():
                    # 使用正则表达式进行大小写不敏感的替换，确保完整匹配
                    import re
                    # 使用单词边界确保完整匹配函数名
                    if func.endswith('()'):
                        pattern = re.escape(func[:-2]) + r'\(\)'
                    else:
                        pattern = r'\b' + re.escape(func) + r'\b'
                    query = re.sub(pattern, replacement, query, flags=re.IGNORECASE)

            if query != original_query_for_functions:
                print(f"替换MySQL函数后: {query}")

            # 处理information_schema查询 - 正确的MySQL表名格式
            if "information_schema." in query.lower():
                # MySQL的INFORMATION_SCHEMA表名映射到SQLite表名
                information_schema_mappings = {
                    'information_schema.tables': 'information_schema_tables',
                    'information_schema.columns': 'information_schema_columns',
                    'information_schema.statistics': 'information_schema_statistics',
                    'information_schema.schemata': 'information_schema_schemata',
                    'information_schema.table_constraints': 'information_schema_table_constraints',
                    'information_schema.key_column_usage': 'information_schema_key_column_usage',
                    'information_schema.views': 'information_schema_views',
                    'information_schema.routines': 'information_schema_routines',
                    'information_schema.triggers': 'information_schema_triggers',
                    'information_schema.processlist': 'information_schema_processlist'
                }

                original_query_for_schema = query
                for mysql_table, sqlite_table in information_schema_mappings.items():
                    if mysql_table.lower() in query.lower():
                        import re
                        # 使用单词边界确保完整匹配表名
                        pattern = r'\b' + re.escape(mysql_table) + r'\b'
                        query = re.sub(pattern, sqlite_table, query, flags=re.IGNORECASE)

                if query != original_query_for_schema:
                    print(f"替换information_schema表名后: {query}")

            cursor.execute(query)
            results = cursor.fetchall()  # 获取所有结果，不只是第一行

            if results:
                # 检查是否是UNION注入（只有明确包含UNION SELECT才显示数据）
                if "union select" in query.lower():
                    # 这是UNION注入，在页面显示查询结果（3列格式）
                    injection_results = []
                    for row in results:
                        injection_results.append({
                            'col1': row[0] if len(row) > 0 else '',
                            'col2': row[1] if len(row) > 1 else '',
                            'col3': row[2] if len(row) > 2 else ''
                        })

                    flash('查询执行成功，以下是结果：', 'info')
                    return render_template('login.html', injection_results=injection_results, show_results=True)
                else:
                    # 正常登录或OR绕过 - 直接跳转到系统
                    user = results[0]
                    session['user_id'] = user[0]
                    session['username'] = user[1]

                    # 根据查询结果的列数确定显示的用户名
                    if len(user) >= 3:
                        display_name = user[2]  # real_name
                    else:
                        display_name = user[1]  # username

                    flash(f'欢迎回来, {display_name}!', 'success')
                    return redirect(url_for('dashboard'))
            else:
                flash('用户名或密码错误', 'error')
                
        except sqlite3.Error as e:
            # 详细的MySQL错误分类系统
            error_msg = str(e).lower()
            print(f"SQLite原始错误: {e}")
            print(f"当前查询: {query}")

            # 错误分类和对应的MySQL错误
            error_category = "未知错误"
            mysql_error = ""

            if "order by column out of range" in error_msg:
                error_category = "列数探测错误"
                mysql_error = f"MySQL Error 1054: Unknown column in 'order clause'"
                print(f"🔍 错误类别: {error_category} - ORDER BY列数超出范围")

            elif "the used select statements have a different number of columns" in error_msg:
                error_category = "UNION列数不匹配"
                mysql_error = "MySQL Error 1222: The used SELECT statements have a different number of columns"
                print(f"🔍 错误类别: {error_category} - UNION查询列数不一致")

            elif "syntax error" in error_msg or "near" in error_msg:
                error_category = "SQL语法错误"
                # 分析具体的语法错误类型
                if "union" in query.lower() and "select" in query.lower():
                    mysql_error = f"MySQL Error 1064: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near UNION SELECT"
                    print(f"🔍 错误类别: {error_category} - UNION语法错误")
                elif "order by" in query.lower():
                    mysql_error = f"MySQL Error 1064: You have an error in your SQL syntax; check the manual for ORDER BY syntax"
                    print(f"🔍 错误类别: {error_category} - ORDER BY语法错误")
                else:
                    mysql_error = f"MySQL Error 1064: You have an error in your SQL syntax near '{username}' at line 1"
                    print(f"🔍 错误类别: {error_category} - 一般语法错误")

            elif "no such column" in error_msg:
                error_category = "字段不存在"
                if "order by" in username.lower():
                    mysql_error = f"MySQL Error 1054: Unknown column in 'order clause'"
                    print(f"🔍 错误类别: {error_category} - ORDER BY中的字段不存在")
                else:
                    mysql_error = f"MySQL Error 1054: Unknown column '{username}' in 'where clause'"
                    print(f"🔍 错误类别: {error_category} - WHERE条件中的字段不存在")

            elif "no such table" in error_msg:
                error_category = "表不存在"
                mysql_error = f"MySQL Error 1146: Table 'hr_system_db.users' doesn't exist"
                print(f"🔍 错误类别: {error_category} - 查询的表不存在")

            elif "no such function" in error_msg:
                error_category = "函数不支持"
                # 检查是否是未支持的MySQL函数
                unsupported_functions = []
                common_functions = ['database', 'version', 'user', 'current_user', 'now', 'count', 'length', 'substring']
                for func in common_functions:
                    if f"{func}(" in query.lower():
                        unsupported_functions.append(func)

                if unsupported_functions:
                    mysql_error = f"MySQL Error 1305: FUNCTION hr_system_db.{unsupported_functions[0]} does not exist"
                    print(f"🔍 错误类别: {error_category} - 函数 {unsupported_functions[0]}() 不被支持")
                else:
                    mysql_error = f"MySQL Error 1305: Unknown function in query"
                    print(f"🔍 错误类别: {error_category} - 未知函数调用")

            elif "datatype mismatch" in error_msg:
                error_category = "数据类型不匹配"
                mysql_error = f"MySQL Error 1366: Incorrect string value for column"
                print(f"🔍 错误类别: {error_category} - 数据类型转换错误")

            elif "constraint" in error_msg:
                error_category = "约束违反"
                mysql_error = f"MySQL Error 1062: Duplicate entry or constraint violation"
                print(f"🔍 错误类别: {error_category} - 违反数据库约束")

            else:
                error_category = "其他数据库错误"
                mysql_error = f"MySQL Error 1064: You have an error in your SQL syntax near '{query[-50:]}' at line 1"
                print(f"🔍 错误类别: {error_category} - 未分类的数据库错误")

            # 输出错误总结
            print(f"📊 错误总结:")
            print(f"   - 错误类别: {error_category}")
            print(f"   - MySQL错误码: {mysql_error.split(':')[0] if ':' in mysql_error else 'Unknown'}")
            print(f"   - 错误描述: {mysql_error}")

            flash(f'数据库连接错误: {mysql_error}', 'error')
            print(f"🚨 最终MySQL错误: {mysql_error}")
        finally:
            conn.close()
    
    return render_template('login.html')

@app.route('/dashboard')
def dashboard():
    """用户仪表板"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    try:
        db_path = os.path.join(os.path.dirname(__file__), 'hr_management_system.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取用户信息
        cursor.execute('SELECT * FROM users WHERE user_id = ?', (session['user_id'],))
        user = cursor.fetchone()
        
        # 获取部门信息
        cursor.execute('SELECT * FROM departments')
        departments = cursor.fetchall()
        
        conn.close()
        
        return render_template('dashboard.html', user=user, departments=departments)
        
    except Exception as e:
        flash(f'获取数据失败: {str(e)}', 'error')
        return redirect(url_for('login'))

@app.route('/search')
def search():
    """搜索功能 - 另一个SQL注入点"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    search_term = request.args.get('q', '')
    results = []
    
    if search_term:
        try:
            db_path = os.path.join(os.path.dirname(__file__), 'hr_management_system.db')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 另一个SQL注入漏洞点
            query = f"SELECT username, real_name, department FROM users WHERE username LIKE '%{search_term}%'"
            print(f"搜索查询: {query}")
            
            # 处理注释符 - 正确截断注释后的内容
            original_query = query

            # 处理 # 注释符
            if '#' in query:
                comment_pos = query.find('#')
                query = query[:comment_pos].rstrip()

            # 处理 -- 注释符（必须后面跟空格）
            if '-- ' in query:
                comment_pos = query.find('-- ')
                query = query[:comment_pos].rstrip()

            if query != original_query:
                print(f"处理注释符后的搜索查询: {query}")

            # 在处理注释符后再检查列数
            # 检查ORDER BY列数错误
            if "order by" in query.lower():
                import re
                order_match = re.search(r'order by (\d+)', query.lower())
                if order_match:
                    col_num = int(order_match.group(1))
                    if col_num > 3:  # 搜索查询只返回3列
                        raise sqlite3.OperationalError("no such column: order by column out of range")

            # 检查UNION列数错误
            if "union select" in query.lower():
                import re
                # 检查处理注释符后的查询
                union_match = re.search(r'union select (.*?)(?:from|$)', query.lower())
                if union_match:
                    columns_str = union_match.group(1).strip()
                    columns = [col.strip() for col in columns_str.split(',')]
                    print(f"搜索检测到UNION SELECT列数: {len(columns)} 列: {columns}")
                    if len(columns) != 3:  # 搜索查询返回3列
                        raise sqlite3.OperationalError("The used SELECT statements have a different number of columns")

            # 替换MySQL函数为SQLite兼容的值（搜索功能）
            mysql_functions = {
                'database()': "'hr_system_db'",
                'version()': "'MySQL 8.0.35-0ubuntu0.20.04.1'",
                'user()': "'hr_admin@localhost'",
                'current_user()': "'hr_admin@localhost'",
                'system_user()': "'hr_admin@localhost'",
                'session_user()': "'hr_admin@localhost'",
                'connection_id()': "12345",
                'schema()': "'hr_system_db'",
                '@@version': "'MySQL 8.0.35-0ubuntu0.20.04.1'",
                '@@hostname': "'hr-server-xiaofeifetech'",
                'now()': "'2024-07-28 10:30:00'",
                'count(*)': "10"
            }

            original_query_for_functions = query
            for func, replacement in mysql_functions.items():
                if func.lower() in query.lower():
                    import re
                    # 使用单词边界确保完整匹配函数名
                    if func.endswith('()'):
                        pattern = re.escape(func[:-2]) + r'\(\)'
                    else:
                        pattern = r'\b' + re.escape(func) + r'\b'
                    query = re.sub(pattern, replacement, query, flags=re.IGNORECASE)

            if query != original_query_for_functions:
                print(f"搜索替换MySQL函数后: {query}")

            # 处理information_schema查询（搜索功能）- 正确的MySQL表名格式
            if "information_schema." in query.lower():
                information_schema_mappings = {
                    'information_schema.tables': 'information_schema_tables',
                    'information_schema.columns': 'information_schema_columns',
                    'information_schema.statistics': 'information_schema_statistics',
                    'information_schema.schemata': 'information_schema_schemata',
                    'information_schema.table_constraints': 'information_schema_table_constraints',
                    'information_schema.key_column_usage': 'information_schema_key_column_usage',
                    'information_schema.views': 'information_schema_views',
                    'information_schema.routines': 'information_schema_routines',
                    'information_schema.triggers': 'information_schema_triggers',
                    'information_schema.processlist': 'information_schema_processlist'
                }

                original_query_for_schema = query
                for mysql_table, sqlite_table in information_schema_mappings.items():
                    if mysql_table.lower() in query.lower():
                        import re
                        # 使用单词边界确保完整匹配表名
                        pattern = r'\b' + re.escape(mysql_table) + r'\b'
                        query = re.sub(pattern, sqlite_table, query, flags=re.IGNORECASE)

                if query != original_query_for_schema:
                    print(f"搜索替换information_schema表名后: {query}")

            cursor.execute(query)
            results = cursor.fetchall()
            conn.close()
            
        except sqlite3.Error as e:
            # 详细的MySQL搜索错误分类系统
            error_msg = str(e).lower()
            print(f"搜索SQLite原始错误: {e}")
            print(f"搜索当前查询: {query}")

            # 错误分类
            error_category = "未知搜索错误"
            mysql_error = ""

            if "order by column out of range" in error_msg:
                error_category = "搜索列数探测错误"
                mysql_error = f"MySQL Error 1054: Unknown column in 'order clause'"
                print(f"🔍 搜索错误类别: {error_category} - ORDER BY列数超出范围(搜索结果只有3列)")

            elif "the used select statements have a different number of columns" in error_msg:
                error_category = "搜索UNION列数不匹配"
                mysql_error = "MySQL Error 1222: The used SELECT statements have a different number of columns"
                print(f"🔍 搜索错误类别: {error_category} - UNION查询列数不匹配(搜索结果需要3列)")

            elif "syntax error" in error_msg or "near" in error_msg:
                error_category = "搜索SQL语法错误"
                mysql_error = f"MySQL Error 1064: You have an error in your SQL syntax near '{search_term}' at line 1"
                print(f"🔍 搜索错误类别: {error_category} - 搜索查询语法错误")

            elif "no such column" in error_msg:
                error_category = "搜索字段不存在"
                mysql_error = f"MySQL Error 1054: Unknown column '{search_term}' in 'where clause'"
                print(f"🔍 搜索错误类别: {error_category} - 搜索中的字段不存在")

            elif "no such function" in error_msg:
                error_category = "搜索函数不支持"
                mysql_error = f"MySQL Error 1305: FUNCTION hr_system_db.unknown does not exist"
                print(f"🔍 搜索错误类别: {error_category} - 搜索中使用了不支持的函数")

            else:
                error_category = "其他搜索错误"
                mysql_error = f"MySQL Error 1064: You have an error in your SQL syntax near '{search_term}' at line 1"
                print(f"🔍 搜索错误类别: {error_category} - 未分类的搜索错误")

            print(f"📊 搜索错误总结:")
            print(f"   - 错误类别: {error_category}")
            print(f"   - MySQL错误码: {mysql_error.split(':')[0] if ':' in mysql_error else 'Unknown'}")
            print(f"   - 错误描述: {mysql_error}")

            flash(f'搜索查询错误: {mysql_error}', 'error')
    
    return render_template('search.html', results=results, search_term=search_term)

@app.route('/logout')
def logout():
    """登出"""
    session.clear()
    flash('已成功登出', 'info')
    return redirect(url_for('login'))

if __name__ == '__main__':
    # 初始化数据库
    init_database()
    
    print("小飞飞科技公司人事管理系统启动中...")
    print("系统版本: XiaoFeiFei HR Management System v3.2.1")
    print("数据库: MySQL 8.0.35")
    print("访问地址: http://localhost:3000")
    print("\n人事部联系方式: <EMAIL>")
    print("技术支持热线: ext.8888")
    print("公司总机: 021-12345678")
    print("\n系统状态: 正常运行")
    print("最后备份时间: 2024-07-27 02:00:00")
    
    app.run(host='0.0.0.0', port=3000, debug=True)
