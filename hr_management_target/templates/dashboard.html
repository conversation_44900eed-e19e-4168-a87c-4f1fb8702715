<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户仪表板 - 企业管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f6fa;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 24px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-info span {
            background: rgba(255,255,255,0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 14px;
            transition: background 0.3s ease;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .user-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
        }
        
        .detail-item label {
            font-weight: 600;
            color: #555;
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .detail-item span {
            color: #333;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        
        .orders-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .orders-table th,
        .orders-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .orders-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }
        
        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status.completed {
            background: #d4edda;
            color: #155724;
        }
        
        .status.pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.shipped {
            background: #cce7ff;
            color: #004085;
        }
        
        .search-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .search-form {
            display: flex;
            gap: 15px;
            align-items: end;
        }
        
        .search-input {
            flex: 1;
        }
        
        .search-input label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        
        .search-input input {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .search-btn {
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .search-btn:hover {
            background: #5a6fd8;
        }
        
        .flash-messages {
            margin-bottom: 20px;
        }
        
        .flash-message {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .flash-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .flash-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .user-details {
                grid-template-columns: 1fr;
            }
            
            .search-form {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>小飞飞科技公司人事管理系统</h1>
            <div class="user-info">
                <span>{{ user[3] if user[3] else session.username }}</span>
                <span>{{ user[4] if user[4] else '员工' }}</span>
                <a href="{{ url_for('logout') }}" class="logout-btn">登出</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="flash-messages">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="flash-message {{ category }}">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>
        
        <div class="dashboard-grid">
            <div class="card">
                <h2>👤 个人信息</h2>
                <div class="user-details">
                    <div class="detail-item">
                        <label>员工ID:</label>
                        <span>{{ user[0] }}</span>
                    </div>
                    <div class="detail-item">
                        <label>用户名:</label>
                        <span>{{ user[1] }}</span>
                    </div>
                    <div class="detail-item">
                        <label>姓名:</label>
                        <span>{{ user[3] }}</span>
                    </div>
                    <div class="detail-item">
                        <label>部门:</label>
                        <span>{{ user[4] }}</span>
                    </div>
                    <div class="detail-item">
                        <label>月薪:</label>
                        <span>¥{{ "%.2f"|format(user[5]) if user[5] else '未设置' }}</span>
                    </div>
                    <div class="detail-item">
                        <label>入职时间:</label>
                        <span>{{ user[6] if user[6] else '未设置' }}</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h2>🏢 部门信息</h2>
                {% if departments %}
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>部门ID</th>
                                <th>部门名称</th>
                                <th>部门经理ID</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for dept in departments %}
                            <tr>
                                <td>{{ dept[0] }}</td>
                                <td>{{ dept[1] }}</td>
                                <td>{{ dept[2] if dept[2] else '待定' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                {% else %}
                    <p>暂无部门信息</p>
                {% endif %}
            </div>
        </div>
        
        <div class="search-section">
            <h2>🔍 员工搜索</h2>
            <form class="search-form" action="{{ url_for('search') }}" method="GET">
                <div class="search-input">
                    <label for="search">搜索员工 (用户名):</label>
                    <input type="text" id="search" name="q" placeholder="输入员工用户名...">
                </div>
                <button type="submit" class="search-btn">搜索</button>
            </form>
            <p style="margin-top: 10px; color: #666; font-size: 14px;">
                💡 提示: 支持模糊搜索，可以输入用户名的部分内容查找员工信息
            </p>
        </div>
    </div>
</body>
</html>
