<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小飞飞科技公司人事管理系统 - 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 5px;
        }
        
        .logo p {
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .flash-messages {
            margin-bottom: 20px;
        }
        
        .flash-message {
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .flash-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .flash-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .flash-message.info {
            background: #cce7ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }
        
        .help-section {
            margin-top: 30px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #17a2b8;
        }

        .help-section h4 {
            color: #17a2b8;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .help-section p {
            font-size: 12px;
            color: #666;
            margin: 5px 0;
        }

        .injection-results {
            margin-top: 30px;
            padding: 20px;
            background: #f0f8ff;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }

        .injection-results h4 {
            color: #007bff;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .results-table-container {
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            background: white;
        }

        .results-table th,
        .results-table td {
            padding: 8px 6px;
            text-align: left;
            border: 1px solid #ddd;
            word-break: break-all;
            max-width: 120px;
        }

        .results-table th {
            background: #007bff;
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .results-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .results-table tr:hover {
            background: #e3f2fd;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>小飞飞科技公司人事管理系统</h1>
            <p>XiaoFeiFei Technology HR Management System</p>
        </div>
        
        <div class="flash-messages">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="flash-message {{ category }}">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>
        
        <form method="POST" action="{{ url_for('login') }}">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" placeholder="请输入用户名" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" placeholder="请输入密码" required>
            </div>
            
            <button type="submit" class="login-btn">登录</button>
        </form>
        
        {% if show_results and injection_results %}
        <div class="injection-results">
            <h4>🔍 查询结果</h4>
            <div class="results-table-container">
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>列1</th>
                            <th>列2</th>
                            <th>列3</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for result in injection_results %}
                        <tr>
                            <td>{{ result.col1 }}</td>
                            <td>{{ result.col2 }}</td>
                            <td>{{ result.col3 }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% else %}
        <div class="help-section">
            <h4>💡 需要帮助？</h4>
            <p>如果您忘记了登录凭据，请联系人事部门或系统管理员</p>
            <p>员工首次登录请使用工号作为用户名</p>
            <p>如有技术问题请联系IT部门：ext.8888</p>
        </div>
        {% endif %}
    </div>
</body>
</html>
