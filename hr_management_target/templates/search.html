<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索结果 - 企业管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f6fa;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 24px;
        }
        
        .nav-links {
            display: flex;
            gap: 20px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            background: rgba(255,255,255,0.2);
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .search-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .search-form {
            display: flex;
            gap: 15px;
            align-items: end;
        }
        
        .search-input {
            flex: 1;
        }
        
        .search-input label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        
        .search-input input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .search-btn {
            padding: 12px 24px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
        }
        
        .search-btn:hover {
            background: #5a6fd8;
        }
        
        .results-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .results-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #667eea;
        }
        
        .results-header h2 {
            color: #333;
            font-size: 20px;
        }
        
        .results-count {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .results-table th,
        .results-table td {
            padding: 15px 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .results-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
            font-size: 14px;
        }
        
        .results-table td {
            color: #333;
        }
        
        .results-table tr:hover {
            background: #f8f9fa;
        }
        
        .role-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .role-badge.admin {
            background: #f8d7da;
            color: #721c24;
        }
        
        .role-badge.manager {
            background: #fff3cd;
            color: #856404;
        }
        
        .role-badge.user {
            background: #d4edda;
            color: #155724;
        }
        
        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .no-results i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #ccc;
        }
        
        .flash-messages {
            margin-bottom: 20px;
        }
        
        .flash-message {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .flash-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .search-tip {
            background: #e7f3ff;
            border: 1px solid #b3d7ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }

        .search-tip h4 {
            color: #004085;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .search-tip p {
            color: #004085;
            margin: 5px 0;
            font-size: 13px;
        }
        
        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
                align-items: stretch;
            }
            
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🔍 员工搜索结果</h1>
            <div class="nav-links">
                <a href="{{ url_for('dashboard') }}">返回仪表板</a>
                <a href="{{ url_for('logout') }}">登出</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="flash-messages">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="flash-message {{ category }}">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>
        
        <div class="search-card">
            <form class="search-form" action="{{ url_for('search') }}" method="GET">
                <div class="search-input">
                    <label for="search">搜索员工:</label>
                    <input type="text" id="search" name="q" value="{{ search_term }}" placeholder="输入员工用户名...">
                </div>
                <button type="submit" class="search-btn">搜索</button>
            </form>

            <div class="search-tip">
                <h4>🔍 搜索帮助</h4>
                <p>支持按员工用户名进行模糊搜索</p>
                <p>输入完整或部分用户名即可查找员工信息</p>
            </div>
        </div>
        
        <div class="results-card">
            <div class="results-header">
                <h2>搜索结果</h2>
                {% if search_term %}
                    <div class="results-count">
                        搜索关键词: "{{ search_term }}" | 找到 {{ results|length }} 条结果
                    </div>
                {% endif %}
            </div>
            
            {% if results %}
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>姓名</th>
                            <th>部门</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for result in results %}
                        <tr>
                            <td>{{ result[0] }}</td>
                            <td>{{ result[1] }}</td>
                            <td>{{ result[2] }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% elif search_term %}
                <div class="no-results">
                    <div style="font-size: 48px; margin-bottom: 15px;">🔍</div>
                    <h3>未找到匹配的结果</h3>
                    <p>尝试使用不同的关键词搜索，或者尝试SQL注入payload</p>
                </div>
            {% else %}
                <div class="no-results">
                    <div style="font-size: 48px; margin-bottom: 15px;">👆</div>
                    <h3>请输入搜索关键词</h3>
                    <p>在上方输入框中输入用户名或邮箱进行搜索</p>
                </div>
            {% endif %}
        </div>
    </div>
</body>
</html>
