@echo off
echo ========================================
echo 小飞飞科技公司人事管理系统
echo XiaoFeiFei HR Management System v3.2.1
echo ========================================
echo.
echo 正在启动系统...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查Flask是否安装
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 正在安装Flask...
    pip install flask
)

REM 启动应用
echo 启动人事管理系统...
echo 访问地址: http://localhost:3000
echo.
echo 按 Ctrl+C 停止服务器
echo.
python app.py

pause
