# Nmap 7.92 scan initiated Tue Aug  5 11:30:30 2025 as: D:\\nmap\\nmap.exe -sS -A -T4 -p1-65535 -v --open -oA full_scan localhost
WARNING: Service 127.0.0.1:11200 had already soft-matched rtsp, but now soft-matched sip; ignoring second value
Nmap scan report for localhost (127.0.0.1)
Host is up (0.00045s latency).
Other addresses for localhost (not scanned): ::1
rDNS record for 127.0.0.1: kubernetes.docker.internal
Not shown: 65490 closed tcp ports (reset), 1 filtered tcp port (no-response)
Some closed ports may be reported as filtered due to --defeat-rst-ratelimit
PORT      STATE SERVICE               VERSION
80/tcp    open  http                  Apache httpd 2.4.39 ((Win64) OpenSSL/1.1.1b mod_fcgid/2.3.9a mod_log_rotate/1.02)
|_http-server-header: Apache/2.4.39 (Win64) OpenSSL/1.1.1b mod_fcgid/2.3.9a mod_log_rotate/1.02
|_http-title: \xE7\xAB\x99\xE7\x82\xB9\xE5\x88\x9B\xE5\xBB\xBA\xE6\x88\x90\xE5\x8A\x9F-phpstudy for windows
| http-methods: 
|   Supported Methods: HEAD GET POST OPTIONS TRACE
|_  Potentially risky methods: TRACE
135/tcp   open  msrpc                 Microsoft Windows RPC
445/tcp   open  microsoft-ds?
902/tcp   open  ssl/vmware-auth       VMware Authentication Daemon 1.10 (Uses VNC, SOAP)
912/tcp   open  vmware-auth           VMware Authentication Daemon 1.0 (Uses VNC, SOAP)
2179/tcp  open  vmrdp?
2869/tcp  open  http                  Microsoft HTTPAPI httpd 2.0 (SSDP/UPnP)
3306/tcp  open  mysql                 MySQL 5.7.26
| mysql-info: 
|   Protocol: 10
|   Version: 5.7.26
|   Thread ID: 4
|   Capabilities flags: 63487
|   Some Capabilities: InteractiveClient, SupportsLoadDataLocal, Speaks41ProtocolOld, LongPassword, ConnectWithDatabase, Support41Auth, SupportsCompression, SupportsTransactions, DontAllowDatabaseTableColumn, IgnoreSigpipes, IgnoreSpaceBeforeParenthesis, Speaks41ProtocolNew, ODBCClient, FoundRows, LongColumnFlag, SupportsMultipleStatments, SupportsAuthPlugins, SupportsMultipleResults
|   Status: Autocommit
|   Salt: \x0DGNX]8k\x19`,b:
| Y\x14Wh\x02K;
|_  Auth Plugin Name: mysql_native_password
3504/tcp  open  http                  Octoshape P2P streaming web service
|_http-title: Site doesn't have a title.
3518/tcp  open  artifact-msg?
| fingerprint-strings: 
|   FourOhFourRequest: 
|     HTTP/1.0 404 Not Found
|     Content-Type: text/plain; charset=utf-8
|     Vary: Origin
|     X-Content-Type-Options: nosniff
|     Date: Tue, 05 Aug 2025 03:31:25 GMT
|     Content-Length: 19
|     page not found
|   GetRequest, HTTPOptions: 
|     HTTP/1.0 404 Not Found
|     Content-Type: text/plain; charset=utf-8
|     Vary: Origin
|     X-Content-Type-Options: nosniff
|     Date: Tue, 05 Aug 2025 03:30:52 GMT
|     Content-Length: 19
|     page not found
|   Kerberos, LDAPSearchReq, RTSPRequest, SIPOptions, SSLSessionReq, TLSSessionReq, TerminalServerCookie: 
|     HTTP/1.1 400 Bad Request
|     Content-Type: text/plain; charset=utf-8
|     Connection: close
|_    Request
3523/tcp  open  http                  Golang net/http server (Go-IPFS json-rpc or InfluxDB API)
| http-methods: 
|_  Supported Methods: GET HEAD POST OPTIONS
|_http-favicon: Unknown favicon MD5: 575ADFEA2D8D1528A37C7863FF2F10BD
|_http-title: Windsurf Chat
3556/tcp  open  sky-transport?
4301/tcp  open  ssl/d-data?
| ssl-cert: Subject: commonName=localhost.ptlogin2.qq.com/organizationName=Shenzhen Tencent Computer Systems Company Limited/stateOrProvinceName=Guangdong Province/countryName=CN
| Subject Alternative Name: DNS:localhost.ptlogin2.qq.com, DNS:localhost.ptlogin2.tencent.com, DNS:localhost.ptlogin2.tenpay.com, DNS:localhost.ptlogin2.weiyun.com
| Issuer: commonName=DigiCert Secure Site OV G2 TLS CN RSA4096 SHA256 2022 CA1/organizationName=DigiCert, Inc./countryName=US
| Public Key type: rsa
| Public Key bits: 2048
| Signature Algorithm: sha256WithRSAEncryption
| Not valid before: 2025-05-16T00:00:00
| Not valid after:  2026-06-16T23:59:59
| MD5:   431b 3ac6 15fc 3330 b0a6 9bc6 485e 4a5a
|_SHA-1: b87f fac8 3152 2e22 2c12 249b f5d7 bd3e 1678 f0bd
4310/tcp  open  mirrtex?
| fingerprint-strings: 
|   FourOhFourRequest: 
|     HTTP/1.1 200 
|     Content-Length: 296
|     z?#M>
|     F7jh
|     cWJy
|   GetRequest: 
|     HTTP/1.1 200 
|     Content-Length: 296
|     y}5H
|     Bar2
|     L\xcf*eI
|     Q&I;"@ C-
|   HTTPOptions: 
|     HTTP/1.1 200 
|     Content-Length: 296
|_    |gHr
4709/tcp  open  ssl/unknown
| fingerprint-strings: 
|   FourOhFourRequest, GetRequest: 
|     HTTP/1.1 400 Bad Request
|     Content-Encoding: 
|     Content-Length: 90
|     Content-Type: application/json
|     {"api_ver":"1.1","call_status":"error","error_code":5,"error_msg":"sign lost","result":{}}
|   GenericLines, RTSPRequest, SIPOptions: 
|     HTTP/1.1 400 Bad Request
|     Content-Encoding:
|   HTTPOptions: 
|     HTTP/1.1 200 OK
|     Access-Control-Allow-Credentials: true
|     Access-Control-Allow-Headers: x-pop-token
|     Access-Control-Allow-Origin: 
|     Content-Encoding: 
|     Content-Length: 90
|     Content-Type: application/json
|_    {"api_ver":"1.1","call_status":"error","error_code":5,"error_msg":"sign lost","result":{}}
|_ssl-date: TLS randomness does not represent time
| ssl-cert: Subject: commonName=localhost.wbridge.wps.cn
| Subject Alternative Name: DNS:localhost.wbridge.wps.cn
| Issuer: commonName=RapidSSL TLS RSA CA G1/organizationName=DigiCert Inc/countryName=US
| Public Key type: rsa
| Public Key bits: 2048
| Signature Algorithm: sha256WithRSAEncryption
| Not valid before: 2024-12-04T00:00:00
| Not valid after:  2025-12-04T23:59:59
| MD5:   85d4 d80e fc59 b31b db8c 8268 8baf 89b7
|_SHA-1: 2752 82b7 662a bbaa 92ab 2a37 4f61 6576 dd35 7f53
5000/tcp  open  upnp?
| fingerprint-strings: 
|   GetRequest: 
|     HTTP/1.1 200 OK
|     Server: Werkzeug/3.1.3 Python/3.12.1
|     Date: Tue, 05 Aug 2025 03:30:47 GMT
|     Content-Type: text/html; charset=utf-8
|     Content-Length: 23462
|     Access-Control-Allow-Origin: *
|     Access-Control-Allow-Headers: Content-Type,Authorization
|     Access-Control-Allow-Methods: GET,PUT,POST,DELETE,OPTIONS
|     Connection: close
|     <!DOCTYPE html>
|     <html lang="zh-CN">
|     <head>
|     <meta charset="UTF-8">
|     <meta name="viewport" content="width=device-width, initial-scale=1.0">
|     <title>
|     </title>
|     <style>
|     body {
|     font-family: Arial, sans-serif;
|     max-width: 800px;
|     margin: 50px auto;
|     padding: 20px;
|     background-color: #f5f5f5;
|     .container {
|     background: white;
|     padding: 30px;
|     border-radius: 10px;
|     box-shadow: 0 2px 10px rgba(0,0,0,0.1);
|   RTSPRequest: 
|     <!DOCTYPE HTML>
|     <html lang="en">
|     <head>
|     <meta charset="utf-8">
|     <title>Error response</title>
|     </head>
|     <body>
|     <h1>Error response</h1>
|     <p>Error code: 400</p>
|     <p>Message: Bad request version ('RTSP/1.0').</p>
|     <p>Error code explanation: 400 - Bad request syntax or unsupported method.</p>
|     </body>
|_    </html>
5040/tcp  open  unknown
5283/tcp  open  unknown
5284/tcp  open  unknown
5354/tcp  open  mdnsresponder?
6188/tcp  open  unknown
6189/tcp  open  unknown
7897/tcp  open  unknown
| fingerprint-strings: 
|   FourOhFourRequest, GetRequest, HTTPOptions: 
|     HTTP/1.0 400 Bad Request
|     Connection: close
|_    Content-Length: 0
8086/tcp  open  http                  Apache httpd 2.4.39 ((Win64) OpenSSL/1.1.1b mod_fcgid/2.3.9a mod_log_rotate/1.02)
|_http-server-header: Apache/2.4.39 (Win64) OpenSSL/1.1.1b mod_fcgid/2.3.9a mod_log_rotate/1.02
|_http-title: \xE7\xAB\x99\xE7\x82\xB9\xE5\x88\x9B\xE5\xBB\xBA\xE6\x88\x90\xE5\x8A\x9F-phpstudy for windows
| http-methods: 
|   Supported Methods: HEAD GET POST OPTIONS TRACE
|_  Potentially risky methods: TRACE
9097/tcp  open  unknown
| fingerprint-strings: 
|   FourOhFourRequest: 
|     HTTP/1.0 404 Not Found
|     Content-Type: text/plain; charset=utf-8
|     Vary: Origin
|     X-Content-Type-Options: nosniff
|     Date: Tue, 05 Aug 2025 03:31:22 GMT
|     Content-Length: 19
|     page not found
|   GenericLines, Help, Kerberos, LDAPSearchReq, LPDString, RTSPRequest, SSLSessionReq, TLSSessionReq, TerminalServerCookie: 
|     HTTP/1.1 400 Bad Request
|     Content-Type: text/plain; charset=utf-8
|     Connection: close
|     Request
|   GetRequest: 
|     HTTP/1.0 401 Unauthorized
|     Content-Type: application/json
|     Vary: Origin
|     Date: Tue, 05 Aug 2025 03:30:57 GMT
|     Content-Length: 27
|     {"message":"Unauthorized"}
|   HTTPOptions: 
|     HTTP/1.0 405 Method Not Allowed
|     Allow: GET
|     Vary: Origin
|     Date: Tue, 05 Aug 2025 03:30:57 GMT
|_    Content-Length: 0
9210/tcp  open  oma-mlp?
| fingerprint-strings: 
|   DNSStatusRequestTCP, DNSVersionBindReqTCP, Help, Kerberos, RPCCheck, SMBProgNeg, SSLSessionReq, TLSSessionReq, TerminalServerCookie, X11Probe: 
|     HTTP/1.1 400 Bad Request
|     Connection: close
|   GetRequest, HTTPOptions, RTSPRequest: 
|     HTTP/1.1 200 OK
|     Content-Type: application/json
|     Date: Tue, 05 Aug 2025 03:30:52 GMT
|     Connection: close
|_    eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlcnJDb2RlIjo0MDAxLCJlcnJNc2ciOiLor7fmsYLmlbDmja7nu5PmnoTplJnor68iLCJwb3J0Ijo5MjEwLCJpc09ubHlHdWlsZCI6ZmFsc2UsImlhdCI6MTc1NDM2NDY1Mn0.WeNvfttf6IOWQTxtiwHeiUF6bAU6MIlYn2xnDTWkNuM
10000/tcp open  ssl/snet-sensor-mgmt?
| ssl-cert: Subject: commonName=localhost.pan.baidu.com/organizationName=BeiJing Baidu Netcom Science Technology Co., Ltd/stateOrProvinceName=\xE5\x8C\x97\xE4\xBA\xAC\xE5\xB8\x82/countryName=CN
| Subject Alternative Name: DNS:localhost.pan.baidu.com
| Issuer: commonName=DigiCert Secure Site Pro G2 TLS CN RSA4096 SHA256 2022 CA1/organizationName=DigiCert, Inc./countryName=US
| Public Key type: rsa
| Public Key bits: 2048
| Signature Algorithm: sha256WithRSAEncryption
| Not valid before: 2025-06-16T00:00:00
| Not valid after:  2026-07-10T23:59:59
| MD5:   940c a7af c0cd 07d1 d626 defd 3f55 ddaf
|_SHA-1: 2f83 4863 b2af 6e0b 2022 ee97 ad28 e1f9 1968 f74e
|_ssl-date: TLS randomness does not represent time
| fingerprint-strings: 
|   DNSVersionBindReqTCP, SMBProgNeg, ms-sql-s, oracle-tns: 
|     HTTP/1.0 400 Bad Request
|     Connection: close
|     Content-Type: text/plain
|     Content-Length: 12
|     Request.
|   FourOhFourRequest, GetRequest: 
|     HTTP/1.1 400 Bad Request
|     Connection: close
|     Content-Type: application/json
|     Content-Length: 27
|_    {"info":"Invalid request!"}
11200/tcp open  rtsp
| fingerprint-strings: 
|   GenericLines: 
|     405 Method not allowed
|     Connection: close
|     Date: Tue, 05 Aug 2025 11:30:41 GMT
|   HTTPOptions: 
|     HTTP/1.0 405 Method not allowed
|     Connection: close
|     Date: Tue, 05 Aug 2025 11:30:52 GMT
|   RTSPRequest: 
|     RTSP/1.0 405 Method not allowed
|     Connection: close
|     Date: Tue, 05 Aug 2025 11:30:57 GMT
|   SIPOptions: 
|     SIP/2.0 405 Method not allowed
|     Connection: close
|_    Date: Tue, 05 Aug 2025 11:31:02 GMT
|_rtsp-methods: ERROR: Script execution failed (use -d to debug)
11434/tcp open  unknown
| fingerprint-strings: 
|   FourOhFourRequest: 
|     HTTP/1.0 404 Not Found
|     Content-Type: text/plain
|     Date: Tue, 05 Aug 2025 03:31:12 GMT
|     Content-Length: 18
|     page not found
|   GenericLines, Help, Kerberos, LDAPSearchReq, LPDString, RTSPRequest, SIPOptions, SSLSessionReq, TLSSessionReq, TerminalServerCookie: 
|     HTTP/1.1 400 Bad Request
|     Content-Type: text/plain; charset=utf-8
|     Connection: close
|     Request
|   GetRequest: 
|     HTTP/1.0 200 OK
|     Content-Type: text/plain; charset=utf-8
|     Date: Tue, 05 Aug 2025 03:30:47 GMT
|     Content-Length: 17
|     Ollama is running
|   HTTPOptions: 
|     HTTP/1.0 204 No Content
|     Allow: HEAD, GET
|_    Date: Tue, 05 Aug 2025 03:30:47 GMT
16422/tcp open  tcpwrapped
20714/tcp open  tcpwrapped
28317/tcp open  unknown
| fingerprint-strings: 
|   FourOhFourRequest: 
|     HTTP/1.1 200 OK
|     Server: MySocket Server
|     Date: Tue, 5 Aug 2025 3:31:45 GMT
|     Access-Control-Allow-Origin: *
|     Access-Control-Allow-Methods: POST, GET, OPTIONS
|     Access-Control-Allow-Private-Network: true
|     Content-Type: text/html; charset=utf-8
|     Content-Length: 23
|     Accept-Ranges: bytes
|     {"ret":1,"version":"3"}
|   GetRequest: 
|     HTTP/1.1 200 OK
|     Server: MySocket Server
|     Date: Tue, 5 Aug 2025 3:30:52 GMT
|     Access-Control-Allow-Origin: *
|     Access-Control-Allow-Methods: POST, GET, OPTIONS
|     Access-Control-Allow-Private-Network: true
|     Content-Type: text/html; charset=utf-8
|     Content-Length: 23
|     Accept-Ranges: bytes
|     {"ret":1,"version":"3"}
|   HTTPOptions, RTSPRequest: 
|     HTTP/1.1 200 OK
|     Server: MySocket Server
|     Date: Tue, 5 Aug 2025 3:30:52 GMT
|     Access-Control-Allow-Origin: *
|     Access-Control-Allow-Methods: POST, GET, OPTIONS
|     Access-Control-Allow-Private-Network: true
|     Content-Type: text/html; charset=utf-8
|     Content-Length: 15
|     Accept-Ranges: bytes
|     {"ret":1,"":""}
|   SIPOptions: 
|     HTTP/1.1 200 OK
|     Server: MySocket Server
|     Date: Tue, 5 Aug 2025 3:32:0 GMT
|     Access-Control-Allow-Origin: *
|     Access-Control-Allow-Methods: POST, GET, OPTIONS
|     Access-Control-Allow-Private-Network: true
|     Content-Type: text/html; charset=utf-8
|     Content-Length: 15
|     Accept-Ranges: bytes
|_    {"ret":1,"":""}
33331/tcp open  diamondport?
| fingerprint-strings: 
|   DNSStatusRequestTCP, DNSVersionBindReqTCP, Help, Kerberos, RPCCheck, RTSPRequest, SMBProgNeg, SSLSessionReq, TLSSessionReq, TerminalServerCookie, X11Probe: 
|     HTTP/1.1 400 Bad Request
|     content-length: 0
|     date: Tue, 05 Aug 2025 03:30:52 GMT
|   FourOhFourRequest, GetRequest, HTTPOptions: 
|     HTTP/1.0 404 Not Found
|     content-length: 0
|_    date: Tue, 05 Aug 2025 03:30:52 GMT
36510/tcp open  unknown
| fingerprint-strings: 
|   FourOhFourRequest: 
|     HTTP/1.0 400 Bad Request
|     Content-Type: text/plain; charset=utf-8
|     Sec-Websocket-Version: 13
|     X-Content-Type-Options: nosniff
|     Date: Tue, 05 Aug 2025 03:31:12 GMT
|     Content-Length: 12
|     Request
|   GenericLines, Help, Kerberos, LPDString, RTSPRequest, SSLSessionReq, TLSSessionReq, TerminalServerCookie: 
|     HTTP/1.1 400 Bad Request
|     Content-Type: text/plain; charset=utf-8
|     Connection: close
|     Request
|   GetRequest, HTTPOptions: 
|     HTTP/1.0 400 Bad Request
|     Content-Type: text/plain; charset=utf-8
|     Sec-Websocket-Version: 13
|     X-Content-Type-Options: nosniff
|     Date: Tue, 05 Aug 2025 03:30:47 GMT
|     Content-Length: 12
|_    Request
37510/tcp open  unknown
| fingerprint-strings: 
|   FourOhFourRequest: 
|     HTTP/1.0 404 Not Found
|     Content-Type: text/plain; charset=utf-8
|     X-Content-Type-Options: nosniff
|     Date: Tue, 05 Aug 2025 03:31:14 GMT
|     Content-Length: 43
|     page not found
|     Powered by Alibaba Cloud
|   GenericLines, Help, Kerberos, LPDString, RTSPRequest, SSLSessionReq, TLSSessionReq, TerminalServerCookie: 
|     HTTP/1.1 400 Bad Request
|     Content-Type: text/plain; charset=utf-8
|     Connection: close
|     Request
|   GetRequest, HTTPOptions: 
|     HTTP/1.0 404 Not Found
|     Content-Type: text/plain; charset=utf-8
|     X-Content-Type-Options: nosniff
|     Date: Tue, 05 Aug 2025 03:30:49 GMT
|     Content-Length: 43
|     page not found
|_    Powered by Alibaba Cloud
49664/tcp open  msrpc                 Microsoft Windows RPC
49665/tcp open  msrpc                 Microsoft Windows RPC
49666/tcp open  msrpc                 Microsoft Windows RPC
49667/tcp open  msrpc                 Microsoft Windows RPC
49668/tcp open  msrpc                 Microsoft Windows RPC
49729/tcp open  msrpc                 Microsoft Windows RPC
50915/tcp open  unknown
| fingerprint-strings: 
|   GenericLines, Help, RTSPRequest: 
|     HTTP/1.1 400 Bad Request
|     Content-Type: text/plain; charset=utf-8
|     Connection: close
|     Request
|   GetRequest: 
|     HTTP/1.0 200 OK
|     Accept-Ranges: bytes
|     Content-Length: 5940
|     Content-Type: text/html; charset=utf-8
|     Date: Tue, 05 Aug 2025 03:30:59 GMT
|     <!doctype html>
|     <html lang="en" style="overflow: hidden">
|     <head>
|     <meta charset="UTF-8" />
|     <link rel="icon" type="image/svg+xml" href="/vite.svg" />
|     <meta name="viewport" content="width=device-width, initial-scale=1.0" />
|     <title>Ollama</title>
|     <script type="module" crossorigin src="/assets/index-Uysc-wCI.js"></script>
|     <link rel="stylesheet" crossorigin href="/assets/index-Cxw75_-Q.css">
|     </head>
|     <body>
|     <div id="root"></div>
|     <script>
|     Initialize webview API object if individual functions are available
|     (typeof window.selectImageFile === "function") {
|     window.webview = {
|     selectImageFile: function () {
|     return new Promise((resolve) => {
|     window
|   HTTPOptions: 
|     HTTP/1.0 200 OK
|     X-Frame-Options: DENY
|     X-Request-Id: 1754364659376965600
|     X-Version: 0.9.6
|     Date: Tue, 05 Aug 2025 03:30:59 GMT
|_    Content-Length: 0
53166/tcp open  unknown
63342/tcp open  http                  PyCharm 2025.1.3.1
|_http-server-header: PyCharm 2025.1.3.1
|_http-title: 404 Not Found
|_http-favicon: Unknown favicon MD5: D9A4C3BB85E108991879486564B92E99
| http-methods: 
|_  Supported Methods: GET HEAD POST OPTIONS
9 services unrecognized despite returning data. If you know the service/version, please submit the following fingerprints at https://nmap.org/cgi-bin/submit.cgi?new-service :
==============NEXT SERVICE FINGERPRINT (SUBMIT INDIVIDUALLY)==============
SF-Port3518-TCP:V=7.92%I=7%D=8/5%Time=68917AEC%P=i686-pc-windows-windows%r
SF:(GetRequest,BE,"HTTP/1\.0\x20404\x20Not\x20Found\r\nContent-Type:\x20te
SF:xt/plain;\x20charset=utf-8\r\nVary:\x20Origin\r\nX-Content-Type-Options
SF::\x20nosniff\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r
SF:\nContent-Length:\x2019\r\n\r\n404\x20page\x20not\x20found\n")%r(HTTPOp
SF:tions,BE,"HTTP/1\.0\x20404\x20Not\x20Found\r\nContent-Type:\x20text/pla
SF:in;\x20charset=utf-8\r\nVary:\x20Origin\r\nX-Content-Type-Options:\x20n
SF:osniff\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\nCont
SF:ent-Length:\x2019\r\n\r\n404\x20page\x20not\x20found\n")%r(RTSPRequest,
SF:67,"HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\
SF:x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request")
SF:%r(SSLSessionReq,67,"HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type
SF::\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x2
SF:0Bad\x20Request")%r(TerminalServerCookie,67,"HTTP/1\.1\x20400\x20Bad\x2
SF:0Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection
SF::\x20close\r\n\r\n400\x20Bad\x20Request")%r(TLSSessionReq,67,"HTTP/1\.1
SF:\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=ut
SF:f-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request")%r(Kerberos,6
SF:7,"HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x
SF:20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request")%
SF:r(FourOhFourRequest,BE,"HTTP/1\.0\x20404\x20Not\x20Found\r\nContent-Typ
SF:e:\x20text/plain;\x20charset=utf-8\r\nVary:\x20Origin\r\nX-Content-Type
SF:-Options:\x20nosniff\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:31:25\
SF:x20GMT\r\nContent-Length:\x2019\r\n\r\n404\x20page\x20not\x20found\n")%
SF:r(LDAPSearchReq,67,"HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:
SF:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20
SF:Bad\x20Request")%r(SIPOptions,67,"HTTP/1\.1\x20400\x20Bad\x20Request\r\
SF:nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\
SF:r\n\r\n400\x20Bad\x20Request");
==============NEXT SERVICE FINGERPRINT (SUBMIT INDIVIDUALLY)==============
SF-Port3556-TCP:V=7.92%I=7%D=8/5%Time=68917AE7%P=i686-pc-windows-windows%r
SF:(NULL,2E,"\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0
SF:\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01")%r(GenericLines,2E,
SF:"\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\
SF:0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01")%r(GetRequest,2E,"\0\0\x18\x
SF:04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0
SF:\0\x04\x08\0\0\0\0\0\0\?\0\x01")%r(HTTPOptions,2E,"\0\0\x18\x04\0\0\0\0
SF:\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08
SF:\0\0\0\0\0\0\?\0\x01")%r(RTSPRequest,2E,"\0\0\x18\x04\0\0\0\0\0\0\x04\0
SF:@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0
SF:\0\?\0\x01")%r(RPCCheck,2E,"\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0
SF:@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01")%
SF:r(DNSVersionBindReqTCP,2E,"\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@
SF:\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01")%r
SF:(DNSStatusRequestTCP,2E,"\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0
SF:\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01")%r(H
SF:elp,2E,"\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\x
SF:fe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01")%r(SSLSessionReq,2E,"
SF:\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0
SF:\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01")%r(TerminalServerCookie,2E,"\
SF:0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\
SF:0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01")%r(TLSSessionReq,2E,"\0\0\x18\
SF:x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\
SF:0\0\x04\x08\0\0\0\0\0\0\?\0\x01")%r(Kerberos,2E,"\0\0\x18\x04\0\0\0\0\0
SF:\0\x04\0@\0\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0
SF:\0\0\0\0\0\?\0\x01")%r(SMBProgNeg,2E,"\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0
SF:\0\0\x05\0@\0\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\
SF:?\0\x01")%r(X11Probe,2E,"\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0
SF:\0\0\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01")%r(F
SF:ourOhFourRequest,2E,"\0\0\x18\x04\0\0\0\0\0\0\x04\0@\0\0\0\x05\0@\0\0\0
SF:\x06\0\0@\0\xfe\x03\0\0\0\x01\0\0\x04\x08\0\0\0\0\0\0\?\0\x01");
==============NEXT SERVICE FINGERPRINT (SUBMIT INDIVIDUALLY)==============
SF-Port4310-TCP:V=7.92%I=7%D=8/5%Time=68917AEC%P=i686-pc-windows-windows%r
SF:(GetRequest,14E,"HTTP/1\.1\x20200\x20\r\nContent-Length:\x20296\r\n\r\n
SF:SE\^\x12\x86R\?\xe7\xed\x0b\x8d\x9a\xd7\x97N\xea\0\0\x01\t\0\0\x01\x10\
SF:xea\xd7\x91\xd6\xb59\x9c\xaf\xe8\xb7KJ\xe8\x12f1\x7f\x03Xe\xa4\xb7\*S\x
SF:fb\x17\xff\xd67\xb1Lj\"\x20U\x0f\x024~\xf0_\x7fB\xd7\x95\x9c\x1e\$\x81m
SF:\x03\xf4xZ\x93\x99\xf0\x84\]\xa2\x80Z\x20\x120\x93!/\x89\xf7\xc0X\xe5\x
SF:0b\xb3/\xca\x1d\x0b-\xc4\xde\xf9\xf0\xc1\xa40`l\x85@\x9a:\x0b\x0e\xd4T\
SF:xd3\xc3y}5H\xcf\*\xce\[>\n\xa1\x85=\x02\xc8\xf4OMq\xb8\x13R}\x18\t\r\x0
SF:7\x95\xb1%\xd8\xfd\xb3Bar2\xd2L\\\xcf\*eI\xcfQ&I;\"@\tC-\xb7s\xce\xd1K\
SF:xed!\x7f\x06\x07\xbb\x9c\x81\xb6\x08\t\xb8s\x1f\x1f\*\xea_\x7f\xfeD\xa7
SF:\x86\x90\xcf\xbb\xacR\xff\xd5bZ\x16\x87\xc4\$\xe3\x0fo\xdd\x01/\xae\xd1
SF:\xff`n\x9a\x04\xe8\xd1\x15\xbf\x8a\x1f\x92\xbc\xab\xab\xea\x08O\x85\xc9
SF:\xc4\x91x\x7f\x9c\x19dS\x98\x15\x89\xa1\x12J\x7fh\xee\x8b2\x7f\x98\xbe\
SF:xcbM\x19\x07\x8e\xad\x03-~\xe4\*%\xbeZ\x89=\x08\$\xc0,h\x1e\x021}\x90\x
SF:b2\x19")%r(HTTPOptions,14E,"HTTP/1\.1\x20200\x20\r\nContent-Length:\x20
SF:296\r\n\r\n\xb6>\x82\xc3\x1b\t\xbc\xe1#\[\x9b\xd6\x90\x08R`\0\0\x01\t\0
SF:\0\x01\x10XV\xc2:7\xc86b\x88r\xb1W\xdcn\xb6\xff\xc7\xbf\x14f2\xefU\xf0\
SF:xf1\x9a\xff0\|\xb4\x10`\xfaxVX\x93\x1c\x80\xac\xf1\xbd\x08w\x20\]\ngN\x
SF:d9\.\x97KU7\x1b\"9q\x1e\x9d\xfd\xae\xf3C\xf2~u\x85\xe9\x17U\x1d\x98\xd3
SF:\xe5\xf8I1s\xb6i\x1d\x1a\xb9\x892\x9d\xf9\\S\x8ad\xc8\$\|\xa6\xf8\xfa\x
SF:9fA\x81;\x12\xb0\|gHr\xcc\xff{\?\r\xf7>\xe97\x9f\x0fv\?e\x9c\x02t\xcc6\
SF:x1d\xf8BsN\xb9\x86\x1a\^\xcc\xb1U8a\xba\xa5\|\xa1\xcf\xe6h\xeb\xabK\xf0
SF:\xfb\x9d\x7f\x9b\xcd\xb2\.\x20\xa3Q\xd0\x13~\xd1\xb9\xe1~\xbc\x9d\xca\x
SF:b4<\x8c\x03\.\x89\*\x16\0\)yc\x9cV\x0c\xe5\xf9\xf1\x14\xb5\xfd\x1c\x9cw
SF:\xf8\xc2\x06\xa9\xcb`\x94\x83lE\x13vw\xb3\x1b\x0ef\x19\xc6\x9d\x83\x14\
SF:x89\x9a\|\xa5-\xac\./\xb1\x13\xe9\xb3\x0e&P7\xa3\xee\xc3\x01X\xc0\x99\x
SF:9b\x14\xf7\xd3\xcd\x14\x08\x9e%\x03\xa7\x91\xb9%\xfe5\x81u_\x08\]\x1e\x
SF:e9\xe3\x01\xe6\xaa\xc8\xc9u")%r(FourOhFourRequest,14E,"HTTP/1\.1\x20200
SF:\x20\r\nContent-Length:\x20296\r\n\r\n\^O\xb9\x1f\xe0y\xcb\x08\xa3\xd0\
SF:)\xder\+1\xb4\0\0\x01\t\0\0\x01\x10\x03\x0e\xea\x9b\[\x95i\x9fss\xd0\xa
SF:5\x93!D\x15\xfd:\x1de\xe3\xc5\x8e\xe8`JB\xf7\xef~4l\xc2\x01\x1f\.\xafp\
SF:xf2Z\xef\x90V\xb14\0P\x84\x02X\xa9P\xe9\x18\.\x97X\xec\xecd}\xbfKH\^\xb
SF:5\xd32MS\xf6p\xcd\xf4\x1az\?#M>\x90\xee\xba\x17O\xdbk\x0bw\xa3\x97\x8e\
SF:xe6\x89,6\xbanD\xc8\xab\x93\xc2OZ\x1aD\x01M\|v\x8eX\x11'\x85\x8dA\xe0&\
SF:xae\x0fk\x03y\x9e\x850\xb3\xf0\xcd\x17\+@\xa9\xee8\x8e\?\x9az\x8f'PD\xb
SF:3w\xb7\x0e\xd1F7jh\xea\xf1\x8c%l\x1ehE\x8b\xa5\x109n\xf8\xf3\x14\xae\x1
SF:0\x10\xb5F\x87\xc5\x13\x88\x07\xd5\xf3\x81n9\x1f\xceo\xf6\x15\x0b\xec8s
SF:\"\xed\t\xc7\+\x85\x17\x076&\xd1OR\x1e,\+Q\x08\xd19\x1em\xa2\x14\xe6\"\
SF:xd2\xff:\x16Q\x08\xbe\xc4\$\xdd\x9cu\xcb\+\x9eB\x17\xc5\xfd\x1cF\xa6g\x
SF:fdd\xabcWJy\x8d\xf2\xeb6\x8c\x88\xa3\x03\xf3\xbcO\xa4\x04\xccao&\x05,\x
SF:87\xcb\xa1");
==============NEXT SERVICE FINGERPRINT (SUBMIT INDIVIDUALLY)==============
SF-Port4709-TCP:V=7.92%T=SSL%I=7%D=8/5%Time=68917AF2%P=i686-pc-windows-win
SF:dows%r(GenericLines,30,"HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-E
SF:ncoding:\x20\r\n\r\n")%r(GetRequest,BE,"HTTP/1\.1\x20400\x20Bad\x20Requ
SF:est\r\nContent-Encoding:\x20\r\nContent-Length:\x2090\r\nContent-Type:\
SF:x20application/json\r\n\r\n{\"api_ver\":\"1\.1\",\"call_status\":\"erro
SF:r\",\"error_code\":5,\"error_msg\":\"sign\x20lost\",\"result\":{}}")%r(
SF:HTTPOptions,127,"HTTP/1\.1\x20200\x20OK\r\nAccess-Control-Allow-Credent
SF:ials:\x20true\r\nAccess-Control-Allow-Headers:\x20x-pop-token\r\nAccess
SF:-Control-Allow-Origin:\x20\r\nContent-Encoding:\x20\r\nContent-Length:\
SF:x2090\r\nContent-Type:\x20application/json\r\n\r\n{\"api_ver\":\"1\.1\"
SF:,\"call_status\":\"error\",\"error_code\":5,\"error_msg\":\"sign\x20los
SF:t\",\"result\":{}}")%r(RTSPRequest,30,"HTTP/1\.1\x20400\x20Bad\x20Reque
SF:st\r\nContent-Encoding:\x20\r\n\r\n")%r(FourOhFourRequest,BE,"HTTP/1\.1
SF:\x20400\x20Bad\x20Request\r\nContent-Encoding:\x20\r\nContent-Length:\x
SF:2090\r\nContent-Type:\x20application/json\r\n\r\n{\"api_ver\":\"1\.1\",
SF:\"call_status\":\"error\",\"error_code\":5,\"error_msg\":\"sign\x20lost
SF:\",\"result\":{}}")%r(SIPOptions,30,"HTTP/1\.1\x20400\x20Bad\x20Request
SF:\r\nContent-Encoding:\x20\r\n\r\n");
==============NEXT SERVICE FINGERPRINT (SUBMIT INDIVIDUALLY)==============
SF-Port5000-TCP:V=7.92%I=7%D=8/5%Time=68917AE7%P=i686-pc-windows-windows%r
SF:(GetRequest,5CEB,"HTTP/1\.1\x20200\x20OK\r\nServer:\x20Werkzeug/3\.1\.3
SF:\x20Python/3\.12\.1\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:47\x
SF:20GMT\r\nContent-Type:\x20text/html;\x20charset=utf-8\r\nContent-Length
SF::\x2023462\r\nAccess-Control-Allow-Origin:\x20\*\r\nAccess-Control-Allo
SF:w-Headers:\x20Content-Type,Authorization\r\nAccess-Control-Allow-Method
SF:s:\x20GET,PUT,POST,DELETE,OPTIONS\r\nConnection:\x20close\r\n\r\n<!DOCT
SF:YPE\x20html>\n<html\x20lang=\"zh-CN\">\n<head>\n\x20\x20\x20\x20<meta\x
SF:20charset=\"UTF-8\">\n\x20\x20\x20\x20<meta\x20name=\"viewport\"\x20con
SF:tent=\"width=device-width,\x20initial-scale=1\.0\">\n\x20\x20\x20\x20<t
SF:itle>\xe6\xb8\x97\xe9\x80\x8f\xe6\xb5\x8b\xe8\xaf\x95\xe6\x99\xba\xe8\x
SF:83\xbd\xe5\x8c\x96\xe6\xa3\x80\xe6\xb5\x8b\xe5\xb7\xa5\xe5\x85\xb7</tit
SF:le>\n\x20\x20\x20\x20<style>\n\x20\x20\x20\x20\x20\x20\x20\x20body\x20{
SF:\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-family:\x20Arial
SF:,\x20sans-serif;\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20max-w
SF:idth:\x20800px;\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin
SF::\x2050px\x20auto;\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20pad
SF:ding:\x2020px;\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20backgro
SF:und-color:\x20#f5f5f5;\n\x20\x20\x20\x20\x20\x20\x20\x20}\n\x20\x20\x20
SF:\x20\x20\x20\x20\x20\.container\x20{\n\x20\x20\x20\x20\x20\x20\x20\x20\
SF:x20\x20\x20\x20background:\x20white;\n\x20\x20\x20\x20\x20\x20\x20\x20\
SF:x20\x20\x20\x20padding:\x2030px;\n\x20\x20\x20\x20\x20\x20\x20\x20\x20\
SF:x20\x20\x20border-radius:\x2010px;\n\x20\x20\x20\x20\x20\x20\x20\x20\x2
SF:0\x20\x20\x20box-shadow:\x200\x202px\x2010px\x20rgba\(0,0,0,0\.1\);\n\x
SF:20\x20\x20\x20\x20")%r(RTSPRequest,16C,"<!DOCTYPE\x20HTML>\n<html\x20la
SF:ng=\"en\">\n\x20\x20\x20\x20<head>\n\x20\x20\x20\x20\x20\x20\x20\x20<me
SF:ta\x20charset=\"utf-8\">\n\x20\x20\x20\x20\x20\x20\x20\x20<title>Error\
SF:x20response</title>\n\x20\x20\x20\x20</head>\n\x20\x20\x20\x20<body>\n\
SF:x20\x20\x20\x20\x20\x20\x20\x20<h1>Error\x20response</h1>\n\x20\x20\x20
SF:\x20\x20\x20\x20\x20<p>Error\x20code:\x20400</p>\n\x20\x20\x20\x20\x20\
SF:x20\x20\x20<p>Message:\x20Bad\x20request\x20version\x20\('RTSP/1\.0'\)\
SF:.</p>\n\x20\x20\x20\x20\x20\x20\x20\x20<p>Error\x20code\x20explanation:
SF:\x20400\x20-\x20Bad\x20request\x20syntax\x20or\x20unsupported\x20method
SF:\.</p>\n\x20\x20\x20\x20</body>\n</html>\n");
==============NEXT SERVICE FINGERPRINT (SUBMIT INDIVIDUALLY)==============
SF-Port7897-TCP:V=7.92%I=7%D=8/5%Time=68917AE7%P=i686-pc-windows-windows%r
SF:(GetRequest,42,"HTTP/1\.0\x20400\x20Bad\x20Request\r\nConnection:\x20cl
SF:ose\r\nContent-Length:\x200\r\n\r\n")%r(HTTPOptions,42,"HTTP/1\.0\x2040
SF:0\x20Bad\x20Request\r\nConnection:\x20close\r\nContent-Length:\x200\r\n
SF:\r\n")%r(FourOhFourRequest,42,"HTTP/1\.0\x20400\x20Bad\x20Request\r\nCo
SF:nnection:\x20close\r\nContent-Length:\x200\r\n\r\n");
==============NEXT SERVICE FINGERPRINT (SUBMIT INDIVIDUALLY)==============
SF-Port9097-TCP:V=7.92%I=7%D=8/5%Time=68917AF1%P=i686-pc-windows-windows%r
SF:(GenericLines,67,"HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x
SF:20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Ba
SF:d\x20Request")%r(GetRequest,9F,"HTTP/1\.0\x20401\x20Unauthorized\r\nCon
SF:tent-Type:\x20application/json\r\nVary:\x20Origin\r\nDate:\x20Tue,\x200
SF:5\x20Aug\x202025\x2003:30:57\x20GMT\r\nContent-Length:\x2027\r\n\r\n{\"
SF:message\":\"Unauthorized\"}\n")%r(HTTPOptions,75,"HTTP/1\.0\x20405\x20M
SF:ethod\x20Not\x20Allowed\r\nAllow:\x20GET\r\nVary:\x20Origin\r\nDate:\x2
SF:0Tue,\x2005\x20Aug\x202025\x2003:30:57\x20GMT\r\nContent-Length:\x200\r
SF:\n\r\n")%r(RTSPRequest,67,"HTTP/1\.1\x20400\x20Bad\x20Request\r\nConten
SF:t-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n
SF:400\x20Bad\x20Request")%r(Help,67,"HTTP/1\.1\x20400\x20Bad\x20Request\r
SF:\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close
SF:\r\n\r\n400\x20Bad\x20Request")%r(SSLSessionReq,67,"HTTP/1\.1\x20400\x2
SF:0Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nCon
SF:nection:\x20close\r\n\r\n400\x20Bad\x20Request")%r(TerminalServerCookie
SF:,67,"HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;
SF:\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request"
SF:)%r(TLSSessionReq,67,"HTTP/1\.1\x20400\x20Bad\x20Request\r\nContent-Typ
SF:e:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\r\n\r\n400\x
SF:20Bad\x20Request")%r(Kerberos,67,"HTTP/1\.1\x20400\x20Bad\x20Request\r\
SF:nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnection:\x20close\
SF:r\n\r\n400\x20Bad\x20Request")%r(FourOhFourRequest,BE,"HTTP/1\.0\x20404
SF:\x20Not\x20Found\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nVa
SF:ry:\x20Origin\r\nX-Content-Type-Options:\x20nosniff\r\nDate:\x20Tue,\x2
SF:005\x20Aug\x202025\x2003:31:22\x20GMT\r\nContent-Length:\x2019\r\n\r\n4
SF:04\x20page\x20not\x20found\n")%r(LPDString,67,"HTTP/1\.1\x20400\x20Bad\
SF:x20Request\r\nContent-Type:\x20text/plain;\x20charset=utf-8\r\nConnecti
SF:on:\x20close\r\n\r\n400\x20Bad\x20Request")%r(LDAPSearchReq,67,"HTTP/1\
SF:.1\x20400\x20Bad\x20Request\r\nContent-Type:\x20text/plain;\x20charset=
SF:utf-8\r\nConnection:\x20close\r\n\r\n400\x20Bad\x20Request");
==============NEXT SERVICE FINGERPRINT (SUBMIT INDIVIDUALLY)==============
SF-Port9210-TCP:V=7.92%I=7%D=8/5%Time=68917AEC%P=i686-pc-windows-windows%r
SF:(GetRequest,143,"HTTP/1\.1\x20200\x20OK\r\nContent-Type:\x20application
SF:/json\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\nConne
SF:ction:\x20close\r\n\r\neyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\.eyJlcnJDb2
SF:RlIjo0MDAxLCJlcnJNc2ciOiLor7fmsYLmlbDmja7nu5PmnoTplJnor68iLCJwb3J0Ijo5M
SF:jEwLCJpc09ubHlHdWlsZCI6ZmFsc2UsImlhdCI6MTc1NDM2NDY1Mn0\.WeNvfttf6IOWQTx
SF:tiwHeiUF6bAU6MIlYn2xnDTWkNuM")%r(HTTPOptions,143,"HTTP/1\.1\x20200\x20O
SF:K\r\nContent-Type:\x20application/json\r\nDate:\x20Tue,\x2005\x20Aug\x2
SF:02025\x2003:30:52\x20GMT\r\nConnection:\x20close\r\n\r\neyJhbGciOiJIUzI
SF:1NiIsInR5cCI6IkpXVCJ9\.eyJlcnJDb2RlIjo0MDAxLCJlcnJNc2ciOiLor7fmsYLmlbDm
SF:ja7nu5PmnoTplJnor68iLCJwb3J0Ijo5MjEwLCJpc09ubHlHdWlsZCI6ZmFsc2UsImlhdCI
SF:6MTc1NDM2NDY1Mn0\.WeNvfttf6IOWQTxtiwHeiUF6bAU6MIlYn2xnDTWkNuM")%r(RTSPR
SF:equest,143,"HTTP/1\.1\x20200\x20OK\r\nContent-Type:\x20application/json
SF:\r\nDate:\x20Tue,\x2005\x20Aug\x202025\x2003:30:52\x20GMT\r\nConnection
SF::\x20close\r\n\r\neyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\.eyJlcnJDb2RlIjo
SF:0MDAxLCJlcnJNc2ciOiLor7fmsYLmlbDmja7nu5PmnoTplJnor68iLCJwb3J0Ijo5MjEwLC
SF:Jpc09ubHlHdWlsZCI6ZmFsc2UsImlhdCI6MTc1NDM2NDY1Mn0\.WeNvfttf6IOWQTxtiwHe
SF:iUF6bAU6MIlYn2xnDTWkNuM")%r(RPCCheck,2F,"HTTP/1\.1\x20400\x20Bad\x20Req
SF:uest\r\nConnection:\x20close\r\n\r\n")%r(DNSVersionBindReqTCP,2F,"HTTP/
SF:1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\n\r\n")%r(DNSSt
SF:atusRequestTCP,2F,"HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x2
SF:0close\r\n\r\n")%r(Help,2F,"HTTP/1\.1\x20400\x20Bad\x20Request\r\nConne
SF:ction:\x20close\r\n\r\n")%r(SSLSessionReq,2F,"HTTP/1\.1\x20400\x20Bad\x
SF:20Request\r\nConnection:\x20close\r\n\r\n")%r(TerminalServerCookie,2F,"
SF:HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\n\r\n")%r(
SF:TLSSessionReq,2F,"HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection:\x20
SF:close\r\n\r\n")%r(Kerberos,2F,"HTTP/1\.1\x20400\x20Bad\x20Request\r\nCo
SF:nnection:\x20close\r\n\r\n")%r(SMBProgNeg,2F,"HTTP/1\.1\x20400\x20Bad\x
SF:20Request\r\nConnection:\x20close\r\n\r\n")%r(X11Probe,2F,"HTTP/1\.1\x2
SF:0400\x20Bad\x20Request\r\nConnection:\x20close\r\n\r\n");
==============NEXT SERVICE FINGERPRINT (SUBMIT INDIVIDUALLY)==============
SF-Port10000-TCP:V=7.92%T=SSL%I=7%D=8/5%Time=68917AF3%P=i686-pc-windows-wi
SF:ndows%r(GetRequest,7E,"HTTP/1\.1\x20400\x20Bad\x20Request\r\nConnection
SF::\x20close\r\nContent-Type:\x20application/json\r\nContent-Length:\x202
SF:7\r\n\r\n{\"info\":\"Invalid\x20request!\"}")%r(DNSVersionBindReqTCP,69
SF:,"HTTP/1\.0\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\nContent
SF:-Type:\x20text/plain\r\nContent-Length:\x2012\r\n\r\nBad\x20Request\.")
SF:%r(SMBProgNeg,69,"HTTP/1\.0\x20400\x20Bad\x20Request\r\nConnection:\x20
SF:close\r\nContent-Type:\x20text/plain\r\nContent-Length:\x2012\r\n\r\nBa
SF:d\x20Request\.")%r(FourOhFourRequest,7E,"HTTP/1\.1\x20400\x20Bad\x20Req
SF:uest\r\nConnection:\x20close\r\nContent-Type:\x20application/json\r\nCo
SF:ntent-Length:\x2027\r\n\r\n{\"info\":\"Invalid\x20request!\"}")%r(oracl
SF:e-tns,69,"HTTP/1\.0\x20400\x20Bad\x20Request\r\nConnection:\x20close\r\
SF:nContent-Type:\x20text/plain\r\nContent-Length:\x2012\r\n\r\nBad\x20Req
SF:uest\.")%r(ms-sql-s,69,"HTTP/1\.0\x20400\x20Bad\x20Request\r\nConnectio
SF:n:\x20close\r\nContent-Type:\x20text/plain\r\nContent-Length:\x2012\r\n
SF:\r\nBad\x20Request\.");
No exact OS matches for host (If you know what OS is running on it, see https://nmap.org/submit/ ).
TCP/IP fingerprint:
OS:SCAN(V=7.92%E=4%D=8/5%OT=80%CT=1%CU=39367%PV=N%DS=0%DC=L%G=Y%TM=68917BFD
OS:%P=i686-pc-windows-windows)SEQ(SP=102%GCD=1%ISR=105%CI=I%II=I%TS=A)OPS(O
OS:1=MFFD7NW8ST11%O2=MFFD7NW8ST11%O3=MFFD7NW8NNT11%O4=MFFD7NW8ST11%O5=MFFD7
OS:NW8ST11%O6=MFFD7ST11)WIN(W1=FFFF%W2=FFFF%W3=FFFF%W4=FFFF%W5=FFFF%W6=FFFF
OS:)ECN(R=Y%DF=Y%T=80%W=FFFF%O=MFFD7NW8NNS%CC=N%Q=)T1(R=Y%DF=Y%T=80%S=O%A=S
OS:+%F=AS%RD=0%Q=)T2(R=Y%DF=Y%T=80%W=0%S=Z%A=S%F=AR%O=%RD=0%Q=)T3(R=Y%DF=Y%
OS:T=80%W=0%S=Z%A=O%F=AR%O=%RD=0%Q=)T4(R=Y%DF=Y%T=80%W=0%S=A%A=O%F=R%O=%RD=
OS:0%Q=)T5(R=Y%DF=Y%T=80%W=0%S=Z%A=S+%F=AR%O=%RD=0%Q=)T6(R=Y%DF=Y%T=80%W=0%
OS:S=A%A=O%F=R%O=%RD=0%Q=)T7(R=Y%DF=Y%T=80%W=0%S=Z%A=S+%F=AR%O=%RD=0%Q=)U1(
OS:R=Y%DF=N%T=80%IPL=164%UN=0%RIPL=G%RID=G%RIPCK=Z%RUCK=G%RUD=G)IE(R=Y%DFI=
OS:N%T=80%CD=Z)

Uptime guess: 0.097 days (since Tue Aug  5 09:16:02 2025)
Network Distance: 0 hops
TCP Sequence Prediction: Difficulty=258 (Good luck!)
IP ID Sequence Generation: Busy server or unknown class
Service Info: OS: Windows; CPE: cpe:/o:microsoft:windows

Host script results:
| smb2-time: 
|   date: 2025-08-05T03:33:41
|_  start_date: N/A
| smb2-security-mode: 
|   3.1.1: 
|_    Message signing enabled but not required

Read data files from: D:\nmap
OS and Service detection performed. Please report any incorrect results at https://nmap.org/submit/ .
# Nmap done at Tue Aug  5 11:35:25 2025 -- 1 IP address (1 host up) scanned in 295.71 seconds
